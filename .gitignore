# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
node_modules
.pnp
.pnp.js
.npmrc

# Local env files
.env
.env.local
.env.development.local
.env.preview.local
.env.test.local
.env.production.local

# Testing
coverage

# Turbo
.turbo

# Vercel
.vercel

# Build Outputs
.next/
out/
build
dist
**/*.tsbuildinfo


# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Storybook
**/build-storybook.log
storybook-static/

# Misc
.DS_Store
*.pem

# Devenv
.devenv*
devenv.local.nix

# direnv
.direnv

# pre-commit
.pre-commit-config.yaml

# Devenv
.devenv*
devenv.local.nix

# direnv
.direnv

# pre-commit
.pre-commit-config.yaml
.aider*

# cline
memory-bank/*
.clinerules/user-memory-bank.md

# playwright
**/playwright-report/
**/playwright-results/
.projectile-cache.eld
/apps/playwright/results
/apps/web/test-results

# LazyVim
.lazy.lua
.env*.local

# Bootstrap script
.bootstrap_progress

# user-specific claude stuff
CLAUDE.local.md
.claude/settings.local.json
