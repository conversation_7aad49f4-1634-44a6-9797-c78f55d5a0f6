# Icon System Documentation

This document outlines how to use icons in the wx-next project through the centralized `@repo/icons` package.

## Overview

The wx-next project uses a centralized icon system where all icons are managed through the `@repo/icons` package. **SVGs cannot be imported directly in Next.js applications** - all icons must come from the icons package.

The system follows a two-pronged approach:

1. **Weather-Specific Icons (WxIcon)**: Dynamic weather condition icons based on icon codes
2. **General UI Icons (Direct Import)**: Static interface icons imported directly from organized categories

## Important: No Direct SVG Imports

Unlike traditional Next.js setups, this project does **not** support direct SVG imports with webpack loaders. All SVG handling is done at build-time through the icons package generation system.

```tsx
// ❌ This will NOT work - SVGs are treated as images by Next.js
import MyIcon from './my-icon.svg';

// ✅ This is the correct approach - use the icons package
import { UserAvatar } from '@repo/icons/Avatar';
```

## Weather-Specific Icons (WxIcon)

For dynamic weather condition icons that change based on weather data, use the `WxIcon` component:

```tsx
import { WxIcon } from '@repo/icons/WxIcon';
import { NoData } from '@repo/icons/Data';

// Basic usage with icon code
<WxIcon iconCode={32} size="lg" />

// With alert extension for specific conditions
<WxIcon
  iconCode={32}
  iconCodeExtend={alertCode}
  size="md"
  iconTheme="light"
  className="weather-icon"
  title="Sunny weather"
  desc="Clear skies with sunshine"
/>

// Fallback for missing data
{iconCode ? (
  <WxIcon iconCode={iconCode} size="lg" />
) : (
  <NoData className="h-8 w-8" />
)}
```

### WxIcon Props

- `iconCode: number` - The primary weather condition code
- `iconCodeExtend?: number` - Extended code for alert-related variations
- `size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl'` - Predefined sizes
- `iconTheme?: 'lightBG' | 'darkBG' | 'white' | 'panther'` - Color themes for different backgrounds
- `className?: string` - Additional CSS classes
- `title?: string` - Accessibility title
- `desc?: string` - Accessibility description

## General UI Icons (Direct Import)

For static interface icons, import specific components directly from their categories:

```tsx
// Import specific icons from categories
import { UserAvatar } from '@repo/icons/Avatar';
import { ChevronRight } from '@repo/icons/Navigation';
import { Settings } from '@repo/icons/Interface';
import { Humidity, Wind } from '@repo/icons/Weather';

// Standard usage
<UserAvatar className="h-8 w-8" />
<ChevronRight width={16} height={16} title="Navigate forward" />
<Settings
  className="text-gray-600 hover:text-gray-800"
  desc="Open settings menu"
/>
```

### Available Categories

Icons are organized by functional categories:

- `Avatar/` - User avatars and profile icons
- `Navigation/` - Arrows, chevrons, navigation elements
- `Interface/` - Settings, search, close, general UI elements
- `Data/` - NoData, loading, error states
- `Weather/` - UI elements for weather data (humidity, wind, etc.)
- `Status/` - Status indicators and alerts

## Icon Properties

All icons accept standard React and SVG props:

- `className?: string` - CSS classes (commonly used with Tailwind)
- `width?: string | number` - Icon width
- `height?: string | number` - Icon height
- `title?: string` - Accessibility title
- `desc?: string` - Accessibility description
- Standard SVG attributes (`viewBox`, `fill`, etc.)

## Usage Examples

### Weather Component

```tsx
import { WxIcon } from '@repo/icons/WxIcon';
import { NoData } from '@repo/icons/Data';
import { Humidity, Wind } from '@repo/icons/Weather';

export const WeatherDisplay = ({ iconCode, temperature, humidity, windSpeed }) => {
  return (
    <div className="weather-display">
      {/* Dynamic weather icon */}
      <div className="weather-icon">
        {iconCode ? (
          <WxIcon
            iconCode={iconCode}
            size="xl"
            iconTheme="auto"
            title={`Weather condition`}
          />
        ) : (
          <NoData className="h-16 w-16 text-gray-400" />
        )}
      </div>

      {/* Static UI icons for weather data */}
      <div className="weather-details">
        <div className="flex items-center">
          <Humidity className="h-5 w-5 mr-2" />
          <span>{humidity}%</span>
        </div>
        <div className="flex items-center">
          <Wind className="h-5 w-5 mr-2" />
          <span>{windSpeed} mph</span>
        </div>
      </div>
    </div>
  );
};
```

### Navigation Component

```tsx
import { ChevronLeft, ChevronRight } from '@repo/icons/Navigation';
import { Settings } from '@repo/icons/Interface';

export const NavigationHeader = () => {
  return (
    <header className="navigation-header">
      <button className="nav-button">
        <ChevronLeft className="h-5 w-5" title="Go back" />
      </button>

      <h1 className="page-title">Weather Dashboard</h1>

      <div className="header-actions">
        <button className="nav-button">
          <Settings className="h-5 w-5" title="Open settings" />
        </button>
        <button className="nav-button">
          <ChevronRight className="h-5 w-5" title="Next page" />
        </button>
      </div>
    </header>
  );
};
```

## Adding New Icons

To add new icons to the system:

1. **Add SVG files** to the appropriate category directory in `packages/icons/src/svg/[category]/`
2. **Run the generation script**:
   ```bash
   cd packages/icons
   pnpm build
   # or for development with file watching
   pnpm dev
   ```
3. **Import and use** the generated React component

### Icon Generation Process

The build process:

1. Scans `packages/icons/src/svg/` for SVG files organized by category
2. Optimizes SVGs using SVGO with category-specific configurations
3. Converts optimized SVGs to React components using SVGR
4. Wraps components in a consistent `BaseIcon` wrapper
5. Generates TypeScript components in `packages/icons/src/components/`
6. Creates category index files for easy importing

## Best Practices

### Choosing the Right Approach

1. **Use WxIcon for**:
   - Dynamic weather condition icons
   - Icons that change based on weather data
   - Icons that need theme support

2. **Use Direct Imports for**:
   - Static interface elements
   - Navigation icons
   - User interface components
   - Icons that don't change based on data

### Accessibility

Always provide accessibility attributes:

```tsx
// Good: Descriptive title and description
<Humidity
  className="h-6 w-6"
  title="Humidity Level"
  desc="Current humidity percentage"
/>

// Good: Decorative icons can be hidden
<ChevronRight
  className="h-4 w-4"
  aria-hidden="true"
/>

// Good: Weather icons with context
<WxIcon
  iconCode={32}
  size="lg"
  title="Current weather condition"
  desc="Sunny skies with clear visibility"
/>
```

### Performance

- **Tree-shaking**: Direct imports enable optimal bundle sizes
- **Build-time optimization**: SVGs are optimized during the build process
- **Consistent sizing**: Use Tailwind classes for consistent icon sizing

### Styling

```tsx
// Size with Tailwind classes
<UserAvatar className="h-8 w-8" />

// Color with Tailwind classes
<Settings className="text-gray-600 hover:text-gray-800" />

// Custom styling
<Wind
  className="h-6 w-6 text-blue-500 transition-colors hover:text-blue-700"
/>
```

## Examples and Testing

See the complete icon system in action at:
- `apps/web/app/(web)/[code]/test/icons/page.tsx` - Comprehensive examples
- Storybook icon gallery - Visual documentation of all available icons

## Migration from Direct SVG Imports

If you have existing code that imports SVGs directly:

```tsx
// ❌ Old approach (no longer works)
import MyIcon from './my-icon.svg';

// ✅ New approach - find equivalent in icons package
import { EquivalentIcon } from '@repo/icons/CategoryName';
```

If you need a new icon that doesn't exist in the package, add it to the appropriate category in `packages/icons/src/svg/` and regenerate the components.

## Troubleshooting

### Common Issues

1. **"Cannot resolve SVG import"**
   - SVGs cannot be imported directly in Next.js apps
   - Use the `@repo/icons` package instead

2. **"Icon not found in category"**
   - Check if the icon exists in `packages/icons/src/components/[category]/`
   - If missing, add the SVG to `packages/icons/src/svg/[category]/` and run `pnpm build`

3. **"Icon not displaying correctly"**
   - Ensure you're using the correct import path
   - Check that the icon component is properly exported in the category index

### Getting Help

1. Check existing icons in the Storybook gallery
2. Review the test page at `/test/icons` for usage examples
3. Examine the icon generation script in `packages/icons/scripts/generate-icons.mjs`
4. Consult the system patterns documentation in `.clinerules/05-system-patterns/icon-system.md`
