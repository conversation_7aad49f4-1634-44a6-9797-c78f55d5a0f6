# Technical Context

## Technology Stack

### Core Technologies

- **Next.js 15.2.0**: Server-side rendering and routing framework
- **React 19.1.0**: Component-based UI development
- **TypeScript 5.8.3**: Type-safe JavaScript development
- **MongoDB**: Document database for content and data storage
- **PayloadCMS 3.35.1**: Headless content management system
- **DAL Package**: Domain-oriented Data Access Layer with transformation utilities
  - Organized by business domains (weather, locations, content)
  - Type-safe interfaces for API interactions
  - Response transformation utilities for compact data formats
  - Consistent error handling with tryCatch pattern

### Frontend Technologies

- **TailwindCSS 4.0.14**: Utility-first CSS framework
- **Radix UI**: Unstyled, accessible component primitives
- **Lucide React 0.503.0**: Icon library (for icons not supported by our own icons package)
- **next-themes 0.4.6**: Theme management for Next.js
- **SWR 2.3.3**: React hooks for data fetching with stale-while-revalidate caching strategy
- **Jotai 2.12.2**: Lightweight state management with atomic approach

### Design System

#### Color Palette

The project uses a custom Tailwind color palette with an animal-themed naming convention:

- **Animal-themed color families**: Each color family is named after an animal (e.g., Whale, Flamingo, Lobster)
- **Consistent shade structure**: Brand colors (Whale) have 4 shades (100-400), other colors have 9 shades (100-900)
- **Tailwind compatibility**: Colors are mapped to existing Tailwind color families to ensure only approved colors are used
- **Documentation**: Comprehensive documentation available in `packages/ui/src/tailwind/TAILWIND_COLORS.md`

The color system is implemented using CSS custom properties and is showcased in Storybook for visual reference.

#### Typography System

The project uses a structured typography system with consistent font sizes, line heights, and letter spacing:

- **Semantic text variants**: Text styles are organized into semantic categories (Display, Title, Body, Caption)
- **Size variations**: Each category includes multiple size variants (XL, L, M, S)
- **Consistent scales**: Standardized scales for font sizes, line heights, and letter spacing
- **Text component**: A flexible Text component that implements the typography system with TypeScript support
- **Documentation**: Comprehensive documentation available in `packages/ui/src/tailwind/TAILWIND_TYPOGRAPHY.md`

The typography system is implemented using CSS custom properties and Tailwind utility classes, with a focus on accessibility and semantic HTML.

### Development Tools

- **pnpm 10.10.0**: Package manager with workspace support
- **Turborepo 2.4.4**: Monorepo task runner and build system
- **Vitest 3.0.7**: Fast testing framework for unit tests
- **Playwright 1.42.1**: End-to-end testing framework
- **ESLint 9.21.0**: JavaScript/TypeScript linting
- **Prettier 3.5.3**: Code formatting
- **Husky 9.1.7 & lint-staged 15.5.1**: Git hooks for code quality

## Development Environment

### System Requirements

- Node.js 22+
- pnpm 10+
- MongoDB 8.0

### Environment Setup

#### Standard Setup

```sh
# Setup Node.js via fnm
brew install fnm

# Setup MongoDB
brew tap mongodb/brew
brew install mongodb-community@8.0

# Setup pnpm via corepack
npm install --global corepack@latest
corepack enable pnpm
corepack up
```

#### Nix Setup (Alternative)

```sh
# Install Nix
curl --proto '=https' --tlsv1.2 -sSf -L https://install.determinate.systems/nix | sh -s -- install

# Install direnv
brew install direnv

# Hook direnv in .zshrc
echo 'eval "$(direnv hook zsh)"' >> ~/.zshrc
source ~/.zshrc
```

### Project Setup

```sh
# Clone repository
<NAME_EMAIL>:TheWeatherCompany/wx-next.git
cd wx-next

# Install dependencies
pnpm install

# Link to Vercel project (for environment variables)
pnpm --filter web vercel:link
pnpm --filter web vercel:env:pull

# Seed the database
pnpm --filter web payload:migrate:fresh
```

## Development Workflow

### Common Commands

```sh
# Run dev command for all workspaces
pnpm dev

# Build for all workspaces
pnpm build

# Run tests across all workspaces
pnpm test

# Lint all workspaces
pnpm lint

# Format all files in workspaces
pnpm format

# Type checking for all workspaces
pnpm check-types
```

### Workspace-specific Commands

```sh
# Run commands for specific workspaces
pnpm --filter <workspace> <command>

# Examples:
pnpm --filter web dev
pnpm --filter web check-types
pnpm --filter web lint
pnpm --filter web test
```


## Deployment

### CI/CD Pipeline

- GitHub Actions for CI/CD
- Vercel for deployment
- Pull request previews
- Production deployments from main branch

### CI Checks

- Type checking
- Linting
- Tests
- Build
- Checkmarx security scan
- Vercel preview comments
- Vercel deployments

## External Services

### APIs

- Weather data APIs
  - SUN API (indicated by domain api.weather.com)
- Content APIs
	- Kalliope (Drupal based JSONAPI, indicated by domain cms.weather.com)
	- DSX API (indicated by domain dsx.weather.com)
- Profile API
	- UPSx (indicated by upsx.weather.com)

### Third-party Services

- Vercel for hosting
- MongoDB for database
- AWS S3 for file storage
- SMTP server for email

## PayloadCMS

### Admin Access

- Local admin available at `/payload/admin`
- Authentication via email/password
- Role-based access control

### Content Management

- Collections: Users, Tenants, Pages, Articles, Images, Tags, Liveblogs, Videos
- Rich text editing via Lexical
- Media management
- Multi-tenant support

### Database Management

```sh
# Seed the database with initial data
pnpm --filter web payload:migrate:fresh

# Generate TypeScript types from Payload config
pnpm --filter web generate:types
```

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- No explicit IE11 support

## Testing Technologies

### Unit Testing

- **Vitest**: Fast, ESM-native testing framework for JavaScript/TypeScript
- Focused on unit and integration tests
- Compatible with Jest API for easy migration
- Test files use `.test.ts|tsx|mts|js|jsx|mjs` extensions
- Located near the files being tested

### End-to-End Testing

- **Playwright 1.42.1**: Modern browser automation framework
- Cross-browser testing (Chromium, Firefox, WebKit)
- Supports headless and headed browser testing
- Test files use `.spec.ts` extension in the `app` directory
- Integrated with GitHub Actions for CI/CD
- Browser caching for optimized performance in CI
- Automatically runs against Vercel preview deployments
- Reports results via GitHub Checks API

### Testing Commands

```sh
# Unit Tests
pnpm test                    # Run all unit tests
pnpm --filter web test      # Run unit tests for web package
pnpm test -- -t "pattern"   # Run tests matching pattern

# End-to-End Tests
pnpm --filter web test:e2e:setup  # Install Playwright browsers
pnpm --filter web test:e2e        # Run Playwright tests
pnpm --filter web test:e2e:ui     # Run tests with Playwright UI
pnpm --filter web test:e2e:report # View HTML test report
```

## Performance Considerations

- Server-side rendering for initial page load
- Client-side data fetching for dynamic content
- Optimized image loading via Next.js Image component
- Turborepo caching for faster builds
- Atom-based state management for efficient UI updates
- Browser caching in CI for faster Playwright test runs

## Security Considerations

- Authentication via PayloadCMS
- Role-based access control
- Environment variable management via Vercel
- Checkmarx security scanning in CI pipeline
