# Avoiding Barrel Files in Next.js

Preventing unintended client-side imports by avoiding barrel files and using direct imports instead.

## Key Components

1. **Direct Imports**
   ```typescript
   // ✅ Good: Direct imports
   import { Button } from "@/components/Button";
   import { Card } from "@/components/Card";

   // 🚫 Bad: Barrel file import
   import { <PERSON><PERSON>, <PERSON> } from "@/components";
   ```

   Direct imports ensure only the specific modules you need are included in the bundle.

2. **Server-Only Package**
   ```typescript
   import "server-only";

   export async function getDatabaseData() {
     // This function will throw if imported on the client
     const db = await connectToDatabase();
     return db.query();
   }
   ```

   The `server-only` package prevents server code from being accidentally used on the client.

3. **Clear Code Separation**
   ```typescript
   src/
     server/
       db/
         index.ts  // includes 'server-only'
       actions/
         userActions.ts  // includes 'use server'
     components/
       Button.tsx
       Card.tsx
   ```

   Organize code to clearly separate server and client concerns.

## Benefits

- **Bundle Size Optimization**: Only imports what's actually used
- **Security**: Prevents server-only code from leaking to client bundles
- **Performance**: Reduces client bundle size and improves load times
- **Type Safety**: TypeScript can better track dependencies
- **Maintainability**: Clear dependency relationships

## Example Implementation

```typescript
// 🚫 Problematic barrel file (index.ts)
export * from "./UserService";
export * from "./DatabaseClient";
export * from "./AuthUtils";
export * from "./UIComponents";

// When you import from this barrel file on the client:
import { Button } from "@/components";
// The entire barrel file gets included, even server-only utilities

// ✅ Better approach - Direct imports
import { Button } from "@/components/Button";
import { Card } from "@/components/Card";

// ✅ Server-side file with protection
import "server-only";
import { sql } from "@vercel/postgres";

export async function queryDatabase() {
  const result = await sql`SELECT * FROM users`;
  return result;
}

// ✅ Type safety for server-only functions
type ServerOnly<T> = T & {
  __server_only?: never;
};

export const serverFunction: ServerOnly<() => Promise<void>> = async () => {
  // Server-side operations
};
```

## Important Notes

- **Always use direct imports** rather than barrel files in Next.js applications
- **Use `server-only` or `'use server'` directives** in files that should never run on the client
- **Maintain clear separation** between server and client code
- **Leverage TypeScript** to help catch server/client separation issues
- **Follow Next.js conventions** for server components and actions
- This approach ensures better performance, security, and maintainability
