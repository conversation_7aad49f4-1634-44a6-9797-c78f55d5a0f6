# System Architecture

## System Architecture

### Monorepo Structure

- pnpm workspace-based monorepo
- Turborepo for task orchestration and caching
- Organized into apps/ and packages/ directories

### Application Architecture

- Next.js web application with PayloadCMS integration
- MongoDB database for content and data storage
- Domain-oriented Data Access Layer (DAL)
- Shared UI component library

## Component Relationships

### Application Dependencies

- Web app depends on DAL and UI packages
- Shared configuration through typescript-config and eslint-config packages
- Clear dependency hierarchy managed by Turborepo

### Data Flow

- UI components consume data from DAL
- DAL abstracts external API calls
- Type definitions ensure consistency across layers

### Integration Points

- PayloadCMS integration with Next.js
- MongoDB connection for data persistence
- External weather API integration through DAL
