# System Patterns

This directory contains the system patterns and architectural decisions for the wx-next project. The patterns are organized into individual files for ease of reading and developer maintenance.

## Pattern Documentation Guidelines

When documenting patterns in `.clinerules/04-system-patterns/[pattern-name].md`.

If coming across a new pattern that is not documented, suggest creating a new pattern file as part of the plan before doing work.

````markdown
# {Pattern Name}

Brief description of what this pattern accomplishes and when to use it.

## Key Components

1. **{Component Name}**
   ```typescript
   // Code example
   ```

   Description of the component's purpose

1. **{Another Component}**

   ```typescript
   // Another example
   ```

## Benefits

- List of benefits
- Why this pattern is useful
- Problems it solves

## Example Implementation

```typescript
// Complete working example
```

## Important Notes

- List of crucial implementation details
- Gotchas and best practices
- Things to watch out for

````

Guidelines for Pattern Documentation:
- Place patterns in `.clinerules/04-system-patterns/`
- Update the `File Organization` in `.clinerules/00-README.md` in table format
with name of pattern, summary and link
- Use kebab-case for filenames
- Include working TypeScript examples
- Document all key components separately
- List concrete benefits
- Provide a complete implementation example
- Include important notes and gotchas
- Link to official documentation when relevant

## File Organization

| Pattern Name | Summary | Link |
|--------------|---------|------|
| System Architecture | Monorepo structure, application architecture, and component relationships | [01-architecture.md](01-architecture.md) |
| Technical Decisions | Key technology choices, package management, and Next.js framework decisions | [02-technical-decisions.md](02-technical-decisions.md) |
| Component Patterns | Comprehensive guide to component architecture, types, and implementation strategies | [components.md](components.md) |
| Next.js Layout Optimization | Optimizing layout and page components for better performance and UX | [nextjs-layout-optimization.md](nextjs-layout-optimization.md) |
| Next.js Client vs Server Components | Decision tree and patterns for choosing between client and server components | [nextjs-client-server-components.md](nextjs-client-server-components.md) |
| Next.js Authentication | Authentication patterns with client-side state management and middleware protection | [nextjs-authentication.md](nextjs-authentication.md) |
| Barrel Files Avoidance | Preventing unintended client-side imports by avoiding barrel files | [barrel-files-avoidance.md](barrel-files-avoidance.md) |
| Container/Presenter | Separation of data fetching from presentation logic | [container-presenter.md](container-presenter.md) |
| Custom Routing Middleware | Scalable routing system to overcome Next.js rewrite limits | [custom-routing-middleware.md](custom-routing-middleware.md) |
| DAL Patterns | Domain-oriented data access layer with type-safe interfaces | [dal-patterns.md](dal-patterns.md) |
| Error Handling with tryCatch | Consistent error handling using type-safe tryCatch utility | [error-handling-trycatch.md](error-handling-trycatch.md) |
| Icon System | Two-pronged approach for weather-specific and general UI icons | [icon-system.md](icon-system.md) |
| Jotai State Management | Atomic state management with server-side rendering support | [jotai-state-management.md](jotai-state-management.md) |
| Storybook Consolidation | Consolidating component variants to minimize Chromatic screenshots | [storybook-consolidation.md](storybook-consolidation.md) |
| SWR Data Fetching | Client-side data fetching with automatic revalidation and caching | [swr-data-fetching.md](swr-data-fetching.md) |
| Weather Data Patterns | Location management and client-side data fetching for weather components | [weather-data-patterns.md](weather-data-patterns.md) |
| Client-Side Location Hook | Pattern for accessing location data in client components with separation of concerns | [client-side-location-hook.md](client-side-location-hook.md) |
| Inline Hook Pattern | Co-location of custom hooks with their primary consumer components | [inline-hook-pattern.md](inline-hook-pattern.md) |
| Next.js Image with Fastly | Next.js Image component optimized with custom Fastly loader | [nextjs-image-fastly.md](nextjs-image-fastly.md) |
| Animal-Themed Color System | Custom Tailwind color palette with animal-themed naming convention | [animal-themed-color-system.md](animal-themed-color-system.md) |
| Semantic Typography System | Structured typography system with semantic naming conventions | [semantic-typography-system.md](semantic-typography-system.md) |
| Debug Logger Pattern | Standardized logging pattern using industry-standard debug-js library | [debug-logger.md](debug-logger.md) |
| Coding Patterns | General coding patterns and conventions for consistent, maintainable code | [coding-patterns.md](coding-patterns.md) |
