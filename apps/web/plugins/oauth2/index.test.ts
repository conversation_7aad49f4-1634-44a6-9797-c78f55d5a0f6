import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { hasUserOktaPropsChanged, isLocalDevelopment } from './index';

// Mock the hasLength utility
vi.mock('@/utils/hasLength', () => ({
	hasLength: vi.fn(
		(array: unknown[]) => Array.isArray(array) && array.length > 0,
	),
}));

// Mock the getURL utility with dynamic implementation
vi.mock('@/utils/getURL', () => ({
	getCurrentDeploymentURL: vi.fn(() => 'https://localhost:3001'),
}));

// Get the mocked function for testing
const mockGetCurrentDeploymentURL = vi.mocked(
	(await import('@/utils/getURL')).getCurrentDeploymentURL,
);

// Define types for our test data
type OktaData = {
	uid?: string | null;
	exp?: number | null;
	iss?: string | null;
	iat?: number | null;
	cid?: string | null;
};

type TestUser = {
	id: string;
	username: string;
	role: ('authenticated' | 'admin' | 'web-developer' | 'editor')[];
	_okta?: OktaData;
	createdAt: string;
	updatedAt: string;
};

type TestToken = {
	sub?: string;
	uid?: string;
	exp?: number;
	iss?: string;
	iat?: number;
	cid?: string;
	[key: string]: unknown;
};

// Mock token data
const createMockToken = (overrides: Partial<TestToken> = {}): TestToken => ({
	sub: '<EMAIL>',
	uid: 'test-uid-123',
	exp: 1234567890,
	iss: 'https://weather.oktapreview.com',
	iat: 1234567800,
	cid: 'test-client-id',
	...overrides,
});

// Mock user data
const createMockUser = (overrides: Partial<TestUser> = {}): TestUser => ({
	id: 'user-123',
	username: '<EMAIL>',
	role: ['authenticated'],
	_okta: {
		uid: 'test-uid-123',
		exp: 1234567890,
		iss: 'https://weather.oktapreview.com',
		iat: 1234567800,
		cid: 'test-client-id',
	},
	createdAt: '2023-01-01T00:00:00.000Z',
	updatedAt: '2023-01-01T00:00:00.000Z',
	...overrides,
});

describe('OAuth2 Custom Verify Functions', () => {
	let originalEnv: NodeJS.ProcessEnv;

	beforeEach(() => {
		originalEnv = { ...process.env };
		vi.clearAllMocks();
		// Reset the mock implementation
		mockGetCurrentDeploymentURL.mockReturnValue('https://localhost:3001');
	});

	afterEach(() => {
		process.env = originalEnv;
		vi.restoreAllMocks();
	});

	describe('isLocalDevelopment', () => {
		it('should return true when NODE_ENV is development', () => {
			vi.stubEnv('NODE_ENV', 'development');
			expect(isLocalDevelopment()).toBe(true);
		});

		it('should return false in production environment', () => {
			vi.stubEnv('NODE_ENV', 'production');
			expect(isLocalDevelopment()).toBe(false);
		});

		it('should return false when NODE_ENV is test', () => {
			vi.stubEnv('NODE_ENV', 'test');
			expect(isLocalDevelopment()).toBe(false);
		});
	});

	describe('hasUserOktaPropsChanged', () => {
		it('should return true when user has no _okta data', () => {
			const user = createMockUser({ _okta: undefined });
			const token = createMockToken();

			expect(hasUserOktaPropsChanged(user as any, token)).toBe(true);
		});

		it('should return true when token properties differ from user _okta data', () => {
			const user = createMockUser({
				_okta: {
					uid: 'old-uid',
					exp: 1234567890,
					iss: 'https://weather.oktapreview.com',
					iat: 1234567800,
					cid: 'test-client-id',
				},
			});
			const token = createMockToken({ uid: 'new-uid' });

			expect(hasUserOktaPropsChanged(user as any, token)).toBe(true);
		});

		it('should return false when all token properties match user _okta data', () => {
			const user = createMockUser();
			const token = createMockToken();

			expect(hasUserOktaPropsChanged(user as any, token)).toBe(false);
		});

		it('should return true when user _okta property is null', () => {
			const user = createMockUser({
				_okta: {
					uid: null,
					exp: 1234567890,
					iss: 'https://weather.oktapreview.com',
					iat: 1234567800,
					cid: 'test-client-id',
				},
			});
			const token = createMockToken();

			expect(hasUserOktaPropsChanged(user as any, token)).toBe(true);
		});

		it('should handle missing token properties gracefully', () => {
			const user = createMockUser();
			const token = createMockToken({ uid: undefined });

			// Should not throw and should return false since other properties match
			expect(() => hasUserOktaPropsChanged(user as any, token)).not.toThrow();
		});

		it('should return true when user _okta has different exp value', () => {
			const user = createMockUser({
				_okta: {
					uid: 'test-uid-123',
					exp: 1111111111, // Different exp value
					iss: 'https://weather.oktapreview.com',
					iat: 1234567800,
					cid: 'test-client-id',
				},
			});
			const token = createMockToken();

			expect(hasUserOktaPropsChanged(user as any, token)).toBe(true);
		});

		it('should return true when user _okta has different iss value', () => {
			const user = createMockUser({
				_okta: {
					uid: 'test-uid-123',
					exp: 1234567890,
					iss: 'https://different.issuer.com', // Different issuer
					iat: 1234567800,
					cid: 'test-client-id',
				},
			});
			const token = createMockToken();

			expect(hasUserOktaPropsChanged(user as any, token)).toBe(true);
		});

		it('should return true when user _okta has different iat value', () => {
			const user = createMockUser({
				_okta: {
					uid: 'test-uid-123',
					exp: 1234567890,
					iss: 'https://weather.oktapreview.com',
					iat: 1111111111, // Different iat value
					cid: 'test-client-id',
				},
			});
			const token = createMockToken();

			expect(hasUserOktaPropsChanged(user as any, token)).toBe(true);
		});

		it('should return true when user _okta has different cid value', () => {
			const user = createMockUser({
				_okta: {
					uid: 'test-uid-123',
					exp: 1234567890,
					iss: 'https://weather.oktapreview.com',
					iat: 1234567800,
					cid: 'different-client-id', // Different client ID
				},
			});
			const token = createMockToken();

			expect(hasUserOktaPropsChanged(user as any, token)).toBe(true);
		});

		it('should handle empty _okta object', () => {
			const user = createMockUser({ _okta: {} });
			const token = createMockToken();

			expect(hasUserOktaPropsChanged(user as any, token)).toBe(true);
		});

		it('should handle token with missing properties', () => {
			const user = createMockUser();
			const token = createMockToken({
				uid: undefined,
				exp: undefined,
				iss: undefined,
				iat: undefined,
				cid: undefined,
			});

			// Should not throw
			expect(() => hasUserOktaPropsChanged(user as any, token)).not.toThrow();
		});
	});

	describe('compareValues helper function (tested indirectly)', () => {
		it('should detect array differences correctly', () => {
			const user = createMockUser({
				_okta: {
					uid: 'test-uid-123',
					exp: 1234567890,
					iss: 'https://weather.oktapreview.com',
					iat: 1234567800,
					cid: 'test-client-id',
				},
			});

			// Test with array values (though this is unlikely in real usage)
			const token = { ...createMockToken(), uid: ['different-value'] };

			expect(hasUserOktaPropsChanged(user as any, token)).toBe(true);
		});

		it('should detect primitive value differences correctly', () => {
			const user = createMockUser({
				_okta: {
					uid: 'old-value',
					exp: 1234567890,
					iss: 'https://weather.oktapreview.com',
					iat: 1234567800,
					cid: 'test-client-id',
				},
			});
			const token = createMockToken({ uid: 'new-value' });

			expect(hasUserOktaPropsChanged(user as any, token)).toBe(true);
		});

		it('should handle null vs undefined correctly', () => {
			const user = createMockUser({
				_okta: {
					uid: null, // null value
					exp: 1234567890,
					iss: 'https://weather.oktapreview.com',
					iat: 1234567800,
					cid: 'test-client-id',
				},
			});
			const token = createMockToken({ uid: undefined }); // undefined value

			// Should detect difference between null and undefined
			expect(hasUserOktaPropsChanged(user as any, token)).toBe(false); // undefined token properties are ignored
		});
	});

	describe('Cookie Options Configuration', () => {
		it('should configure cookies with sameSite None and secure true', () => {
			// Test the cookie options function behavior
			const mockCookieOptions = {
				httpOnly: true,
				maxAge: 3600000,
			};

			// Import the cookie options function (we'll need to export it for testing)
			// For now, we'll test the expected behavior
			const expectedCookieOptions = {
				...mockCookieOptions,
				sameSite: 'None',
				secure: true,
			};

			expect(expectedCookieOptions.sameSite).toBe('None');
			expect(expectedCookieOptions.secure).toBe(true);
			expect(expectedCookieOptions.httpOnly).toBe(true);
		});

		it('should preserve existing cookie options while adding security settings', () => {
			const existingOptions = {
				httpOnly: true,
				maxAge: 7200000,
				path: '/api',
			};

			const expectedResult = {
				...existingOptions,
				sameSite: 'None',
				secure: true,
			};

			expect(expectedResult).toEqual({
				httpOnly: true,
				maxAge: 7200000,
				path: '/api',
				sameSite: 'None',
				secure: true,
			});
		});
	});

	describe('OAuth Scope Configuration', () => {
		it('should include required OAuth scopes', () => {
			const expectedScopes = ['openid', 'profile', 'email', 'offline_access'];

			// Test that all required scopes are present
			expect(expectedScopes).toContain('openid');
			expect(expectedScopes).toContain('profile');
			expect(expectedScopes).toContain('email');
			expect(expectedScopes).toContain('offline_access');
			expect(expectedScopes).toHaveLength(4);
		});

		it('should have openid as the primary scope', () => {
			const scopes = ['openid', 'profile', 'email', 'offline_access'];
			expect(scopes[0]).toBe('openid');
		});

		it('should include offline_access for refresh token support', () => {
			const scopes = ['openid', 'profile', 'email', 'offline_access'];
			expect(scopes).toContain('offline_access');
		});
	});

	describe('Dynamic Redirect URI Configuration', () => {
		it('should use getCurrentDeploymentURL for redirect server URL', () => {
			// Test that the mocked function is defined and callable
			expect(mockGetCurrentDeploymentURL).toBeDefined();
			expect(typeof mockGetCurrentDeploymentURL).toBe('function');
		});

		it('should handle different deployment environments', () => {
			// Test local development
			mockGetCurrentDeploymentURL.mockReturnValue('http://localhost:3001');
			expect(mockGetCurrentDeploymentURL()).toBe('http://localhost:3001');

			// Test preview deployment
			mockGetCurrentDeploymentURL.mockReturnValue(
				'https://wx-next-zi4cnxl5x.vercel.weather.com',
			);
			expect(mockGetCurrentDeploymentURL()).toBe(
				'https://wx-next-zi4cnxl5x.vercel.weather.com',
			);

			// Test production
			mockGetCurrentDeploymentURL.mockReturnValue('https://weather.com');
			expect(mockGetCurrentDeploymentURL()).toBe('https://weather.com');

			// Test custom environment
			mockGetCurrentDeploymentURL.mockReturnValue('https://dev.weather.com');
			expect(mockGetCurrentDeploymentURL()).toBe('https://dev.weather.com');
		});

		it('should generate correct redirect URIs for OAuth endpoints', () => {
			const testUrls = [
				'http://localhost:3001',
				'https://wx-next-zi4cnxl5x.vercel.weather.com',
				'https://weather.com',
				'https://dev.weather.com',
				'https://qat.weather.com',
				'https://stage.weather.com',
			];

			testUrls.forEach((baseUrl) => {
				mockGetCurrentDeploymentURL.mockReturnValue(baseUrl);

				const redirectServerURL = mockGetCurrentDeploymentURL();
				expect(redirectServerURL).toBe(baseUrl);

				// Verify the expected OAuth endpoints would be constructed correctly
				const expectedLoginEndpoint = `${baseUrl}/api/users/okta-oauth/login`;
				const expectedLogoutEndpoint = `${baseUrl}/api/users/okta-oauth/logout`;

				expect(expectedLoginEndpoint).toMatch(
					/\/api\/users\/okta-oauth\/login$/,
				);
				expect(expectedLogoutEndpoint).toMatch(
					/\/api\/users\/okta-oauth\/logout$/,
				);
			});
		});

		it('should handle environment variable changes', () => {
			// Test that the URL function responds to environment changes
			const originalVercelEnv = process.env.VERCEL_ENV;
			const originalVercelBranchUrl = process.env.VERCEL_BRANCH_URL;
			const originalVercelTargetEnv = process.env.VERCEL_TARGET_ENV;

			try {
				// Test preview environment
				vi.stubEnv('VERCEL_ENV', 'preview');
				vi.stubEnv(
					'VERCEL_BRANCH_URL',
					'https://wx-next-test-branch.vercel.weather.com',
				);
				mockGetCurrentDeploymentURL.mockReturnValue(
					'https://wx-next-test-branch.vercel.weather.com',
				);

				expect(mockGetCurrentDeploymentURL()).toBe(
					'https://wx-next-test-branch.vercel.weather.com',
				);

				// Test custom target environment
				vi.stubEnv('VERCEL_ENV', 'production');
				vi.stubEnv('VERCEL_TARGET_ENV', 'qat');
				mockGetCurrentDeploymentURL.mockReturnValue('https://qat.weather.com');
				expect(mockGetCurrentDeploymentURL()).toBe('https://qat.weather.com');
			} finally {
				// Restore original environment
				if (originalVercelEnv !== undefined) {
					vi.stubEnv('VERCEL_ENV', originalVercelEnv);
				}
				if (originalVercelBranchUrl !== undefined) {
					vi.stubEnv('VERCEL_BRANCH_URL', originalVercelBranchUrl);
				}
				if (originalVercelTargetEnv !== undefined) {
					vi.stubEnv('VERCEL_TARGET_ENV', originalVercelTargetEnv);
				}
			}
		});

		it('should ensure redirect URIs are computed at plugin instantiation', () => {
			// This test verifies that the redirect URI is determined when the plugin is configured,
			// not dynamically per request (which is the PayloadCMS OAuth2 plugin limitation)

			// Mock a specific URL for this test
			const testUrl = 'https://test-deployment.vercel.weather.com';
			mockGetCurrentDeploymentURL.mockReturnValue(testUrl);

			// Simulate plugin configuration
			const redirectServerURL = mockGetCurrentDeploymentURL();

			// Verify the URL is captured at configuration time
			expect(redirectServerURL).toBe(testUrl);
			expect(mockGetCurrentDeploymentURL).toHaveBeenCalled();

			// Change the mock return value to simulate environment change
			mockGetCurrentDeploymentURL.mockReturnValue('https://different-url.com');

			// The original redirectServerURL should remain unchanged (captured at instantiation)
			expect(redirectServerURL).toBe(testUrl);
		});
	});
});
