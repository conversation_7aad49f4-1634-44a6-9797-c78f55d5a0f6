# OAuth2 Plugin for PayloadCMS

This plugin provides OAuth2 authentication integration with Ok<PERSON> for the wx-next PayloadCMS application. It extends PayloadCMS's authentication system to support single sign-on (SSO) through Okta, allowing users to authenticate using their corporate credentials.

## Overview

The OAuth2 plugin is built on top of the `@payloadcms/plugin-oauth2` package and provides:

- **Okta Integration**: Custom configuration for Okta OAuth2 provider
- **Automatic User Creation**: Creates new users automatically when they first authenticate
- **Token Management**: Stores and updates Okta-specific token data
- **Role Assignment**: Assigns appropriate roles based on environment
- **Development Mode Support**: Special handling for local development environments

## Features

### 🔐 Secure Authentication

- OAuth2 flow with Okta identity provider
- HTTP-only cookie-based session management
- Automatic token renewal and refresh

### 👤 User Management

- Automatic user creation on first login
- Stores Okta-specific metadata (`uid`, `exp`, `iss`, `iat`, `cid`)
- Updates user data when token properties change
- Role-based access control (admin in dev, authenticated in production)

### 🛠️ Development Support

- Environment-aware configuration
- Bypass option for local development (`BYPASS_SSO=true`)
- Comprehensive test coverage

## Configuration

### Environment Variables

The plugin requires the following environment variables:

```bash
# Required - Okta OAuth2 Configuration
PAYLOAD_OAUTH_IDENTITY_METADATA=https://your-okta-domain.oktapreview.com/.well-known/openid_configuration
PAYLOAD_OAUTH_CLIENT_ID=your-okta-client-id

# Optional - Development/Testing
BYPASS_SSO=true  # Set to 'true' to disable SSO in non-production environments
NODE_ENV=development|production

# Vercel Environment Variables (automatically set by Vercel)
VERCEL_ENV=development|preview|production
VERCEL_TARGET_ENV=qat|dev|staging  # Custom environment mapping
VERCEL_BRANCH_URL=your-branch-url.vercel.app  # Preview deployment URL
```

### Dynamic Redirect URI Configuration

The plugin uses a dynamic approach to determine the correct redirect URI based on the deployment environment. This is necessary because PayloadCMS OAuth2 plugins compute the redirect URI at instantiation time (server startup), not per-request.

#### Why Dynamic URL Generation is Required

1. **Vercel Deployment Complexity**: Each Vercel deployment creates multiple URLs:
   - Commit-based: `https://wx-next-zi4cnxl5x.vercel.weather.com`
   - Branch-based: `https://wx-next-web-git-feat-branch.vercel.weather.com`
   - Custom domains: `https://dev.wxnext.weather.com`

2. **OAuth2 Plugin Limitation**: The redirect URI must be known at server startup, not dynamically per request

3. **Environment Mapping**: Different environments need different base URLs for proper OAuth flow

#### Environment URL Mapping

The `getCurrentDeploymentURL()` function in `utils/getURL.ts` handles environment detection:

```typescript
// Environment mapping logic:
// 1. Local development: http://localhost:3001
// 2. Preview environments: Uses VERCEL_BRANCH_URL
// 3. Production: https://weather.com
// 4. Custom environments via VERCEL_TARGET_ENV:
//    - qat → https://qat.weather.com
//    - dev → https://dev.weather.com
//    - staging → https://stage.weather.com
```

This approach ensures that:

- Each deployment gets the correct redirect URI
- OAuth flows work across all environments
- No manual configuration is needed per deployment

### Okta Application Settings

Your Okta application should be configured with the following settings:

#### General Settings

- **Application type**: Single Page App (SPA)
- **Grant type**: Authorization Code with PKCE
- **Client authentication**: None (public client)

#### Sign-in redirect URIs

Due to the dynamic nature of Vercel deployments, you'll need to configure multiple redirect URIs in your Okta application:

```text
# Local development
https://localhost:3001/api/users/okta-oauth/login

# Production
https://weather.com/api/users/okta-oauth/login

# Custom environments
https://qat.weather.com/api/users/okta-oauth/login
https://dev.weather.com/api/users/okta-oauth/login
https://stage.weather.com/api/users/okta-oauth/login

# Preview environments (add as needed)
https://*.vercel.weather.com/api/users/okta-oauth/login
```

**Note**: Okta may require you to add specific preview URLs individually, as wildcard support varies by Okta configuration.

#### Sign-out redirect URIs

```text
# Local development
https://localhost:3001/api/users/okta-oauth/logout

# Production
https://weather.com/api/users/okta-oauth/logout

# Custom environments
https://qat.weather.com/api/users/okta-oauth/logout
https://dev.weather.com/api/users/okta-oauth/logout
https://stage.weather.com/api/users/okta-oauth/logout

# Preview environments (add as needed)
https://*.vercel.weather.com/api/users/okta-oauth/logout
```

#### Scopes

- `openid` (required)
- `profile` (required)
- `email` (required)

## Usage

### Plugin Configuration

The plugin is configured in the PayloadCMS config:

```typescript
import { oAuth2 } from "./plugins/oauth2";

export default buildConfig({
 plugins: [
  oAuth2,
  // ... other plugins
 ],
 // ... rest of config
});
```

### Plugin Options

The plugin is configured with the following options:

```typescript
{
  disabled: boolean,              // Disabled when BYPASS_SSO=true in non-production
  strategyName: 'okta-oauth',     // Strategy identifier
  redirectServerURL: string,      // Base URL for redirects
  collections: [{
    slug: 'users',                // Target collection
    identityMetadata: string,     // Okta .well-known endpoint
    clientID: string,             // Okta client ID
    scope: ['openid', 'profile', 'email'],
    endOAuthSessionOnLogout: false,
    usernameField: 'username',    // Field to store user identifier
    verify: customVerify          // Custom verification function
  }]
}
```

## Custom Verification Logic

The plugin includes a custom verify function that handles Okta-specific token processing:

### Token Processing

- Uses the `sub` field from Okta tokens as the username
- Extracts and stores Okta metadata (`uid`, `exp`, `iss`, `iat`, `cid`)
- Updates user records when token properties change

### User Creation

- Automatically creates new users on first authentication
- Assigns roles based on environment:
  - **Development**: `['admin']`
  - **Production**: `['authenticated']`

### User Updates

- Compares current user data with new token data
- Updates `_okta` field when properties have changed
- Preserves existing user data while updating Okta-specific fields

## API Endpoints

The plugin automatically creates the following endpoints:

### Authentication Endpoints

- `GET /api/users/okta-oauth/login` - Initiates OAuth flow
- `GET /api/users/okta-oauth/logout` - Handles logout
- `GET /api/users/sso/meta` - Returns authorization URL

### Meta Endpoint Query Parameters

```text
?redirect=https://example.com          # Success redirect URL
&failedRedirect=https://example.com    # Failure redirect URL
&serverURL=https://your-domain.com     # Override server URL
```

## Development

### Local Development

1. **Environment Setup**:

   ```bash
   # Set bypass flag to disable SSO locally
   BYPASS_SSO=true

   # Or configure with local Okta settings
   PAYLOAD_OAUTH_IDENTITY_METADATA=https://dev-okta.oktapreview.com/.well-known/openid_configuration
   PAYLOAD_OAUTH_CLIENT_ID=your-dev-client-id
   ```

2. **Testing**:

   ```bash
   # Run unit tests
   pnpm test plugins/oauth2/index.test.ts

   # Run all tests
   pnpm test
   ```

### Environment Detection

The plugin automatically detects development environments using:

```typescript
const isLocalDevelopment = () => {
 return process.env.NODE_ENV === "development";
};
```

## Testing

The plugin includes comprehensive test coverage for:

### Core Functions

- `isLocalDevelopment()` - Environment detection
- `hasUserOktaPropsChanged()` - Token comparison logic
- Custom verify function behavior

### Test Scenarios

- ✅ User creation on first login
- ✅ User updates when token changes
- ✅ Environment-based role assignment
- ✅ Token property comparison
- ✅ Error handling and edge cases

### Running Tests

```bash
# Run OAuth2 plugin tests
pnpm test plugins/oauth2/index.test.ts

# Run with coverage
pnpm test plugins/oauth2/index.test.ts --coverage

# Run in watch mode
pnpm test:watch plugins/oauth2/index.test.ts
```

## Data Structure

### User Document Structure

When a user authenticates, their document includes:

```typescript
{
  id: string,
  username: string,              // From token 'sub' field
  role: ['admin'] | ['authenticated'],
  collection: 'users',
  _strategy: 'okta-oauth',
  _okta: {                      // Okta-specific metadata
    uid: string,                // User ID from Okta
    exp: number,                // Token expiration
    iss: string,                // Token issuer
    iat: number,                // Token issued at
    cid: string                 // Client ID
  },
  createdAt: string,
  updatedAt: string
}
```

### Token Structure

Okta tokens contain the following relevant fields:

```typescript
{
  sub: string,                  // User email/identifier
  uid: string,                  // Okta user ID
  exp: number,                  // Expiration timestamp
  iss: string,                  // Issuer URL
  iat: number,                  // Issued at timestamp
  cid: string,                  // Client ID
  // ... other standard OAuth2 fields
}
```

## Security Considerations

### Token Storage

- Tokens are stored in HTTP-only cookies
- Automatic token refresh prevents session expiration
- Sensitive token data is stored server-side only

### User Validation

- Users must be granted access in Okta before they can authenticate
- Role assignment is environment-aware
- Failed authentication attempts are logged

### Environment Isolation

- Development and production use separate Okta applications
- Bypass mechanism for local development
- Environment-specific role assignment

## Troubleshooting

### Common Issues

1. **"User not found" errors**

   - Ensure user has been granted access in Okta
   - Check that the `sub` field in the token matches expected format

2. **Redirect URI mismatch**

   - Verify redirect URIs in Okta match your application URLs
   - Check that `redirectServerURL` is correctly configured

3. **Token validation failures**

   - Verify `PAYLOAD_OAUTH_IDENTITY_METADATA` URL is accessible
   - Check that Okta application scopes include `openid`, `profile`, `email`

4. **Development environment issues**
   - Set `BYPASS_SSO=true` to disable OAuth in local development
   - Ensure `NODE_ENV` is set correctly

### Debug Mode

Enable debug logging by setting:

```bash
DEBUG=payload:oauth2
```

### Health Check

Test the OAuth configuration:

```bash
# Check metadata endpoint
curl https://your-okta-domain.oktapreview.com/.well-known/openid_configuration

# Check authorization URL
curl http://localhost:3001/api/users/sso/meta
```

## Migration Notes

### From Previous Versions

If migrating from a previous OAuth implementation:

1. Update environment variables to match new naming convention
2. Update Okta redirect URIs to use new strategy name (`okta-oauth`)
3. Test user creation and role assignment in development environment

### Database Changes

The plugin adds the following fields to user documents:

- `_okta` - Object containing Okta-specific metadata
- `_strategy` - String identifying the authentication strategy

## Contributing

### Adding New Features

1. Update the plugin configuration in `index.ts`
2. Add corresponding tests in `index.test.ts`
3. Update this README with new functionality
4. Test in both development and production environments

### Code Style

- Follow existing TypeScript patterns
- Include comprehensive error handling
- Add unit tests for new functionality
- Update type definitions as needed

## Related Documentation

- [PayloadCMS OAuth2 Plugin Documentation](https://payloadcms.com/docs/plugins/oauth2)
- [Okta Developer Documentation](https://developer.okta.com/docs/)
- [OAuth2 Specification](https://tools.ietf.org/html/rfc6749)
