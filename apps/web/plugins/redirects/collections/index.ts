import type {
	CollectionConfig,
	Payload,
	CollectionAfter<PERSON>hangeHook,
	CollectionAfterDeleteHook,
} from 'payload';
import * as BloomFilters from 'bloom-filters';
import type { Redirect, RedirectsSelect } from '@/payload-types';
import {
	getBloomFilterFromEdgeConfig,
	saveBloomFilterToEdgeConfig,
	isEdgeConfigAvailable,
} from '@/utils/edgeConfig';

async function saveFilter(
	payload: Payload,
	filter: BloomFilters.ScalableBloomFilter,
): Promise<void> {
	if (!isEdgeConfigAvailable()) {
		payload.logger.warn('Edge Config not available, bloom filter not saved');
		return;
	}

	const success = await saveBloomFilterToEdgeConfig(filter);
	if (success) {
		payload.logger.debug('Bloom filter saved to Edge Config');
	} else {
		payload.logger.error('Failed to save bloom filter to Edge Config');
	}
}

async function getFilter(
	payload: Payload,
): Promise<BloomFilters.ScalableBloomFilter | null> {
	if (!isEdgeConfigAvailable()) {
		payload.logger.warn('Edge Config not available');
		return null;
	}

	const filter = await getBloomFilterFromEdgeConfig();
	if (filter) {
		payload.logger.debug('Bloom filter loaded from Edge Config');
	} else {
		payload.logger.warn('No bloom filter found in Edge Config');
	}

	return filter;
}

async function generateAndSaveBloomFilter(
	payload: Payload,
): Promise<BloomFilters.ScalableBloomFilter> {
	const allRedirects = await fetchAllRedirects(payload);

	const estimatedItemCount =
		allRedirects.length > 0 ? allRedirects.length : 1000;
	const falsePositiveRate = 0.001;

	// Use the static create method for ScalableBloomFilter
	const filter: BloomFilters.ScalableBloomFilter =
		BloomFilters.ScalableBloomFilter.create(
			estimatedItemCount,
			falsePositiveRate,
		);

	for (const redirect of allRedirects) {
		if (redirect.source) {
			filter.add(redirect.source);
		}
	}

	await saveFilter(payload, filter);

	return filter;
}

async function fetchAllRedirects(payload: Payload): Promise<Redirect[]> {
	const allRedirects: Redirect[] = [];
	let page = 1;
	let hasMore = true;
	const limit = 100;

	while (hasMore) {
		const result = await payload.find<'redirects', RedirectsSelect>({
			collection: 'redirects',
			limit,
			page,
		});

		allRedirects.push(...(result.docs as Redirect[]));
		hasMore = result.hasNextPage;
		page++;
	}

	return allRedirects;
}

/**
 * Updates the bloom filter with a new redirect after creation
 * This function should be called after a redirect is successfully created
 */
export async function updateBloomFilterAfterRedirectCreation(
	payload: Payload,
	source: string,
): Promise<void> {
	try {
		payload.logger.debug(
			`Adding new redirect to filter after creation: ${source}`,
		);

		const filter = await getFilter(payload);

		if (!filter) {
			payload.logger.debug('No valid filter found, regenerating...');
			await generateAndSaveBloomFilter(payload);
			return;
		}

		// Add the new redirect to the existing filter
		filter.add(source);

		await saveFilter(payload, filter);

		payload.logger.debug(`Filter updated with new redirect: ${source}`);
	} catch (error: unknown) {
		payload.logger.error(
			`Filter update failed after redirect creation: ${error instanceof Error ? error.message : 'Unknown error'}`,
		);
		// Don't regenerate here to avoid potential loops
	}
}

// Hook for handling redirect updates (not creation)
const afterChangeHook: CollectionAfterChangeHook<Redirect> = async (args) => {
	const { req, doc, operation } = args;
	try {
		if (!doc.source) {
			return;
		}

		// Skip bloom filter updates for new redirect creation
		// The bloom filter will be updated after creation is complete
		if (operation === 'create') {
			req.payload.logger.debug(
				`Skipping bloom filter update for new redirect creation: ${doc.source}`,
			);
			return;
		}

		req.payload.logger.debug(
			`Updating bloom filter for redirect change: ${doc.source}`,
		);

		const filter = await getFilter(req.payload);

		if (!filter) {
			req.payload.logger.debug('No valid filter found, regenerating...');
			await generateAndSaveBloomFilter(req.payload);
			return;
		}

		// Add the updated redirect to the existing filter
		filter.add(doc.source);

		await saveFilter(req.payload, filter);

		req.payload.logger.debug(
			`Filter updated with changed redirect: ${doc.source}`,
		);
	} catch (error: unknown) {
		req.payload.logger.error(
			`Filter update failed for change: ${error instanceof Error ? error.message : 'Unknown error'}`,
		);
		// Don't regenerate here to avoid potential loops
	}
};

// Hook for handling redirect deletions
const afterDeleteHook: CollectionAfterDeleteHook<Redirect> = async (args) => {
	const { req } = args;
	try {
		// For deletions, regenerate the filter since ScalableBloomFilter doesn't support removal
		req.payload.logger.info('Regenerating filter due to redirect deletion(s)');
		await generateAndSaveBloomFilter(req.payload);
	} catch (error: unknown) {
		req.payload.logger.error(
			`Filter regeneration failed for delete: ${error instanceof Error ? error.message : 'Unknown error'}`,
		);
	}
};

const Redirects: CollectionConfig = {
	slug: 'redirects',
	admin: {
		defaultColumns: ['source', 'destination', 'permanent', 'createdAt'],
		useAsTitle: 'source',
		group: 'Content',
	},
	versions: false,
	access: {
		read: () => true,
	},
	hooks: {
		afterChange: [afterChangeHook],
		afterDelete: [afterDeleteHook],
	},
	fields: [
		{
			name: 'source',
			label: 'Source Path',
			type: 'text',
			required: true,
			unique: true,
			index: true,
			admin: {
				description:
					'The original URL path that should be redirected (e.g., /old-path)',
			},
		},
		{
			name: 'destination',
			label: 'Destination Path',
			type: 'text',
			required: true,
			index: true,
			admin: {
				description: 'The new URL path to redirect to (e.g., /new-path)',
			},
		},
		{
			name: 'permanent',
			label: 'Permanent Redirect (301)',
			type: 'checkbox',
			defaultValue: true,
			admin: {
				description:
					'If checked, a 301 (permanent) redirect will be used. Otherwise, a 302 (temporary) redirect will be used.',
			},
		},
		{
			name: 'notes',
			label: 'Notes',
			type: 'textarea',
			admin: {
				description:
					'Optional notes about this redirect (e.g., why it was created, when it can be removed)',
			},
		},
		{
			name: 'createdByAssetNameChange',
			type: 'checkbox',
			defaultValue: false,
			admin: {
				position: 'sidebar',
				description:
					'Whether this redirect was automatically created due to an assetName change',
			},
		},
		{
			name: 'originalCollection',
			type: 'text',
			admin: {
				position: 'sidebar',
				description:
					'The collection that contained the document whose assetName changed',
				condition: (data: Partial<Redirect>) => !!data.createdByAssetNameChange,
			},
		},
		{
			name: 'originalDocumentId',
			type: 'text',
			admin: {
				position: 'sidebar',
				description: 'The ID of the document whose assetName changed',
				condition: (data: Partial<Redirect>) => !!data.createdByAssetNameChange,
			},
		},
	],
};

export default Redirects;
