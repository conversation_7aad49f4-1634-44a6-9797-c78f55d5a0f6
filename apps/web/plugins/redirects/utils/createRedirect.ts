import { PayloadRequest } from 'payload';
import { revalidatePath, revalidateTag } from 'next/cache';
import { RedirectsSelect } from '@/payload-types';
import { updateBloomFilterAfterRedirectCreation } from '../collections';

type CreateRedirectResponse = {
	success: boolean;
	data?: {
		redirectEntry: string;
	};
	error?: string;
};

export const createRedirect = async (data: {
	req: PayloadRequest;
	source: string;
	destination: string;
	originalCollection?: string;
	originalDocumentId?: string;
}): Promise<CreateRedirectResponse> => {
	const { req, source, destination, originalCollection, originalDocumentId } =
		data;

	if (!req.payload) {
		return {
			success: false,
			error: 'Payload not found',
		};
	}
	const { payload } = req;

	try {
		// Check if a redirect already exists
		const existingRedirect = await payload.find<'redirects', RedirectsSelect>({
			collection: 'redirects',
			where: {
				source: { equals: source },
			},
		});

		// If no redirect exists, create one
		if (existingRedirect.totalDocs === 0) {
			const newRedirect = await payload.create<'redirects', RedirectsSelect>({
				collection: 'redirects',
				data: {
					source,
					destination,
					permanent: true,
					createdByAssetNameChange: !!originalCollection,
					originalCollection,
					originalDocumentId,
				},
			});

			payload.logger.info(
				`[REDIRECTS_PLUGIN] Created redirect for: ${source} → ${destination}`,
			);

			// Update bloom filter after successful redirect creation
			await updateBloomFilterAfterRedirectCreation(payload, source);

			// Revalidate paths and tags
			revalidatePath(source);
			revalidateTag(`redirect:${source}`);
			revalidatePath(destination);
			revalidateTag(`redirect:${destination}`);

			return {
				success: true,
				data: {
					redirectEntry: newRedirect.id,
				},
			};
		}

		payload.logger.debug(
			`[REDIRECTS_PLUGIN] Redirect already exists for: ${source} → ${destination}`,
		);
		return {
			success: true,
			data: {
				redirectEntry: existingRedirect.docs[0]?.id || '',
			},
		};
	} catch (error: unknown) {
		payload.logger.error(
			`[REDIRECTS_PLUGIN] Failed to create redirect: ${(error as Error).message}`,
		);
		return {
			success: false,
			error: (error as Error).message,
		};
	}
};
