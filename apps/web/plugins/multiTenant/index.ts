import { multiTenantPlugin } from '@payloadcms/plugin-multi-tenant';
import type { Config } from '@/payload-types';
import { isAdmin } from '@/configs/access';
import { getUserTenantIDs } from '@/utils/getUserTenantIDs';

export const multiTenant = multiTenantPlugin<Config>({
	collections: {
		pages: {},
		articles: {},
		images: {},
		liveblogs: {},
		videos: {},
	},
	enabled: true,
	tenantField: {
		access: {
			read: () => true,
			update: ({ req }) => {
				if (isAdmin(req.user)) {
					return true;
				}
				return getUserTenantIDs(req.user).length > 0;
			},
		},
	},
	tenantsArrayField: {
		includeDefaultField: false,
	},
	userHasAccessToAllTenants: (user) => isAdmin(user),
});
