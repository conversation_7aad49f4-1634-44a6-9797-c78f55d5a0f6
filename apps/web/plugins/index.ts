import type { Page, Article } from '@/payload-types';
import type { Plugin } from 'payload';

import { seoPlugin } from '@payloadcms/plugin-seo';
import { GenerateTitle, GenerateURL } from '@payloadcms/plugin-seo/types';
import { s3SdotConnector } from './s3-connector';
import { multiTenant } from './multiTenant';
import { redirectsPlugin } from './redirects';

const generateTitle: GenerateTitle<Article | Page> = ({ doc }) => {
	return doc?.title
		? `${doc.title} | the weather company foo bar`
		: 'the weather company foo bar';
};

const generateURL: GenerateURL<Article | Page> = ({ doc, collectionSlug }) => {
	let path = '/';

	switch (collectionSlug) {
		case 'articles':
			path = `/${(doc as Article).assetName}`;
			break;
		case 'pages':
			path = `/${doc.assetName}`;
			break;
		default:
	}

	let url = process.env.NEXT_PUBLIC_SERVER_URL;

	if (!url && process.env.VERCEL_PROJECT_PRODUCTION_URL) {
		url = `https://${process.env.VERCEL_PROJECT_PRODUCTION_URL}`;
	}

	if (!url) {
		url = 'https://localhost:3001';
	}

	return `${url}${path}`;
};

export const plugins: Plugin[] = [
	seoPlugin({
		generateTitle,
		generateURL,
	}),
	s3SdotConnector,
	multiTenant,
	redirectsPlugin(),
];
