import {
	deviceClassFlag,
	partnerParamFlag,
	precomputeFlags,
	privacyRegimeCodeFlag,
	userTierFlag,
} from '@/flags';

/**
 * Context parameter interface
 */
export type ContextParameters =
	| {
			[k: string]: unknown;
	  }
	| Record<string, string | string[]>
	| unknown[]
	| string
	| number
	| boolean
	| null
	| undefined;

/**
 * Extracts context parameters from the request, via search params.
 * If additional context parameters sources need to be used, they can be added
 * to the searchParams argument.
 * This is really only used for debugging via an api endpoint.
 *
 * @param searchParams The query parameters from a request
 * @returns Object containing extracted context parameters
 */
export const extractContextParameters = (
	searchParams: Record<string, string | string[] | undefined>,
): ContextParameters => {
	const params: ContextParameters = {};

	params.pageKey = getPageKey(searchParams);
	params.deviceClass = getDeviceClass(searchParams);
	params.partner = getPartner(searchParams);
	params.weatherMode = getWeatherMode(searchParams);
	params.siteMode = getSiteMode(searchParams);
	params.privacyRegime = getPrivacyRegime(searchParams);

	// remove falsy params
	const filteredParams = Object.fromEntries(
		Object.entries(params).filter(([, val]) => val),
	);

	return filteredParams;
};

export const getPageKey = (
	searchParams: Record<string, string | string[] | undefined>,
): string | string[] => {
	const pageKeySearchParam = getFromSearchParams(searchParams, 'pageKey');

	return pageKeySearchParam;
};

export const getDeviceClass = (
	searchParams: Record<string, string | string[] | undefined>,
): string | string[] => {
	const deviceSearchParam = getFromSearchParams(searchParams, 'deviceClass');

	// default to mobile
	return deviceSearchParam || 'mobile';
};

export const getPartner = (
	searchParams: Record<string, string | string[] | undefined>,
): string | string[] => {
	const partnerParam = getFromSearchParams(searchParams, 'par');

	return partnerParam || '';
};

export const getWeatherMode = (
	searchParams: Record<string, string | string[] | undefined>,
): string | string[] => {
	const weatherModeParam = getFromSearchParams(searchParams, 'weatherMode');

	return weatherModeParam;
};

export const getSiteMode = (
	searchParams: Record<string, string | string[] | undefined>,
): string | string[] => {
	const siteModeParam = getFromSearchParams(searchParams, 'siteMode');

	return siteModeParam;
};

export const getPrivacyRegime = (
	searchParams: Record<string, string | string[] | undefined>,
): string | string[] => {
	const privacyRegime = getFromSearchParams(searchParams, 'privacyRegime');

	return privacyRegime;
};

/**
 * Returns the value(s) of the requested search param.
 * If multiple values are set for the requested key, all values are returned as an array.
 * If a single value is set, it's returned as a string.
 *
 * @param searchParams Query parameters from a request
 * @param key Query parameter to look-up
 * @returns String for single values, string array for multiple values, or empty string if not found
 */
const getFromSearchParams = (
	searchParams: Record<string, string | string[] | undefined>,
	key: string,
): string => {
	const requestedParam = searchParams[key];
	if (!requestedParam) return '';

	// If it's already an array with valid values, return it
	if (
		Array.isArray(requestedParam) &&
		requestedParam.length > 0 &&
		requestedParam[0]
	) {
		return requestedParam[0];
	}
	// Otherwise, return as a single string
	else if (requestedParam && typeof requestedParam === 'string') {
		return requestedParam;
	}

	return '';
};

export async function getContextParamsFromCode(
	code: string,
): Promise<Record<string, string>> {
	let params = {};
	try {
		const [partner, deviceClass, privacyRegime, userTier] = await Promise.all([
			partnerParamFlag(code, precomputeFlags),
			deviceClassFlag(code, precomputeFlags),
			privacyRegimeCodeFlag(code, precomputeFlags),
			userTierFlag(code, precomputeFlags),
		]);
		params = {
			partner,
			deviceClass,
			privacyRegime,
			userTier,
		};
	} catch (err) {
		console.error(err);
	}

	return params;
}

/**
 * Returns all context parameters to be used for contextual queries.
 * E.g. the result of this can be used as parameters in a call to getContextualizedPage.
 * @see ./getContextualizedPage.ts
 */
export async function getContextParams(
	code: string,
): Promise<Record<string, string>> {
	const contextParamsFromCode = await getContextParamsFromCode(code);
	// add additional context params here
	// const weatherMode = await getWeatherMode();
	return {
		...contextParamsFromCode,
		// weatherMode,
	};
}
