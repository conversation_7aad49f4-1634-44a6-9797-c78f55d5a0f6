/**
 * PayloadCMS Form Helper Utilities
 * 
 * These utilities help resolve common form state issues in PayloadCMS admin interface.
 * They can be used manually in browser console or integrated into custom components.
 */

/**
 * Force reload the current edit page with draft data
 * Usage: Call this function in browser console when form is empty but draft exists
 */
export const reloadDraftData = () => {
	try {
		const url = new URL(window.location.href);
		url.searchParams.set('draft', 'true');
		url.searchParams.set('_reload', Date.now().toString());
		window.location.href = url.toString();
	} catch (error) {
		console.error('Failed to reload draft data:', error);
		window.location.reload();
	}
};

/**
 * Force reload the current edit page with published data
 * Usage: Call this function when you want to see the published version
 */
export const reloadPublishedData = () => {
	try {
		const url = new URL(window.location.href);
		url.searchParams.delete('draft');
		url.searchParams.set('_reload', Date.now().toString());
		window.location.href = url.toString();
	} catch (error) {
		console.error('Failed to reload published data:', error);
		window.location.reload();
	}
};

/**
 * Check if the current form appears to be empty
 * Returns true if form has no meaningful content
 */
export const isFormEmpty = (): boolean => {
	try {
		const form = document.querySelector('form') as HTMLFormElement;
		if (!form) return true;

		const inputs = form.querySelectorAll('input, textarea, select');
		let hasContent = false;

		inputs.forEach((input) => {
			const element = input as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
			if (element.name && !element.name.startsWith('_') && element.value.trim()) {
				hasContent = true;
			}
		});

		// Check for rich text content
		const richTextElements = form.querySelectorAll('[data-lexical-editor]');
		richTextElements.forEach((element) => {
			if (element.textContent && element.textContent.trim()) {
				hasContent = true;
			}
		});

		return !hasContent;
	} catch (error) {
		console.warn('Error checking form state:', error);
		return false;
	}
};

/**
 * Get current document ID from URL
 */
export const getCurrentDocumentId = (): string | null => {
	try {
		const pathParts = window.location.pathname.split('/');
		const editIndex = pathParts.findIndex(part => part === 'edit');
		if (editIndex !== -1 && editIndex + 1 < pathParts.length) {
			return pathParts[editIndex + 1];
		}
		return null;
	} catch (error) {
		console.warn('Error getting document ID:', error);
		return null;
	}
};

/**
 * Get current collection slug from URL
 */
export const getCurrentCollection = (): string | null => {
	try {
		const pathParts = window.location.pathname.split('/');
		const adminIndex = pathParts.findIndex(part => part === 'admin');
		if (adminIndex !== -1 && adminIndex + 2 < pathParts.length) {
			return pathParts[adminIndex + 2];
		}
		return null;
	} catch (error) {
		console.warn('Error getting collection:', error);
		return null;
	}
};

/**
 * Check if draft data exists for current document
 */
export const checkDraftExists = async (): Promise<boolean> => {
	try {
		const collection = getCurrentCollection();
		const id = getCurrentDocumentId();
		
		if (!collection || !id) return false;

		const response = await fetch(
			`/api/payload/${collection}/${id}?draft=true&depth=0`,
			{
				credentials: 'include',
				headers: {
					'Accept': 'application/json',
				},
			}
		);

		if (response.ok) {
			const data = await response.json();
			return data && Object.keys(data).length > 0;
		}
		return false;
	} catch (error) {
		console.warn('Error checking draft existence:', error);
		return false;
	}
};

/**
 * Diagnostic function to check form state and provide recommendations
 * Usage: Call this in browser console to diagnose form issues
 */
export const diagnoseFormState = async () => {
	console.log('🔍 PayloadCMS Form State Diagnosis');
	console.log('=====================================');
	
	const collection = getCurrentCollection();
	const id = getCurrentDocumentId();
	const isEmpty = isFormEmpty();
	const isDraftMode = window.location.href.includes('draft=true');
	
	console.log(`Collection: ${collection}`);
	console.log(`Document ID: ${id}`);
	console.log(`Form is empty: ${isEmpty}`);
	console.log(`Draft mode: ${isDraftMode}`);
	
	if (collection && id) {
		const draftExists = await checkDraftExists();
		console.log(`Draft exists: ${draftExists}`);
		
		if (isEmpty && draftExists) {
			console.log('⚠️  ISSUE DETECTED: Form is empty but draft data exists');
			console.log('💡 SOLUTION: Run reloadDraftData() to fix this');
		} else if (isEmpty && !draftExists) {
			console.log('ℹ️  Form is empty and no draft exists (this is normal for new documents)');
		} else {
			console.log('✅ Form state appears normal');
		}
	}
	
	console.log('\n🛠️  Available helper functions:');
	console.log('- reloadDraftData() - Reload with draft data');
	console.log('- reloadPublishedData() - Reload with published data');
	console.log('- isFormEmpty() - Check if form is empty');
	console.log('- checkDraftExists() - Check if draft data exists');
};

// Make functions available globally for console usage
if (typeof window !== 'undefined') {
	(window as any).payloadFormHelpers = {
		reloadDraftData,
		reloadPublishedData,
		isFormEmpty,
		getCurrentDocumentId,
		getCurrentCollection,
		checkDraftExists,
		diagnoseFormState,
	};
}
