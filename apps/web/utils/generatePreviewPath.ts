import { PayloadRequest, CollectionSlug } from 'payload';

/**
 * Maps collection slugs to their corresponding URL prefixes
 */
export const collectionPrefixMap: Partial<Record<CollectionSlug, string>> = {
	articles: '/content',
	pages: '/pages',
	videos: '/videos',
};

// Define supported collection types for better type safety
export type SupportedCollection = keyof typeof collectionPrefixMap;

type Props = {
	collection: SupportedCollection;
	data: Record<string, unknown>;
	locale?: string;
	req?: PayloadRequest;
	isLivePreview?: boolean;
};

/**
 * Generates a path for live preview mode
 * Uses ID-based paths to maintain preview during slug changes
 */
function generateLivePreviewPath(
	collection: SupportedCollection,
	data: Record<string, unknown>,
	locale?: string,
): string {
	const prefix = collectionPrefixMap[collection] || '';
	return locale && locale !== 'en-US'
		? `/${locale}${prefix}/${data.id}`
		: `${prefix}/${data.id}`;
}

/**
 * Generates a path for standard preview mode
 * Uses assetName for pages, or direct assetName for other collections
 */
export function generateStandardPreviewPath(
	collection: SupportedCollection,
	data: Record<string, unknown>,
): string | null | undefined {
	if (collection === 'pages') {
		const prefix = collectionPrefixMap[collection] || '';
		return `${prefix}${data.assetName}`;
	}

	return data.assetName as string;
}

/**
 * Generates a preview path for content based on collection type and preview mode
 *
 * @param options - Configuration options
 * @returns The preview URL with encoded parameters
 */
export const generatePreviewPath = ({
	collection,
	data,
	locale,
	isLivePreview,
}: Props): string => {
	// Generate the appropriate path based on preview mode
	const path = isLivePreview
		? generateLivePreviewPath(collection, data, locale)
		: generateStandardPreviewPath(collection, data);

	// Encode parameters for the preview route
	const encodedParams = new URLSearchParams({
		path: path as string,
		previewSecret: process.env.PREVIEW_SECRET || '',
	});

	return `/api/payload/v1/preview?${encodedParams.toString()}`;
};
