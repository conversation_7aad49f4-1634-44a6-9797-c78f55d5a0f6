/* eslint-disable @next/next/no-before-interactive-script-outside-document */
import Script from 'next/script';
import { rum } from './rum';
import { createLogger } from '@repo/logger';

const logger = createLogger('NewRelic');

export type ActionName = string;
export type AttributeName = string;
export type AttributeValue = string | number | boolean | null;
export type AttributeMap = Record<AttributeName, AttributeValue>;

declare global {
	interface Window {
		NREUM: {
			addPageAction: (name: ActionName, attributes?: AttributeMap) => void;
			setCustomAttribute: (
				name: AttributeName,
				value: AttributeValue,
				persist?: boolean,
			) => void;
		};
	}
}

const rumAccountId = process.env.NEW_RELIC_RUM_ACCOUNT_ID;
const rumAppId = process.env.NEW_RELIC_RUM_APP_ID;
const rumLicenseKey = process.env.NEW_RELIC_RUM_LICENSE_KEY;

/**
 * Adds New Relic's Real-time User Metrics (RUM) library to a page.
 * Must be used within an app's root layout.
 */
export function NewRelic() {
	const rumScript = rum({
		accountId: rumAccountId,
		appId: rumAppId,
		licenseKey: rumLicenseKey,
	});

	return !rumScript ? null : (
		<Script id="newrelic-rum" strategy="beforeInteractive">
			{rumScript}
		</Script>
	);
}

const metaPrefix = 'meta.';

/**
 * Sends a RUM event to New Relic with optional metadata
 */
export function sendReumEvent(name: string, meta = {}) {
	try {
		window?.NREUM?.addPageAction?.(name, {
			...prefixKeys(meta, metaPrefix),
		});
	} catch (e: unknown) {
		logger.error(
			'New Relic: sendReumEvent error',
			e instanceof Error ? e.message : String(e),
		);
	}
}

/**
 * Prefixes all keys in an object with a given prefix
 */
const prefixKeys = (obj: AttributeMap, prefix = ''): AttributeMap =>
	Object.fromEntries(
		Object.entries(obj).map(([key, value]) => [`${prefix}${key}`, value]),
	);
