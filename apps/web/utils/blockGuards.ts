import type { Image, User, Article, ContentQuery } from '@/payload-types';

// Type guard to check if author is a User object (not a string)
export function isUser(author: string | User): author is User {
	return typeof author !== 'string';
}

// Type guard to check if profilePicture is an Image object
export function isImageObject(
	image: object | string | Image | null | undefined,
): image is Image {
	return typeof image === 'object' && image !== null && 'url' in image;
}

export function hasAuthors(
	article: Article,
): article is Article & { authors: User[]; publishedAt: string } {
	return (
		article &&
		Array.isArray(article.authors) &&
		typeof article.publishDate === 'string' &&
		article.authors.every((author) => {
			// Ensure author is a User object and has authorData
			if (isUser(author)) {
				const authorData = author.authorData;
				return (
					authorData &&
					typeof authorData.firstName === 'string' &&
					typeof authorData.lastName === 'string' &&
					(authorData.profilePicture === undefined ||
						typeof authorData.profilePicture === 'object')
				);
			}
			return false; // Author is a string (ID), not a User object
		})
	);
}

/**
 * Type guard to check if an item is an Article
 */
export function isArticleObject(item: object): item is Article {
	return (
		item &&
		typeof item === 'object' &&
		('title' in item || 'body' in item || 'featuredImage' in item)
	);
}

/**
 * Type guard to check if an item has video-related properties
 */
function isVideoObject(item: object): item is object & {
	mimeType?: string;
	duration?: string;
	jwpmediaid?: string;
	is_live_stream?: boolean;
} {
	return (
		'mimeType' in item ||
		'duration' in item ||
		'jwpmediaid' in item ||
		'is_live_stream' in item
	);
}

/**
 * Determines the content type of an item
 * @param item Any content item to check
 */
export function getContentType(
	item: object,
): NonNullable<ContentQuery['mergedContent']>[number]['contentType'] {
	if (!item || typeof item !== 'object') {
		return 'article';
	}

	// Check if it's an image
	if (isImageObject(item)) {
		return 'image';
	}

	// Check if it's a video
	if (isVideoObject(item)) {
		return 'video';
	}

	// Check if it's an article
	if (isArticleObject(item)) {
		return 'article';
	}

	// Default to article if type cannot be determined
	return 'article';
}

export function getSafeAuthors(article: Article): User[] {
	return (article.authors as User[]) || [];
}

export function getSafeFeaturedImage(article: Article): string {
	return typeof article.featuredImage === 'string'
		? article.featuredImage
		: article.featuredImage?.url || '';
}
