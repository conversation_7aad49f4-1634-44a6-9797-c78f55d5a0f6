import { get } from '@vercel/edge-config';
import BloomFilters from 'bloom-filters';

/**
 * Edge Config utilities for managing the bloom filter
 */

const BLOOM_FILTER_KEY = 'bloomFilter';

/**
 * Retrieves the bloom filter from Vercel Edge Config
 * @returns Promise<ScalableBloomFilter | null>
 */
export async function getBloomFilterFromEdgeConfig(): Promise<BloomFilters.ScalableBloomFilter | null> {
	try {
		const bloomFilterString = await get('bloomFilter');
		const bloomFilterJson = JSON.parse(bloomFilterString as string);

		if (!bloomFilterString) {
			console.log('No bloom filter found in Edge Config');
			return null;
		}

		// Reconstruct the bloom filter from the stored JSON data
		const filter = BloomFilters.ScalableBloomFilter.fromJSON(bloomFilterJson);
		console.log('Successfully loaded bloom filter from Edge Config');
		return filter;
	} catch (error) {
		console.error('Error retrieving bloom filter from Edge Config:', error);
		return null;
	}
}

/**
 * Saves the bloom filter to Vercel Edge Config via direct API call
 * @param filter - The ScalableBloomFilter to save
 * @returns Promise<boolean> - Success status
 */
export async function saveBloomFilterToEdgeConfig(
	filter: BloomFilters.ScalableBloomFilter,
): Promise<boolean> {
	try {
		const filterData = filter.saveAsJSON();

		// Check if Edge Config is configured
		const edgeConfigId = process.env.EDGE_CONFIG_ID;
		const vercelToken = process.env.VERCEL_TOKEN;

		if (!edgeConfigId || !vercelToken) {
			console.error('Edge Config ID or Vercel token not configured');
			return false;
		}

		// Check if the bloom filter already exists to determine operation
		const existingFilter = await getBloomFilterFromEdgeConfig();
		const operation = existingFilter ? 'update' : 'create';

		console.log(
			`${operation === 'create' ? 'Creating' : 'Updating'} bloom filter in Edge Config`,
		);

		// Update Edge Config via Vercel API directly
		const response = await fetch(
			`https://api.vercel.com/v1/edge-config/${edgeConfigId}/items?teamId=team_sBvHJvi0k5vxLzjQlH279DCx`,
			{
				method: 'PATCH',
				headers: {
					Authorization: `Bearer ${vercelToken}`,
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					items: [
						{
							operation,
							key: BLOOM_FILTER_KEY,
							value: JSON.stringify(filterData),
						},
					],
				}),
			},
		);

		if (!response.ok) {
			const errorText = await response.text();
			console.error(
				`Failed to ${operation} Edge Config:`,
				response.status,
				errorText,
			);
			return false;
		}

		console.log(
			`Successfully ${operation === 'create' ? 'created' : 'updated'} bloom filter in Edge Config`,
		);
		return true;
	} catch (error) {
		console.error('Error saving bloom filter to Edge Config:', error);
		return false;
	}
}

/**
 * Checks if Edge Config is available and configured
 * @returns boolean
 */
export function isEdgeConfigAvailable(): boolean {
	return !!process.env.EDGE_CONFIG;
}
