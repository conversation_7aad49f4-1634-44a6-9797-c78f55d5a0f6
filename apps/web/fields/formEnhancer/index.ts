import type { Field } from 'payload';

/**
 * Form Enhancer Field
 * 
 * This is a hidden field that includes the AdminFormEnhancer component
 * to automatically handle draft state and form population issues.
 * 
 * Add this field to any collection that experiences form state problems.
 */
export const formEnhancerField: Field = {
	name: 'formEnhancer',
	type: 'ui',
	admin: {
		components: {
			Field: {
				path: '@/components/Payload/AdminFormEnhancer#AdminFormEnhancer',
				serverProps: {},
			},
		},
		hidden: true, // This field won't be visible in the admin UI
	},
};

/**
 * Create a form enhancer field with collection-specific props
 */
export const createFormEnhancerField = (collection: string): Field => ({
	name: 'formEnhancer',
	type: 'ui',
	admin: {
		components: {
			Field: {
				path: '@/components/Payload/AdminFormEnhancer#AdminFormEnhancer',
				serverProps: {
					collection,
				},
			},
		},
		hidden: true,
	},
});
