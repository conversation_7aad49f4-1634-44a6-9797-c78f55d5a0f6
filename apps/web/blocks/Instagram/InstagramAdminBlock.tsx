'use client';

import { FC, useMemo } from 'react';
import { useAllFormFields } from '@payloadcms/ui';
import { reduceFieldsToValues } from 'payload/shared';
import AdminBlock from '@/components/Payload/AdminBlock';
import { INSTAGRAM_URL_REGEX } from '@/blocks/Instagram/utils';
import { cn } from '@repo/ui/lib/utils';

const extractPostId = (url: string) => {
	const match = url.match(INSTAGRAM_URL_REGEX);
	return [match?.[1] || null, match?.[2] || null]; // [postType, postId]
};

const InstagramAdminBlock: FC = () => {
	const [fields] = useAllFormFields();
	const formData = reduceFieldsToValues(fields, true);
	const [postType, postId] = useMemo(
		() => extractPostId(formData.url || ''),
		[formData.url],
	);

	return (
		<AdminBlock name="Instagram">
			<div className="mb-2">
				<strong className="text-black-800">Post URL:</strong>{' '}
				<span className="text-gray-600">{formData.url || 'Not provided'}</span>
			</div>
			<div className="mb-2">
				<strong className="text-black-800">Embed Size:</strong>{' '}
				<span className="text-gray-600">
					{formData.size === 'hero' ? 'Hero (Full Width)' : 'Article (Small)'}
				</span>
			</div>
			{!(postType && postId) ? (
				<div className="text-red-600">Invalid Instagram URL</div>
			) : (
				<div className="mb-2 flex justify-center">
					<iframe
						className={cn(
							'h-60 w-full rounded-lg shadow-lg',
							formData.size === 'hero' ? 'max-w-full' : 'max-w-[320px]',
						)}
						src={`https://www.instagram.com/${postType}/${postId}/embed`}
						title="Instagram Preview"
						allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
						allowFullScreen
					></iframe>
				</div>
			)}
		</AdminBlock>
	);
};

export default InstagramAdminBlock;
