import { url, size } from '@/blocks/Instagram/field';
import type { Block } from 'payload';

const InstagramBlockConfig: Block = {
	slug: 'Instagram',
	interfaceName: 'InstagramBlockConfig',
	labels: {
		singular: 'Instagram Block',
		plural: 'Instagram Blocks',
	},
	fields: [url, size],
	admin: {
		components: {
			Block: {
				path: '@/blocks/Instagram/InstagramAdminBlock#default',
			},
		},
	},
};

export default InstagramBlockConfig;
