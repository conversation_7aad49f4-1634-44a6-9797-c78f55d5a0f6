import type { TextField, Validate } from 'payload';
import type { InstagramBlockConfig } from '@/payload-types';

export const INSTAGRAM_URL_REGEX = /instagram\.com\/(p|reel)\/([^/?#]+)/;

export const validateInstagramURL: Validate<
	string,
	unknown,
	InstagramBlockConfig,
	TextField
> = (value) => {
	if (!value) {
		return 'Instagram URL is required';
	}

	const match = value.match(INSTAGRAM_URL_REGEX);

	if (!match) {
		return 'Invalid Instagram URL';
	}
	return true;
};
