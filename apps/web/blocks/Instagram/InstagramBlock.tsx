import { FC } from 'react';
import { InstagramBlockConfig } from '@/payload-types';
import Script from 'next/script';
import { cn } from '@repo/ui/lib/utils';

const InstagramBlock: FC<InstagramBlockConfig> = ({ url, size, id }) => {
	return (
		<div key={id}>
			<blockquote
				className={cn(
					'instagram-media',
					'!min-w-auto !my-4 w-[calc(100%-2px)] !rounded-lg !border-0 !bg-transparent !shadow-lg',
					size === 'hero' ? '' : 'max-w-[538px]',
				)}
				data-instgrm-permalink={url}
				data-instgrm-version={14}
			></blockquote>
			<Script async src="https://www.instagram.com/embed.js"></Script>
		</div>
	);
};

export default InstagramBlock;
