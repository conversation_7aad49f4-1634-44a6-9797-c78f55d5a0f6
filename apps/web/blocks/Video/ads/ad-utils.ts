import { CTX } from '@/app/(web)/[code]/content/classic/utils/transformDsxArticle';
import { createLogger } from '@repo/logger';
import { getIabConfig } from '@/components/Helios/utils/iabConfig';
import {
	BaseEvent,
	ImaAdData,
	PrerollAdProps,
	adSystemAdSense,
	adSystemDfp,
} from './ad-types';

const logger = createLogger('AdUtils');

export const prerollAd = async ({
	player,
	user,
	ctx = {},
	isMobile = false,
}: PrerollAdProps): Promise<void> => {
	if (!player) return;

	if (user?.isUserPremium) {
		player.play();
		return;
	}

	const adTag = await getAdTag(ctx, isMobile);

	if (adTag) {
		// Play the ad
		player.playAd(adTag);
	} else {
		// Play the video
		player.play();
	}
};

const getAdTag = async (
	ctx: CTX,
	isMobile: boolean = false,
): Promise<string | null> => {
	if (!isHeliosVideoAvailable()) {
		return null;
	}

	const nctau = getActiveVideoNctau(ctx, isMobile);

	// Todo: Need to understand from where the iabSiteCategories are coming
	const iabConfig = getIabConfig();

	const iabCategoriesForActiveAsset = ctx.iab;
	const iabCategoriesForCurrentPageCode = {};
	const mergedIabPageCategories = mergeIabCategories(
		iabCategoriesForCurrentPageCode,
		iabCategoriesForActiveAsset,
	);

	const iabcUpdateParams = {
		page: mergedIabPageCategories,
		site: iabConfig.iabSiteCategories,
	};

	window.__Helios.emit('UPDATE_AD_UNIT_PATH', nctau);
	try {
		window.__Helios.modules?.iabc?.update?.(iabcUpdateParams);
	} catch (err) {
		logger.error('Error updating IABC parameters', err);
	}

	let adTag: string | null;

	try {
		adTag = await getVideoAdTagFromHelios();
	} catch (err) {
		adTag = null;
		logger.error('Error getting ad tag', err);
	}

	window.__Helios.emit('UPDATE_AD_UNIT_PATH', nctau);
	return adTag;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const mergeIabCategories = (c1: any, c2: any) => {
	const result = { ...c1 };

	for (const key in c2) {
		if (Array.isArray(c2[key])) {
			result[key] = [...(result[key] || []), ...c2[key]];
			result[key] = [...new Set(result[key])];
		} else if (typeof c2[key] === 'object' && c2[key] !== null) {
			result[key] = mergeIabCategories(result[key] || {}, c2[key]);
		} else {
			result[key] = c2[key];
		}
	}

	return result;
};

export const getActiveVideoNctau = (ctx: CTX, isMobile: boolean) => {
	const pageNetworkCode = '/7646';
	const pageDeviceAdUnit = isMobile ? 'mobile_smart_us' : 'web_weather_us';
	const activeVideoAdZone = ctx.collectionAdZone || ctx.adzone || 'video';
	const adsTest = false;
	return `${pageNetworkCode}/${adsTest ? 'test_' : ''}${pageDeviceAdUnit}/${activeVideoAdZone}`;
};

export const getVideoAdTagFromHelios = async () => {
	let bids: unknown[] = [];

	try {
		bids = await window?.__Helios.video.requestVideoBids();
	} catch (err) {
		logger.error('Error getting video bids', err);
	}

	return await window.__Helios.modules.videoTag.createAdTag(bids);
};

export const isHeliosVideoAvailable = () => {
	try {
		return (
			typeof window.__Helios?.video?.requestVideoBids === 'function' &&
			typeof window.__Helios?.modules?.videoTag?.createAdTag === 'function'
		);
	} catch {
		return false;
	}
};

export const getIsViewable = (event: BaseEvent): boolean => !!event.viewable;

/**
 * Add protocol to the url if it's missing.
 */
export const resolveProtocolRelativeUrl = (url: string, protocol = 'https') => {
	return url.replace(/^\/\//, `${protocol}://`);
};

/**
 * Parse cust_params string into a targeting map.
 */
export const parseCustParamsStringToTargetingMap = (
	str: string,
): Record<string, string[]> => {
	if (!str) return {};

	return str.split('&').reduce(
		(map, pair) => {
			const [key, value] = pair.split('=');
			if (key && value) {
				if (!map[key]) {
					map[key] = [];
				}
				map[key].push(value);
			}
			return map;
		},
		{} as Record<string, string[]>,
	);
};

export const getNewRelicVideoMetaData = (ctx: CTX | undefined) => {
	const { collectionId, videoIndex, videoId } = ctx || {};
	return {
		videoCount: videoIndex,
		videoId,
		collectionId,
	};
};

/**
 * Parse line item id out of IMA Ad Data.
 */
export const getLineItemId = (imaAdData: ImaAdData): string => {
	const { adSystem, adId, adWrapperSystems, adWrapperIds } = {
		adSystem: '',
		adId: '',
		adWrapperSystems: [],
		adWrapperIds: [],
		...imaAdData,
	};

	if (isGoogleAdSystem(adSystem)) {
		return adId;
	}

	const idx = adWrapperSystems.findIndex(isGoogleAdSystem);

	return idx >= 0 && idx < adWrapperIds.length && adWrapperIds[idx]
		? adWrapperIds[idx]
		: '';
};

export const isGoogleAdSystem = (adSystem: string): boolean => {
	return adSystem === adSystemDfp || adSystem === adSystemAdSense;
};
