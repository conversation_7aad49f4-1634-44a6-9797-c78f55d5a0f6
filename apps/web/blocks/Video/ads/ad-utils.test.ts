import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest';
import {
	prerollAd,
	getActiveVideoNctau,
	getVideoAdTagFromHelios,
	isHeliosVideoAvailable,
} from './ad-utils';
import type { JWPlayerInstance } from '@/components/JWPlayer/types/player';
import { CTX } from '@/app/(web)/[code]/content/classic/utils/transformDsxArticle';
import * as iabConfigModule from '@/components/Helios/HeliosConfig/iabConfig';
import { HeliosType } from '@/components/Helios';
import { User } from '@/user/hooks/useUser';

// Use vi.hoisted() for mock variables to ensure they're available when mocks are hoisted
const mockLogger = vi.hoisted(() => ({
	error: vi.fn(),
	info: vi.fn(),
	warn: vi.fn(),
	lifecycle: vi.fn(),
}));

const mockCreateLogger = vi.hoisted(() => vi.fn(() => mockLogger));

// Mock JW Player instance
const mockPlayerInstance = {
	play: vi.fn(),
	playAd: vi.fn(),
} as unknown as JWPlayerInstance;

vi.mock('@repo/logger', () => ({
	createLogger: mockCreateLogger,
}));

describe('Video Ad Utils', () => {
	// Create simple mock functions
	const mockEmit = vi.fn();
	const mockOn = vi.fn();
	const mockRequestVideoBids = vi.fn();
	const mockCreateAdTag = vi.fn();
	const mockIabcUpdate = vi.fn();

	// Mock user state
	const mockUser: User = {
		userID: '',
		isUserLoggedIn: false,
		subscriptionTier: 0,
		isUserPremium: false,
	};

	// Setup mock Helios
	const setupMockHelios = () => {
		// Reset mock functions
		mockEmit.mockReset();
		mockOn.mockReset();
		mockRequestVideoBids.mockReset();
		mockCreateAdTag.mockReset();
		mockIabcUpdate.mockReset();

		// Set default resolved values
		mockRequestVideoBids.mockResolvedValue([]);
		mockCreateAdTag.mockResolvedValue('');

		// Create mock Helios object with type assertion
		const helios = {
			emit: mockEmit,
			on: mockOn,
			video: {
				requestVideoBids: mockRequestVideoBids,
			},
			modules: {
				iabc: {
					update: mockIabcUpdate,
				},
				videoTag: {
					createAdTag: mockCreateAdTag,
				},
			},
		} as unknown as HeliosType;

		// Set global Helios
		Object.defineProperty(window, '__Helios', {
			value: helios,
			writable: true,
			configurable: true,
		});

		return helios;
	};

	beforeEach(() => {
		vi.clearAllMocks();
		setupMockHelios();

		// Mock getIabConfig to return the expected value with all required properties
		vi.spyOn(iabConfigModule, 'getIabConfig').mockReturnValue({
			additionalConfig: {
				ortb2Site: {
					keywords: 'weather,science',
				},
			},
			iabSiteCategories: {
				v1: ['IAB15-10_Weather'],
				v3: ['390_Weather'],
			},
			iabCategoriesByPageCode: {
				safety: {
					v1: ['IAB10_Home & Garden'],
					v3: ['281_Home Security'],
				},
				'air-quality': {
					v1: ['IAB15-10_Weather'],
					v3: ['300_Air Quality'],
				},
				'cold-and-flu': {
					v3: ['315_Cold and Flu'],
				},
				'allergy-forecast': {
					v3: ['288_Allergies'],
				},
				'activity-hub-golf': {
					v1: ['IAB17-15_Golf'],
					v3: ['512_Golf'],
				},
				'activity-hub-hiking': {
					v3: ['1000'],
				},
				'activity-hub-tennis': {
					v1: ['IAB17-40_Tennis'],
					v3: ['539_Tennis'],
				},
				'activity-hub-camping': {
					v1: ['IAB20-9_Camping'],
					v3: ['677_Camping'],
				},
				'activity-hub-cycling': {
					v1: ['IAB17-3_Bicycling'],
					v3: ['492_Cycling'],
				},
				'activity-hub-running': {
					v1: ['IAB17-30_Running/Jogging'],
					v3: ['227_Running and Jogging'],
				},
				'activity-hub-gardening': {
					v1: ['IAB10-4_Gardening'],
					v3: ['275_Gardening'],
				},
				'home,radar,monthly,10day-forecast,today-forecast,hourly-forecast,weekend-forecast':
					{
						v1: ['IAB15-10_Weather'],
						v3: ['390_Weather'],
					},
			},
		});
	});

	afterEach(() => {
		vi.resetAllMocks();
	});

	describe('prerollAd', () => {
		test('plays ad when ad tag is available', async () => {
			// Mock ad tag
			mockCreateAdTag.mockResolvedValue('https://example.com/ad-tag');

			const ctx: CTX = {
				adzone: 'weather/video',
			};

			await prerollAd({ player: mockPlayerInstance, user: mockUser, ctx });

			// Should play ad
			expect(mockPlayerInstance.playAd).toHaveBeenCalledWith(
				'https://example.com/ad-tag',
			);
			expect(mockPlayerInstance.play).not.toHaveBeenCalled();
		});

		test('plays video when ad tag is not available', async () => {
			// Mock null ad tag
			mockCreateAdTag.mockResolvedValue(null);

			const ctx: CTX = {
				adzone: 'weather/video',
			};

			await prerollAd({ player: mockPlayerInstance, user: mockUser, ctx });

			// Should play video
			expect(mockPlayerInstance.play).toHaveBeenCalled();
			expect(mockPlayerInstance.playAd).not.toHaveBeenCalled();
		});

		test('plays video when user is premium', async () => {
			const premiumUser: User = {
				...mockUser,
				isUserPremium: true,
			};

			const ctx: CTX = {
				adzone: 'weather/video',
			};

			await prerollAd({ player: mockPlayerInstance, user: premiumUser, ctx });

			// Should play video without ads for premium users
			expect(mockPlayerInstance.play).toHaveBeenCalled();
			expect(mockPlayerInstance.playAd).not.toHaveBeenCalled();

			// Should not attempt to get ad tag
			expect(mockRequestVideoBids).not.toHaveBeenCalled();
		});

		test('does nothing when player is not provided', async () => {
			await prerollAd({
				player: undefined as unknown as JWPlayerInstance,
				user: mockUser,
			});

			// Should not call any methods
			expect(mockRequestVideoBids).not.toHaveBeenCalled();
			expect(mockCreateAdTag).not.toHaveBeenCalled();
		});

		test('handles errors during ad tag retrieval', async () => {
			// Mock error during bid request
			mockRequestVideoBids.mockRejectedValue(new Error('Failed to get bids'));
			// Also make createAdTag throw an error to trigger the debugLogger.error call
			mockCreateAdTag.mockRejectedValue(new Error('Failed to create ad tag'));

			const ctx: CTX = {
				adzone: 'weather/video',
			};

			await prerollAd({ player: mockPlayerInstance, user: mockUser, ctx });

			// Should play video as fallback
			expect(mockPlayerInstance.play).toHaveBeenCalled();
			expect(mockPlayerInstance.playAd).not.toHaveBeenCalled();

			// Should log error
			expect(mockLogger.error).toHaveBeenCalledWith(
				'Error getting ad tag',
				expect.any(Error),
			);
		});
	});

	describe('getActiveVideoNctau', () => {
		test('returns correct ad unit path with collectionAdZone', () => {
			const ctx: CTX = {
				collectionAdZone: 'weather/collection-ad-zone-video',
			};

			const result = getActiveVideoNctau(ctx);

			expect(result).toBe(
				'/7646/web_weather_us/weather/collection-ad-zone-video',
			);
		});

		test('returns correct ad unit path with adzone', () => {
			const ctx: CTX = {
				adzone: 'weather/video',
			};

			const result = getActiveVideoNctau(ctx);

			expect(result).toBe('/7646/web_weather_us/weather/video');
		});

		test('returns default video adzone when adzone is not provided', () => {
			const ctx: CTX = {};

			const result = getActiveVideoNctau(ctx);

			expect(result).toBe('/7646/web_weather_us/video');
		});
	});

	describe('getVideoAdTagFromHelios', () => {
		test('returns ad tag from Helios', async () => {
			// Mock bids and ad tag
			mockRequestVideoBids.mockResolvedValue(['bid1', 'bid2']);
			mockCreateAdTag.mockResolvedValue('https://example.com/ad-tag');

			const result = await getVideoAdTagFromHelios();

			expect(result).toBe('https://example.com/ad-tag');
			expect(mockRequestVideoBids).toHaveBeenCalled();
			expect(mockCreateAdTag).toHaveBeenCalledWith(['bid1', 'bid2']);
		});

		test('handles errors during bid request', async () => {
			// Mock error during bid request
			mockRequestVideoBids.mockRejectedValue(new Error('Failed to get bids'));
			mockCreateAdTag.mockResolvedValue('https://example.com/ad-tag');

			const result = await getVideoAdTagFromHelios();

			expect(result).toBe('https://example.com/ad-tag');
			expect(mockCreateAdTag).toHaveBeenCalledWith([]);

			// Verify error was logged with logger instead of console
			expect(mockLogger.error).toHaveBeenCalledWith(
				'Error getting video bids',
				expect.any(Error),
			);
		});
	});

	describe('isHeliosVideoAvailable', () => {
		test('returns true when Helios video functions are available', () => {
			// Helios is already mocked with required functions
			const result = isHeliosVideoAvailable();

			expect(result).toBe(true);
		});

		test('returns false when Helios video functions are not available', () => {
			// Create a new Helios without video functions
			const helios = setupMockHelios();

			// Remove video property
			Object.defineProperty(helios, 'video', {
				value: null,
				configurable: true,
			});

			const result = isHeliosVideoAvailable();

			expect(result).toBe(false);
		});

		test('returns false when Helios videoTag functions are not available', () => {
			// Create a new Helios without videoTag functions
			const helios = setupMockHelios();

			// Remove videoTag property from modules
			const modules = helios.modules as { videoTag?: unknown; iabc?: unknown };
			modules.videoTag = null;

			const result = isHeliosVideoAvailable();

			expect(result).toBe(false);
		});

		test('handles errors gracefully', () => {
			// Make accessing Helios throw an error
			Object.defineProperty(window, '__Helios', {
				get: () => {
					throw new Error('Cannot access Helios');
				},
				configurable: true,
			});

			const result = isHeliosVideoAvailable();

			expect(result).toBe(false);

			// Restore Helios
			setupMockHelios();
		});
	});

	describe('Integration Test: Full Video Ad Flow', () => {
		test('handles complete ad flow with IAB categories', async () => {
			// Setup mocks
			mockRequestVideoBids.mockResolvedValue(['bid1', 'bid2']);
			mockCreateAdTag.mockResolvedValue('https://example.com/ad-tag');

			// Create context with IAB categories
			const ctx: CTX = {
				adzone: 'weather/video',
				iab: {
					v1: ['IAB15-10_Weather', 'IAB15_Science'],
					v2: ['390_Weather', '464_Science'],
				},
			};

			// Execute preroll ad
			await prerollAd({ player: mockPlayerInstance, user: mockUser, ctx });

			// Verify ad unit path was updated
			expect(mockEmit).toHaveBeenCalledWith(
				'UPDATE_AD_UNIT_PATH',
				'/7646/web_weather_us/weather/video',
			);

			// Verify IAB categories were updated
			expect(mockIabcUpdate).toHaveBeenCalledWith(
				expect.objectContaining({
					page: expect.objectContaining({
						v1: expect.arrayContaining(['IAB15-10_Weather', 'IAB15_Science']),
						v2: expect.arrayContaining(['390_Weather', '464_Science']),
					}),
					site: expect.objectContaining({
						v1: expect.arrayContaining(['IAB15-10_Weather']),
						v3: expect.arrayContaining(['390_Weather']),
					}),
				}),
			);

			// Verify bids were requested
			expect(mockRequestVideoBids).toHaveBeenCalled();

			// Verify ad tag was created with bids
			expect(mockCreateAdTag).toHaveBeenCalledWith(['bid1', 'bid2']);

			// Verify ad was played
			expect(mockPlayerInstance.playAd).toHaveBeenCalledWith(
				'https://example.com/ad-tag',
			);
		});

		test('handles error recovery during ad flow', async () => {
			// Setup mocks to simulate errors
			mockRequestVideoBids.mockRejectedValue(new Error('Failed to get bids'));
			mockIabcUpdate.mockImplementation(() => {
				throw new Error('Failed to update IAB categories');
			});
			// Also make createAdTag throw an error to trigger the debugLogger.error call
			mockCreateAdTag.mockRejectedValue(new Error('Failed to create ad tag'));

			// Create context
			const ctx: CTX = {
				adzone: 'weather/video',
			};

			// Execute preroll ad - should not throw despite errors
			await expect(
				prerollAd({ player: mockPlayerInstance, user: mockUser, ctx }),
			).resolves.not.toThrow();

			// Verify errors were logged
			expect(mockLogger.error).toHaveBeenCalledWith(
				'Error updating IABC parameters',
				expect.any(Error),
			);

			expect(mockLogger.error).toHaveBeenCalledWith(
				'Error getting ad tag',
				expect.any(Error),
			);

			// Verify video was played as fallback
			expect(mockPlayerInstance.play).toHaveBeenCalled();
		});
	});
});
