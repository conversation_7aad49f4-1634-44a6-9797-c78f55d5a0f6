import { describe, test, expect, vi, beforeEach } from 'vitest';
import {
	metricEvents,
	resetEventAttrs,
	resetEventFlags,
} from './metric-events';
import type { JWPlayerInstance } from '@/components/JWPlayer/types/player';
import type { PlaylistItem } from '@/components/JWPlayer/types/playlist';

describe('metricEvents', () => {
	// Common mocks
	let mockPlayer: JWPlayerInstance;
	let mockTrackVideoEvent: ReturnType<typeof vi.fn>;
	let mockEvent: Record<string, unknown>;
	let mockConsoleDebug: ReturnType<typeof vi.spyOn>;
	let mockConsoleError: ReturnType<typeof vi.spyOn>;
	let mockCurrentVideo: PlaylistItem;

	const user = {
		userID: '',
		isUserLoggedIn: false,
		subscriptionTier: 0,
		isUserPremium: false,
	};

	beforeEach(() => {
		// Reset mocks before each test
		vi.resetAllMocks();

		// Mock console methods
		mockConsoleDebug = vi.spyOn(console, 'debug').mockImplementation(() => {});
		mockConsoleError = vi.spyOn(console, 'error').mockImplementation(() => {});

		resetEventAttrs();
		resetEventFlags();

		// Mock current video
		mockCurrentVideo = {
			file: 'test-video.mp4',
			title: 'Test Video',
			custom: { ctx: { id: 'd7acdcb2-46c9-46de-a1cc-76c7a695eaf5' } },
		};

		// Mock player
		mockPlayer = {
			getPlaylistItem: vi.fn(() => mockCurrentVideo),
			getPlaylistIndex: vi.fn(() => 0),
			getDuration: vi.fn(),
			play: vi.fn(),
			pause: vi.fn(),
			stop: vi.fn(),
			getConfig: vi.fn(),
			getPosition: vi.fn(),
			getAdBlock: vi.fn(),
			getMute: vi.fn(),
			on: vi.fn(),
			off: vi.fn(),
			remove: vi.fn(),
			setup: vi.fn(),
			setConfig: vi.fn(),
			playAd: vi.fn(),
		};

		// Mock trackVideoEvent function
		mockTrackVideoEvent = vi.fn();

		// Mock event object
		mockEvent = {};
	});

	test('ready event should add beforeunload listener that sends video-end event when appropriate', async () => {
		// Find the ready event handler
		const readyEvent = metricEvents.find(
			(event) => event.eventName === 'ready',
		);
		expect(readyEvent).toBeDefined();

		// Mock window.addEventListener
		const originalAddEventListener = window.addEventListener;
		const addEventListenerMock = vi.fn();
		window.addEventListener = addEventListenerMock;

		// Call the ready event handler
		if (readyEvent) {
			readyEvent.callback({
				event: {},
				player: mockPlayer,
				isFirstPlayRef: { current: true },
				user,
				trackVideoEvent: mockTrackVideoEvent,
			});
		}

		// Verify addEventListener was called with 'beforeunload'
		expect(addEventListenerMock).toHaveBeenCalledWith(
			'beforeunload',
			expect.any(Function),
		);

		// Get the beforeunload handler function
		const beforeunloadHandler = addEventListenerMock.mock.calls[0]?.[1];

		// Case 1: Video has not started - should not send video-end
		beforeunloadHandler();
		expect(mockTrackVideoEvent).not.toHaveBeenCalled();

		// Case 2: Video has started but not ended - should send video-end
		// First, trigger firstFrame to set videoStarted flag
		const firstFrameEvent = metricEvents.find(
			(event) => event.eventName === 'firstFrame',
		);
		expect(firstFrameEvent).toBeDefined();

		firstFrameEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Reset mock to verify next calls
		mockTrackVideoEvent.mockReset();

		// Now trigger beforeunload
		beforeunloadHandler();

		// Verify video-end was sent
		expect(mockTrackVideoEvent).toHaveBeenCalledWith({
			eventName: 'video-end',
			currentVideo: mockCurrentVideo,
			jwPlayer: mockPlayer,
			eventAttributes: expect.objectContaining({
				adtitle: '',
				errorMessage: '',
				contentWatchedSeconds: 0,
				playReason: '',
				videoView: 1,
			}),
		});

		// Case 3: Video has already ended - should not send video-end again
		mockTrackVideoEvent.mockReset();
		beforeunloadHandler();
		expect(mockTrackVideoEvent).not.toHaveBeenCalled();

		// Restore original addEventListener
		window.addEventListener = originalAddEventListener;
	});

	test('viewable event should update videoView value', async () => {
		// Find the viewable event handler
		const viewableEvent = metricEvents.find(
			(event) => event.eventName === 'viewable',
		);
		expect(viewableEvent).toBeDefined();

		// Set up mock event data
		mockEvent = { viewable: 1 };

		// Call the event handler
		viewableEvent!.callback({
			event: mockEvent,
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// We can't directly test the videoView variable as it's private to the module,
		// but we can test its effect in subsequent events

		// Find the playAttempt event handler
		const playAttemptEvent = metricEvents.find(
			(event) => event.eventName === 'playAttempt',
		);
		expect(playAttemptEvent).toBeDefined();

		// Call the playAttempt handler
		playAttemptEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify trackVideoEvent is called with correct parameters including videoView
		expect(mockTrackVideoEvent).toHaveBeenCalledWith(
			expect.objectContaining({
				eventName: 'video-attempt',
				currentVideo: mockCurrentVideo,
				jwPlayer: mockPlayer,
				eventAttributes: expect.objectContaining({
					videoView: 1,
				}),
			}),
		);
	});

	test('adImpression event should track video-attempt and video-played events', async () => {
		// Find the adImpression event handler
		const adImpressionEvent = metricEvents.find(
			(event) => event.eventName === 'adImpression',
		);
		expect(adImpressionEvent).toBeDefined();

		// Set up mock event data
		mockEvent = { adtitle: 'Test Ad Impression' };

		// Call the event handler
		adImpressionEvent!.callback({
			event: mockEvent,
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify console.debug is called
		expect(mockConsoleDebug).toHaveBeenCalledWith(
			'JW Player callback - adImpression eventSentFlags:',
			expect.any(Object),
		);

		// Verify trackVideoEvent is called with correct parameters for video-attempt
		expect(mockTrackVideoEvent).toHaveBeenCalledWith({
			eventName: 'video-attempt',
			currentVideo: mockCurrentVideo,
			jwPlayer: mockPlayer,
			eventAttributes: {
				adtitle: 'Test Ad Impression',
				errorMessage: '',
				playReason: '',
				videoView: 1,
			},
		});

		// Verify trackVideoEvent is called with correct parameters for video-played
		expect(mockTrackVideoEvent).toHaveBeenCalledWith({
			eventName: 'video-played',
			currentVideo: mockCurrentVideo,
			jwPlayer: mockPlayer,
			eventAttributes: {
				adtitle: 'Test Ad Impression',
				errorMessage: '',
				playReason: '',
				videoView: 1,
			},
		});

		// Verify trackVideoEvent is not called again for these events (flags prevent second call)
		expect(mockTrackVideoEvent).toHaveBeenCalledTimes(2);
	});

	test('playAttempt event should track video-attempt event', async () => {
		// Find the playAttempt event handler
		const playAttemptEvent = metricEvents.find(
			(event) => event.eventName === 'playAttempt',
		);
		expect(playAttemptEvent).toBeDefined();

		// Call the event handler
		playAttemptEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify console.debug is called
		expect(mockConsoleDebug).toHaveBeenCalledWith(
			'JW Player callback - playAttempt eventSentFlags:',
			expect.any(Object),
		);

		// Verify trackVideoEvent is called with correct parameters
		expect(mockTrackVideoEvent).toHaveBeenCalledWith({
			eventName: 'video-attempt',
			currentVideo: mockCurrentVideo,
			jwPlayer: mockPlayer,
			eventAttributes: expect.objectContaining({
				adtitle: '',
				errorMessage: '',
				playReason: '',
				videoView: 1,
			}),
		});

		// Call the event handler again
		playAttemptEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});
	});

	test('firstFrame event should track video-played and video-started events', async () => {
		// Find the firstFrame event handler
		const firstFrameEvent = metricEvents.find(
			(event) => event.eventName === 'firstFrame',
		);
		expect(firstFrameEvent).toBeDefined();

		// Call the event handler
		firstFrameEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify console.debug is called
		expect(mockConsoleDebug).toHaveBeenCalledWith(
			'JW Player callback - firstFrame eventSentFlags:',
			expect.any(Object),
		);

		// Verify trackVideoEvent is called with correct parameters for video-played
		expect(mockTrackVideoEvent).toHaveBeenCalledWith(
			expect.objectContaining({
				eventName: 'video-played',
				currentVideo: mockCurrentVideo,
				jwPlayer: mockPlayer,
				eventAttributes: expect.objectContaining({
					adtitle: '',
					errorMessage: '',
					playReason: '',
					videoView: 1,
				}),
			}),
		);

		// Verify trackVideoEvent is called with correct parameters for video-started
		expect(mockTrackVideoEvent).toHaveBeenCalledWith(
			expect.objectContaining({
				eventName: 'video-started',
				currentVideo: mockCurrentVideo,
				jwPlayer: mockPlayer,
				eventAttributes: expect.objectContaining({
					adtitle: '',
					errorMessage: '',
					playReason: '',
					videoView: 1,
				}),
			}),
		);
	});

	test('complete event should track video-completed event', async () => {
		// Find the complete event handler
		const completeEvent = metricEvents.find(
			(event) => event.eventName === 'complete',
		);
		expect(completeEvent).toBeDefined();

		// Call the event handler
		completeEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify console.debug is called
		expect(mockConsoleDebug).toHaveBeenCalledWith(
			'JW Player callback - complete eventSentFlags:',
			expect.any(Object),
		);

		// Verify trackVideoEvent is called with correct parameters
		expect(mockTrackVideoEvent).toHaveBeenCalledWith({
			eventName: 'video-completed',
			currentVideo: mockCurrentVideo,
			jwPlayer: mockPlayer,
			eventAttributes: expect.objectContaining({
				adtitle: '',
				errorMessage: '',
				playReason: '',
				videoView: 1,
			}),
		});
	});

	test('adStarted event should track ad-started event and update adtitle', async () => {
		// Find the adStarted event handler
		const adStartedEvent = metricEvents.find(
			(event) => event.eventName === 'adStarted',
		);
		expect(adStartedEvent).toBeDefined();

		// Set up mock event data
		mockEvent = { adtitle: 'Test Ad' };

		// Call the event handler
		adStartedEvent!.callback({
			event: mockEvent,
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify console.debug is called
		expect(mockConsoleDebug).toHaveBeenCalledWith(
			'JW Player callback - adStarted eventSentFlags:',
			expect.any(Object),
		);

		// Verify trackVideoEvent is called with correct parameters
		expect(mockTrackVideoEvent).toHaveBeenCalledWith({
			eventName: 'ad-started',
			currentVideo: mockCurrentVideo,
			jwPlayer: mockPlayer,
			eventAttributes: expect.objectContaining({
				adtitle: 'Test Ad',
				errorMessage: '',
				playReason: '',
				videoView: 1,
			}),
		});
	});

	test('adSkipped event should track ad-skipped event', async () => {
		// Find the adSkipped event handler
		const adSkippedEvent = metricEvents.find(
			(event) => event.eventName === 'adSkipped',
		);
		expect(adSkippedEvent).toBeDefined();

		// Call the event handler
		adSkippedEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify trackVideoEvent is called with correct parameters
		expect(mockTrackVideoEvent).toHaveBeenCalledWith({
			eventName: 'ad-skipped',
			currentVideo: mockCurrentVideo,
			jwPlayer: mockPlayer,
			eventAttributes: expect.objectContaining({
				adtitle: '',
				errorMessage: '',
				playReason: '',
				videoView: 1,
			}),
		});
	});

	test('adComplete event should track ad-complete event and update adtitle', async () => {
		// Find the adComplete event handler
		const adCompleteEvent = metricEvents.find(
			(event) => event.eventName === 'adComplete',
		);
		expect(adCompleteEvent).toBeDefined();

		// Set up mock event data
		mockEvent = { adtitle: 'Test Ad Complete' };

		// Call the event handler
		adCompleteEvent!.callback({
			event: mockEvent,
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify console.debug is called
		expect(mockConsoleDebug).toHaveBeenCalledWith(
			'JW Player callback - adComplete eventSentFlags:',
			expect.any(Object),
		);

		// Verify trackVideoEvent is called with correct parameters
		expect(mockTrackVideoEvent).toHaveBeenCalledWith({
			eventName: 'ad-complete',
			currentVideo: mockCurrentVideo,
			jwPlayer: mockPlayer,
			eventAttributes: expect.objectContaining({
				adtitle: 'Test Ad Complete',
				errorMessage: '',
				playReason: '',
				videoView: 1,
			}),
		});
	});

	test('adError event should log error', async () => {
		// Find the adError event handler
		const adErrorEvent = metricEvents.find(
			(event) => event.eventName === 'adError',
		);
		expect(adErrorEvent).toBeDefined();

		// Set up mock event data
		mockEvent = { message: 'Ad error occurred' };

		// Call the event handler
		adErrorEvent!.callback({
			event: mockEvent,
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify console.error is called
		expect(mockConsoleError).toHaveBeenCalledWith('Video ad error:', mockEvent);
	});

	test('error event should log error and update errorMessage', async () => {
		// Find the error event handler
		const errorEvent = metricEvents.find(
			(event) => event.eventName === 'error',
		);
		expect(errorEvent).toBeDefined();

		// Set up mock event data
		mockEvent = { message: 'Video player error occurred' };

		// Call the event handler
		errorEvent!.callback({
			event: mockEvent,
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify console.error is called
		expect(mockConsoleError).toHaveBeenCalledWith(
			'Video Player error:',
			mockEvent,
		);

		// Find another event handler to verify errorMessage was updated
		const firstFrameEvent = metricEvents.find(
			(event) => event.eventName === 'firstFrame',
		);
		expect(firstFrameEvent).toBeDefined();

		// Call the firstFrameEvent event handler
		firstFrameEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify trackVideoEvent is called with updated errorMessage
		expect(mockTrackVideoEvent).toHaveBeenCalledWith(
			expect.objectContaining({
				eventAttributes: expect.objectContaining({
					errorMessage: 'Video player error occurred',
				}),
			}),
		);
	});

	test('resetEventFlags should reset all event flags', async () => {
		// First trigger some events to set flags
		const playAttemptEvent = metricEvents.find(
			(event) => event.eventName === 'playAttempt',
		);
		const firstFrameEvent = metricEvents.find(
			(event) => event.eventName === 'firstFrame',
		);

		// Call playAttempt to set videoAttempt flag
		playAttemptEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Call firstFrame to set videoPlayed and videoStarted flags
		firstFrameEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify trackVideoEvent was called 3 times (video-attempt, video-played, video-started)
		expect(mockTrackVideoEvent).toHaveBeenCalledTimes(3);

		// Reset mock to verify next calls
		mockTrackVideoEvent.mockReset();

		// Call firstFrame again - should not trigger event due to flags
		firstFrameEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify trackVideoEvent was not called
		expect(mockTrackVideoEvent).not.toHaveBeenCalled();

		// Now trigger a new playAttempt which should reset flags if there was no adImpression
		// We need to simulate a new video by changing the mock
		(mockPlayer.getPlaylistItem as ReturnType<typeof vi.fn>).mockImplementation(
			() => ({
				...mockCurrentVideo,
				title: 'New Test Video',
			}),
		);

		// Call playAttempt to reset flags and trigger event
		playAttemptEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify trackVideoEvent was called again
		expect(mockTrackVideoEvent).toHaveBeenCalledTimes(1);
	});

	test('time event should update contentWatchedSeconds correctly', async () => {
		// Find the first frame event handler
		const firstFrameEvent = metricEvents.find(
			(event) => event.eventName === 'firstFrame',
		);
		// Find the time event handler
		const timeEvent = metricEvents.find((event) => event.eventName === 'time');

		expect(firstFrameEvent).toBeDefined();
		expect(timeEvent).toBeDefined();

		// Set up mock event data with position
		mockEvent = { position: 10 };

		// Call firstFrame to set videoPlayed and videoStarted flags
		firstFrameEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Call the event handler
		timeEvent!.callback({
			event: mockEvent,
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Update position again to simulate video progressing
		mockEvent = { position: 15 };

		// Call the event handler again
		timeEvent!.callback({
			event: mockEvent,
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Find the complete event handler to verify contentWatchedSeconds is included
		const completeEvent = metricEvents.find(
			(event) => event.eventName === 'complete',
		);
		expect(completeEvent).toBeDefined();

		// Call the complete event handler
		completeEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		expect(mockTrackVideoEvent).toHaveBeenCalledWith(
			expect.objectContaining({
				eventName: 'video-end',
				currentVideo: mockCurrentVideo,
				jwPlayer: mockPlayer,
				eventAttributes: expect.objectContaining({
					contentWatchedSeconds: 15, // 15 - 0 (initial) + 0 (previous)
					adtitle: '',
					errorMessage: '',
					playReason: '',
					videoView: 1,
				}),
			}),
		);
	});

	test('seek event should update previousTimePosition correctly', async () => {
		// Find the first frame event handler
		const firstFrameEvent = metricEvents.find(
			(event) => event.eventName === 'firstFrame',
		);
		// Find the seek event handler
		const seekEvent = metricEvents.find((event) => event.eventName === 'seek');

		expect(firstFrameEvent).toBeDefined();
		expect(seekEvent).toBeDefined();

		// Set up mock event data with offset
		mockEvent = { offset: 30 };

		// Call firstFrame to set videoPlayed and videoStarted flags
		firstFrameEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Call the seek event handler
		seekEvent!.callback({
			event: mockEvent,
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Find the time event handler
		const timeEvent = metricEvents.find((event) => event.eventName === 'time');
		expect(timeEvent).toBeDefined();

		// Set up mock event data with position after seek
		mockEvent = { position: 35 };

		// Call the time event handler
		timeEvent!.callback({
			event: mockEvent,
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Find the complete event handler to verify contentWatchedSeconds is calculated correctly
		const completeEvent = metricEvents.find(
			(event) => event.eventName === 'complete',
		);
		expect(completeEvent).toBeDefined();

		// Call the complete event handler
		completeEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify trackVideoEvent is called with video-end event that includes contentWatchedSeconds
		// Should be 5 (35 - 30) since we seeked to position 30 and then watched until position 35
		expect(mockTrackVideoEvent).toHaveBeenCalledWith(
			expect.objectContaining({
				eventName: 'video-end',
				currentVideo: mockCurrentVideo,
				jwPlayer: mockPlayer,
				eventAttributes: expect.objectContaining({
					contentWatchedSeconds: 5,
				}),
			}),
		);
	});

	test('resetEventFlags should reset contentWatchedSeconds and previousTimePosition', async () => {
		// Find the first frame event handler
		const firstFrameEvent = metricEvents.find(
			(event) => event.eventName === 'firstFrame',
		);
		// Find the time event handler
		const timeEvent = metricEvents.find((event) => event.eventName === 'time');
		expect(timeEvent).toBeDefined();
		expect(firstFrameEvent).toBeDefined();

		// Set up mock event data with position
		mockEvent = { position: 10 };

		// Call the time event handler to set contentWatchedSeconds
		timeEvent!.callback({
			event: mockEvent,
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Reset event flags
		resetEventFlags();

		// Call firstFrame to set videoPlayed and videoStarted flags
		firstFrameEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Set up mock event data with new position
		mockEvent = { position: 5 };

		// Call the time event handler again
		timeEvent!.callback({
			event: mockEvent,
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Find the complete event handler
		const completeEvent = metricEvents.find(
			(event) => event.eventName === 'complete',
		);
		expect(completeEvent).toBeDefined();

		// Call the complete event handler
		completeEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify trackVideoEvent is called with video-end event that includes contentWatchedSeconds
		// Should be 5 (5 - 0) since we reset the counters
		expect(mockTrackVideoEvent).toHaveBeenCalledWith(
			expect.objectContaining({
				eventName: 'video-end',
				currentVideo: mockCurrentVideo,
				jwPlayer: mockPlayer,
				eventAttributes: expect.objectContaining({
					contentWatchedSeconds: 5,
				}),
			}),
		);
	});

	test('playlistItem event should send video-end event when skipping an unfinished video', async () => {
		// Find the playlistItem event handler
		const playlistItemEvent = metricEvents.find(
			(event) => event.eventName === 'playlistItem',
		);
		expect(playlistItemEvent).toBeDefined();

		// Find the firstFrame event handler to set videoStarted flag
		const firstFrameEvent = metricEvents.find(
			(event) => event.eventName === 'firstFrame',
		);
		expect(firstFrameEvent).toBeDefined();

		// Create a mock for the previous video
		const mockPreviousVideo = {
			file: 'previous-video.mp4',
			title: 'Previous Video',
			custom: { ctx: { id: 'previous-video-id' } },
		};

		// Set up mock for getPlaylistIndex and getPlaylistItem
		mockPlayer.getPlaylistIndex = vi.fn(() => 1); // Current index is 1
		mockPlayer.getPlaylistItem = vi.fn((index) => {
			// Return previous video when index is 0, current video otherwise
			return index === 0 ? mockPreviousVideo : mockCurrentVideo;
		});

		// Case 1: Video has not started - should not send video-end
		playlistItemEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify trackVideoEvent was not called
		expect(mockTrackVideoEvent).not.toHaveBeenCalled();

		// Case 2: Video has started but not completed - should send video-end
		// Call firstFrame to set videoStarted flag
		firstFrameEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Reset mock to verify next calls
		mockTrackVideoEvent.mockReset();

		// Set up some content watched seconds
		const timeEvent = metricEvents.find((event) => event.eventName === 'time');
		expect(timeEvent).toBeDefined();
		mockEvent = { position: 30 };
		timeEvent!.callback({
			event: mockEvent,
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Now trigger playlistItem event
		playlistItemEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify video-end was sent with the previous video
		expect(mockTrackVideoEvent).toHaveBeenCalledWith({
			eventName: 'video-end',
			currentVideo: mockPreviousVideo,
			jwPlayer: mockPlayer,
			eventAttributes: expect.objectContaining({
				adtitle: '',
				errorMessage: '',
				contentWatchedSeconds: 30,
				playReason: '',
				videoView: 1,
			}),
		});

		// Case 3: Video has already completed - should not send video-end again
		// Find the complete event handler
		const completeEvent = metricEvents.find(
			(event) => event.eventName === 'complete',
		);
		expect(completeEvent).toBeDefined();

		// Call the complete event handler to set videoCompleted flag
		completeEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Reset mock to verify next calls
		mockTrackVideoEvent.mockReset();

		// Trigger playlistItem event again
		playlistItemEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify trackVideoEvent was not called
		expect(mockTrackVideoEvent).not.toHaveBeenCalled();

		// Case 4: Video-end already sent - should not send video-end again
		// Reset flags but keep videoStarted true and videoCompleted false
		resetEventFlags();
		firstFrameEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Reset mock to verify next calls
		mockTrackVideoEvent.mockReset();

		// Trigger playlistItem event to send video-end
		playlistItemEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify trackVideoEvent was called once
		expect(mockTrackVideoEvent).toHaveBeenCalledTimes(1);

		// Reset mock to verify next calls
		mockTrackVideoEvent.mockReset();

		// Trigger playlistItem event again
		playlistItemEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: { current: true },
			user,
			trackVideoEvent: mockTrackVideoEvent,
		});

		// Verify trackVideoEvent was not called again
		expect(mockTrackVideoEvent).not.toHaveBeenCalled();
	});
});
