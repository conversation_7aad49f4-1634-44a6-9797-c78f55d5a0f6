import type { VideoEvent } from '@/components/JWPlayer/types/events';

let adtitle = '';
let playReason = '';
let errorMessage = '';
let videoView = 1;

// the total seconds watched for a video, attribute sent in video-end event.
let contentWatchedSeconds = 0;
// used to calculate the seconds watched against the current time position.
let previousTimePosition = 0;

interface EventFlags {
	// ad play flags
	adImpression: boolean;
	// content play flags
	videoAttempt: boolean;
	videoPlayed: boolean;
	videoStarted: boolean;
	videoCompleted: boolean;
	videoEnd: boolean;
}

const eventSentFlags: EventFlags = {
	// ad play flags
	adImpression: false,
	// content play flags
	videoAttempt: false,
	videoPlayed: false,
	videoStarted: false,
	videoCompleted: false,
	videoEnd: false,
};

export function resetEventAttrs() {
	errorMessage = '';
	playReason = '';
	adtitle = '';
}

// Event flags are reset on adImpression or playAttempt (if no preroll or adblock) following
// the video-attempt event
export function resetEventFlags() {
	Object.keys(eventSentFlags).map((flag) => {
		eventSentFlags[flag as keyof EventFlags] = false;
	});

	contentWatchedSeconds = 0;
	previousTimePosition = 0;
}

/**
 * mparticle video events requirements:
 *   video-attempt - sent on adImpression or playAttempt (if no preroll or adblock)
 *   video-played - sent on adImpression or firstFrame (if no preroll or adblock)
 *   video-started - sent on firstFrame
 *   video-completed - sent on complete
 *   video-end - sent on complete, playlistItem (unfinished video skipped), or beforeunload (page exit)
 */
export const metricEvents: VideoEvent[] = [
	{
		eventName: 'ready',
		callback: ({ player, trackVideoEvent }) => {
			window.addEventListener('beforeunload', () => {
				if (eventSentFlags.videoStarted && !eventSentFlags.videoEnd) {
					// Send video-end when the user leaves the page,
					// if hasn't been sent.
					const currentVideo = player.getPlaylistItem();

					trackVideoEvent({
						eventName: 'video-end',
						currentVideo,
						jwPlayer: player,
						eventAttributes: {
							adtitle,
							errorMessage,
							contentWatchedSeconds,
							playReason,
							videoView,
						},
					});

					eventSentFlags.videoEnd = true;
				}
			});
		},
	},
	{
		eventName: 'adImpression',
		callback: async ({ event, player, trackVideoEvent }) => {
			const currentVideo = player.getPlaylistItem();

			// Reset video event flags when new preroll ad starts.
			resetEventFlags();

			console.debug(
				'JW Player callback - adImpression eventSentFlags:',
				eventSentFlags,
			);

			adtitle = event?.adtitle as string;

			eventSentFlags.adImpression = true;

			if (currentVideo && !eventSentFlags.videoAttempt && trackVideoEvent) {
				trackVideoEvent({
					eventName: 'video-attempt',
					currentVideo,
					jwPlayer: player,
					eventAttributes: {
						adtitle,
						errorMessage,
						playReason,
						videoView,
					},
				});

				eventSentFlags.videoAttempt = true;
			}

			if (currentVideo && !eventSentFlags.videoPlayed && trackVideoEvent) {
				trackVideoEvent({
					eventName: 'video-played',
					currentVideo,
					jwPlayer: player,
					eventAttributes: {
						adtitle,
						errorMessage,
						playReason,
						videoView,
					},
				});

				eventSentFlags.videoPlayed = true;
			}
		},
	},
	{
		eventName: 'viewable',
		callback: async ({ event }) => {
			videoView = event?.viewable as number;
		},
	},
	{
		eventName: 'playlistItem',
		callback: async ({ player, trackVideoEvent }) => {
			if (
				eventSentFlags.videoStarted &&
				!eventSentFlags.videoCompleted &&
				!eventSentFlags.videoEnd
			) {
				// Send video-end when a new video is loaded and the previous
				// video was not completed.
				// if completed, event is sent in the "complete" callback.
				const index = player.getPlaylistIndex();
				const previousVideo = player.getPlaylistItem(index - 1);

				trackVideoEvent({
					eventName: 'video-end',
					currentVideo: previousVideo,
					jwPlayer: player,
					eventAttributes: {
						adtitle,
						errorMessage,
						contentWatchedSeconds,
						playReason,
						videoView,
					},
				});

				eventSentFlags.videoEnd = true;
			}
		},
	},
	{
		eventName: 'playAttempt',
		callback: async ({ player, trackVideoEvent }) => {
			const currentVideo = player.getPlaylistItem();

			if (!eventSentFlags.adImpression) {
				// Reset video event flags when new video starts.
				// if there was no preroll ad or adblock.
				resetEventFlags();
			}

			errorMessage = '';

			console.debug(
				'JW Player callback - playAttempt eventSentFlags:',
				eventSentFlags,
			);

			if (currentVideo && !eventSentFlags.videoAttempt && trackVideoEvent) {
				eventSentFlags.videoAttempt = true;
				trackVideoEvent({
					eventName: 'video-attempt',
					currentVideo,
					jwPlayer: player,
					eventAttributes: {
						adtitle,
						errorMessage,
						playReason,
						videoView,
					},
				});
			}
		},
	},
	{
		eventName: 'firstFrame',
		callback: async ({ player, trackVideoEvent }) => {
			console.debug(
				'JW Player callback - firstFrame eventSentFlags:',
				eventSentFlags,
			);

			const currentVideo = player.getPlaylistItem();

			if (currentVideo && !eventSentFlags.videoPlayed && trackVideoEvent) {
				trackVideoEvent({
					eventName: 'video-played',
					currentVideo,
					jwPlayer: player,
					eventAttributes: {
						adtitle,
						errorMessage,
						playReason,
						videoView,
					},
				});

				eventSentFlags.videoPlayed = true;
			}

			if (currentVideo && !eventSentFlags.videoStarted && trackVideoEvent) {
				trackVideoEvent({
					eventName: 'video-started',
					currentVideo,
					jwPlayer: player,
					eventAttributes: {
						adtitle,
						errorMessage,
						playReason,
						videoView,
					},
				});

				eventSentFlags.videoStarted = true;
			}
		},
	},
	{
		eventName: 'complete',
		callback: async ({ player, trackVideoEvent }) => {
			const currentVideo = player.getPlaylistItem();
			console.debug(
				'JW Player callback - complete eventSentFlags:',
				eventSentFlags,
			);

			if (currentVideo && !eventSentFlags.videoCompleted && trackVideoEvent) {
				trackVideoEvent({
					eventName: 'video-completed',
					currentVideo,
					jwPlayer: player,
					eventAttributes: {
						adtitle,
						errorMessage,
						playReason,
						videoView,
					},
				});

				eventSentFlags.videoCompleted = true;
			}

			if (eventSentFlags.videoStarted && !eventSentFlags.videoEnd) {
				// Send video-end when the video is watched entirely.
				// otherwise, event is sent in the "playlistItem" callback.
				trackVideoEvent({
					eventName: 'video-end',
					currentVideo,
					jwPlayer: player,
					eventAttributes: {
						adtitle,
						errorMessage,
						contentWatchedSeconds,
						playReason,
						videoView,
					},
				});

				eventSentFlags.videoEnd = true;
			}
		},
	},
	{
		eventName: 'adStarted',
		callback: async ({ event, player, trackVideoEvent }) => {
			const currentVideo = player.getPlaylistItem();
			console.debug(
				'JW Player callback - adStarted eventSentFlags:',
				eventSentFlags,
			);

			adtitle = event?.adtitle as string;

			if (currentVideo && trackVideoEvent) {
				trackVideoEvent({
					eventName: 'ad-started',
					currentVideo,
					jwPlayer: player,
					eventAttributes: {
						adtitle,
						errorMessage,
						playReason,
						videoView,
					},
				});
			}
		},
	},
	{
		eventName: 'adSkipped',
		callback: async ({ player, trackVideoEvent }) => {
			const currentVideo = player.getPlaylistItem();

			if (currentVideo && trackVideoEvent) {
				trackVideoEvent({
					eventName: 'ad-skipped',
					currentVideo,
					jwPlayer: player,
					eventAttributes: {
						adtitle,
						errorMessage,
						playReason,
						videoView,
					},
				});
			}
		},
	},
	{
		eventName: 'adComplete',
		callback: async ({ event, player, trackVideoEvent }) => {
			const currentVideo = player.getPlaylistItem();
			console.debug(
				'JW Player callback - adComplete eventSentFlags:',
				eventSentFlags,
			);

			adtitle = event?.adtitle as string;

			if (currentVideo && trackVideoEvent) {
				trackVideoEvent({
					eventName: 'ad-complete',
					currentVideo,
					jwPlayer: player,
					eventAttributes: {
						adtitle,
						errorMessage,
						playReason,
						videoView,
					},
				});
			}
		},
	},
	{
		eventName: 'adError',
		callback: async ({ event }) => {
			console.error('Video ad error:', event);
		},
	},
	{
		eventName: 'error',
		callback: async ({ event }) => {
			errorMessage = event?.message as string;
			console.error('Video Player error:', event);
		},
	},
	{
		eventName: 'time',
		callback: ({ event }) => {
			if (typeof event?.position === 'number') {
				const secondsWatchedSincePrevPosition =
					event.position - previousTimePosition;

				contentWatchedSeconds += secondsWatchedSincePrevPosition;
				previousTimePosition = event.position;
			}
		},
	},
	{
		eventName: 'seek',
		callback: ({ event }) => {
			if (typeof event?.offset === 'number') {
				// event.offset - The position that has been requested for seeking (in seconds)
				previousTimePosition = event.offset;
			}
		},
	},
];
