'use client';

import React, { FC, useId, useMemo, useRef } from 'react';
import { useAllFormFields, usePayloadAPI } from '@payloadcms/ui';
import { reduceFieldsToValues } from 'payload/shared';
import AdminBlock from '@/components/Payload/AdminBlock';
import { JWPlayer } from '@/components/JWPlayer/JWPlayer';
import { getVideoThumbnail } from './utils/thumbnail';
import { buildJwPlaylist } from './utils/jwplayer';
import {
	PlaylistItem,
	Track,
	Playlist,
} from '@/components/JWPlayer/types/playlist';
import { Data } from 'payload';
import type { Video } from '@/payload-types';

const VideoAdminBlock: FC = () => {
	const playerKey = `video-player-${useId()}`;

	const [fields] = useAllFormFields();
	// Use useRef to store the previous formData and prevent unnecessary updates
	const prevFormDataRef = useRef<Data>(null);

	const formData = useMemo(() => {
		const newFormData = reduceFieldsToValues(fields, true);
		// Only update if relevant fields have changed
		if (
			prevFormDataRef.current &&
			JSON.stringify(newFormData) === JSON.stringify(prevFormDataRef.current)
		)
			return prevFormDataRef.current;

		prevFormDataRef.current = newFormData;
		return newFormData;
	}, [fields]);

	const {
		file,
		image,
		title,
		description,
		tracks,
		playlist,
		videoReference,
		playlistsReference,
	} = formData;

	// Fetch video data if videoReference is provided
	let videoData: Video | null = null;
	[{ data: videoData }] = usePayloadAPI(
		videoReference && typeof videoReference === 'string'
			? `/api/payload/videos/${videoReference}`
			: '',
	);

	// Check if we have video data from the API or if videoReference is already an object
	const isVideoObject = useMemo(() => {
		return (
			(videoData && Object.keys(videoData).length > 0) ||
			(videoReference && typeof videoReference !== 'string')
		);
	}, [videoData, videoReference]);

	// Get the actual video object to use
	const effectiveVideoObject = useMemo(() => {
		if (videoData && Object.keys(videoData).length > 0) {
			return videoData;
		}
		if (videoReference && typeof videoReference !== 'string') {
			return videoReference;
		}
		return null;
	}, [videoData, videoReference]);

	// Use video reference data if available
	const effectiveFile = useMemo(() => {
		if (
			isVideoObject &&
			effectiveVideoObject?.content?.videoFormatUrls?.length > 0
		) {
			// Use the default format if specified, otherwise use the first format
			if (effectiveVideoObject?.content?.defaultFormat?.url) {
				return effectiveVideoObject?.content?.defaultFormat?.url;
			}
			return effectiveVideoObject?.content?.videoFormatUrls?.[0]?.url;
		}
		return file;
	}, [isVideoObject, effectiveVideoObject, file]);

	const effectiveImage = useMemo(() => {
		if (isVideoObject) {
			return effectiveVideoObject?.content?.keyFrameImage || image;
		}
		return image;
	}, [isVideoObject, effectiveVideoObject, image]);

	const effectiveTitle = useMemo(() => {
		if (isVideoObject) {
			return effectiveVideoObject?.title || title;
		}
		return title;
	}, [isVideoObject, effectiveVideoObject, title]);

	const effectiveDescription = useMemo(() => {
		if (isVideoObject && effectiveVideoObject?.description) {
			// This is a rich text field, so we'd need to extract plain text
			// For now, just use the provided description
			return description;
		}
		return description;
	}, [isVideoObject, effectiveVideoObject, description]);

	const video = useMemo(() => {
		const tracksArray: Track[] = [];
		if (tracks?.file) {
			tracksArray.push(tracks);
		}

		return {
			file: effectiveFile,
			image:
				effectiveImage ||
				(effectiveFile ? getVideoThumbnail(effectiveFile) : null) ||
				'',
			title: effectiveTitle,
			description: effectiveDescription,
			tracks: tracksArray,
		} as PlaylistItem;
	}, [
		effectiveFile,
		effectiveImage,
		effectiveTitle,
		effectiveDescription,
		tracks,
	]);

	const jwplaylist = useMemo(() => {
		let basePlaylist: Playlist = [];

		// First, handle the direct playlist field
		if (playlist) {
			basePlaylist = buildJwPlaylist(playlist || [], video) || [];
		}

		// Then, handle playlist references if available
		if (
			playlistsReference &&
			Array.isArray(playlistsReference) &&
			playlistsReference.length > 0
		) {
			// For each content query reference, we would ideally fetch the content
			// and add it to the playlist. For now, we'll just use what we have.
			// This is a placeholder for future implementation
		}

		// If videoReference has playlists, add those too
		if (
			isVideoObject &&
			effectiveVideoObject?.content?.playlists &&
			Array.isArray(effectiveVideoObject?.content?.playlists) &&
			effectiveVideoObject?.content?.playlists?.length > 0
		) {
			// Similar to above, this would involve processing the playlists from the video reference
			// This is a placeholder for future implementation
		}

		return basePlaylist.length > 0 ? basePlaylist : [video];
	}, [
		playlist,
		video,
		playlistsReference,
		effectiveVideoObject,
		isVideoObject,
	]);

	// Show loading state when fetching video data
	if (videoReference && typeof videoReference === 'string' && !videoData) {
		return (
			<AdminBlock name="Video">
				<div className="flex h-32 items-center justify-center border border-dashed border-gray-300">
					<span>Loading video data...</span>
				</div>
			</AdminBlock>
		);
	}

	if (!effectiveFile) {
		return (
			<AdminBlock name="Video">
				<div className="flex h-32 items-center justify-center border border-dashed border-gray-300">
					<span>No video file provided</span>
				</div>
			</AdminBlock>
		);
	}

	return (
		<AdminBlock name="Video">
			<JWPlayer
				key={playerKey}
				playlist={jwplaylist}
				options={{ autostart: false }}
			/>
		</AdminBlock>
	);
};

export default VideoAdminBlock;
