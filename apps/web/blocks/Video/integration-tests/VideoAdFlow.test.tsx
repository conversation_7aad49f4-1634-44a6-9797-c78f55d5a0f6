import React from 'react';
import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest';
import { render } from '@testing-library/react';
import { VideoBlock } from '../VideoBlock';
import { JWPlayer } from '@/components/JWPlayer/JWPlayer';
import * as adUtils from '../ads/ad-utils';
import type { VideoBlockConfig } from '@/payload-types';

// Mock server-only modules
vi.mock('server-only', () => ({}));

// Mock any other server components or modules that might be using server-only
vi.mock('@/components/Helios/HeliosConfig/iabConfig', () => ({
	getIabConfig: vi.fn(() => ({
		iabSiteCategories: {
			v1: ['IAB15-10_Weather'],
			v3: ['390_Weather'],
		},
		additionalConfig: {},
		iabCategoriesByPageCode: {},
	})),
}));

// Import JWPlayerInstance type
import type { JWPlayerInstance } from '@/components/JWPlayer/types/player';
import { User } from '@/user/hooks/useUser';

// Define event callback types to match the actual implementation
type EventCallback = ({
	event,
	player,
}: {
	event: unknown;
	player: JWPlayerInstance;
}) => void;

// Define types for JWPlayer mock
interface JWPlayerEvent {
	eventName: string;
	callback: EventCallback;
}

// Define types for JWPlayer props
interface JWPlayerProps {
	playlist: unknown;
	events: JWPlayerEvent[];
}

// Mock JWPlayer events storage
let mockJWPlayerEvents: JWPlayerEvent[] = [];

// Shared isFirstPlayRef for tests
const mockIsFirstPlayRef = { current: true };

// Mock user for tests
const mockUser: User = {
	userID: '',
	isUserLoggedIn: false,
	subscriptionTier: 0,
	isUserPremium: false,
};

// Mock the useUser hook
vi.mock('@/user/hooks/useUser', () => ({
	useUser: vi.fn(() => ({ user: mockUser })),
}));

// Mock the ad-events module
vi.mock('../ads/ad-events', () => {
	const mockAdEvents = [
		{
			eventName: 'beforePlay',
			callback: vi.fn(async ({ player, isFirstPlayRef }) => {
				if (!isFirstPlayRef?.current) return;

				const currentItem = player.getPlaylistItem();
				const ctx = currentItem?.custom?.ctx;

				player.stop();
				await adUtils.prerollAd({ player, user: mockUser, ctx });

				isFirstPlayRef.current = false;
			}),
		},
		{
			eventName: 'playlistItem',
			callback: vi.fn(async ({ event, player, isFirstPlayRef }) => {
				if (isFirstPlayRef?.current) return;

				const item = event?.item;
				const ctx = item?.custom?.ctx;

				player.stop();
				await adUtils.prerollAd({ player, user: mockUser, ctx });
			}),
		},
		{
			eventName: 'adComplete',
			callback: vi.fn(async ({ player }) => {
				player.play();
			}),
		},
		{
			eventName: 'adError',
			callback: vi.fn(async ({ event, player }) => {
				mockDebugLogger.error('VideoBlock', 'Ad error occurred', event);
				player.play();
			}),
		},
		{
			eventName: 'error',
			callback: vi.fn(async ({ event }) => {
				mockDebugLogger.error('VideoBlock', 'Video Player error', event);
			}),
		},
	];

	return { adEvents: mockAdEvents };
});

// Mock the JWPlayer component
vi.mock('@/components/JWPlayer/JWPlayer', () => {
	const mockJWPlayer = vi.fn(({ playlist, events }: JWPlayerProps) => {
		// Store the events for testing
		mockJWPlayerEvents = events;

		return (
			<div
				data-testid="mock-jwplayer"
				data-playlist={JSON.stringify(playlist)}
			/>
		);
	});

	return { JWPlayer: mockJWPlayer };
});

// Mock the DebugCollector component
vi.mock('@/components/FrontendAdminHeader/collectors/DebugCollector', () => ({
	DebugCollector: vi.fn(({ children }: { children: React.ReactNode }) => (
		<div data-testid="mock-debug-collector">{children}</div>
	)),
}));

// Mock the thumbnail utility
vi.mock('../utils/thumbnail', () => ({
	getVideoThumbnail: vi.fn((file: string) => `${file}-thumbnail`),
}));

// Mock the jwplayer utility
vi.mock('../utils/jwplayer', () => ({
	buildJwPlaylist: vi.fn((playlist: unknown[], video: unknown) => {
		if (playlist && playlist.length > 0) {
			return [...playlist, video];
		}
		return [video];
	}),
}));

// Mock the debugLogger
const mockDebugLogger = vi.hoisted(() => ({
	error: vi.fn(),
	lifecycle: vi.fn(),
}));

vi.mock('@/components/FrontendAdminHeader/utils/debugLogger', () => ({
	debugLogger: mockDebugLogger,
}));

// Mock Helios
const mockHelios = {
	emit: vi.fn(),
	video: {
		requestVideoBids: vi.fn().mockResolvedValue(['bid1', 'bid2']),
	},
	modules: {
		iabc: {
			update: vi.fn(),
		},
		videoTag: {
			createAdTag: vi.fn().mockResolvedValue('https://example.com/ad-tag'),
		},
	},
};

describe('Video Ad Flow Integration', () => {
	// Mock props for VideoBlock
	const mockProps: VideoBlockConfig = {
		blockType: 'Video',
		file: 'https://example.com/video.mp4',
		image: 'https://example.com/thumbnail.jpg',
		title: 'Test Video with Ads',
		description: 'A video with ads for testing',
		custom: {
			ctx: {
				jwplayer: 'test-player-id',
				adzone: 'weather/video/ads',
				iab: {
					v1: ['IAB15-10_Weather'],
					v2: ['390_Weather'],
				},
			},
		},
	};

	// Mock player instance with all required JWPlayerInstance properties
	const mockPlayerInstance: JWPlayerInstance = {
		setup: vi.fn(),
		on: vi.fn(),
		off: vi.fn(),
		remove: vi.fn(),
		play: vi.fn(),
		pause: vi.fn(),
		stop: vi.fn(),
		playAd: vi.fn(),
		setConfig: vi.fn(),
		getConfig: vi.fn(),
		getPosition: vi.fn(),
		getAdBlock: vi.fn(),
		getDuration: vi.fn(),
		getMute: vi.fn(),
		getPlaylistItem: vi.fn().mockReturnValue({
			custom: {
				ctx: {
					jwplayer: 'test-player-id',
					adzone: 'weather/video/ads',
				},
			},
		}),
		getPlaylistIndex: function (): number {
			throw new Error('Function not implemented.');
		},
	};

	beforeEach(() => {
		vi.clearAllMocks();
		mockJWPlayerEvents = [];
		mockIsFirstPlayRef.current = true;

		// Setup global Helios object
		Object.defineProperty(window, '__Helios', {
			value: mockHelios,
			writable: true,
			configurable: true,
		});

		// Mock the prerollAd function implementation
		vi.spyOn(adUtils, 'prerollAd').mockImplementation(
			async ({ player, ctx }) => {
				// Simulate the ad tag request
				window.__Helios.emit(
					'UPDATE_AD_UNIT_PATH',
					`/7646/web_weather_us/${ctx?.adzone || 'video'}`,
				);

				try {
					window.__Helios.modules?.iabc?.update?.({
						page: ctx?.iab || {},
						site: { v1: ['IAB15-10_Weather'] },
					});
				} catch (err) {
					console.info(err);
				}

				const bids = await window.__Helios.video.requestVideoBids();
				const adTag = await window.__Helios.modules.videoTag.createAdTag(bids);

				if (adTag) {
					player.playAd(adTag);
				} else {
					player.play();
				}
			},
		);
	});

	afterEach(() => {
		vi.restoreAllMocks();

		// Clean up global Helios object
		if ('__Helios' in window) {
			delete (window as { __Helios?: unknown }).__Helios;
		}
	});

	test('VideoBlock correctly initializes JWPlayer with ad configuration', () => {
		render(<VideoBlock {...mockProps} />);

		// Verify JWPlayer was called with the correct props
		expect(JWPlayer).toHaveBeenCalled();

		// Get the props passed to JWPlayer
		const mockCalls = vi.mocked(JWPlayer).mock.calls;
		expect(mockCalls.length).toBeGreaterThan(0);

		// Type assertion for the props
		const jwPlayerProps = mockCalls[0]?.[0] as JWPlayerProps;

		// Verify playlist contains the video with custom fields
		expect(jwPlayerProps.playlist).toBeDefined();

		// Verify playlist is an array
		expect(Array.isArray(jwPlayerProps.playlist)).toBe(true);

		// Directly cast the playlist to a known type with a non-empty array
		const playlist = jwPlayerProps.playlist as [
			{
				file: string;
				title: string;
				custom: {
					ctx: {
						jwplayer: string;
						adzone: string;
					};
				};
			},
		];

		// Verify playlist has items
		expect(playlist.length).toBeGreaterThan(0);

		// Access the first item directly - TypeScript now knows it exists
		expect(playlist[0].file).toBe('https://example.com/video.mp4');
		expect(playlist[0].title).toBe('Test Video with Ads');
		expect(playlist[0].custom.ctx.jwplayer).toBe('test-player-id');
		expect(playlist[0].custom.ctx.adzone).toBe('weather/video/ads');

		// Verify events are passed
		expect(jwPlayerProps.events).toBeDefined();

		// Verify ad-related events are included
		const eventNames = jwPlayerProps.events.map((e) => e.eventName);
		expect(eventNames).toContain('beforePlay');
		expect(eventNames).toContain('playlistItem');
		expect(eventNames).toContain('adComplete');
		expect(eventNames).toContain('adError');
	});

	test('Ad events trigger appropriate player behaviors', async () => {
		render(<VideoBlock {...mockProps} />);

		// Get the events passed to JWPlayer
		const events = mockJWPlayerEvents;

		// Find the beforePlay event
		const beforePlayEvent = events.find((e) => e.eventName === 'beforePlay');
		expect(beforePlayEvent).toBeDefined();

		// Reset the mock isFirstPlayRef for this test
		mockIsFirstPlayRef.current = true;

		// Create a mock player with all required methods for beforePlay
		const mockBeforePlayPlayer: JWPlayerInstance = {
			...mockPlayerInstance,
			getPlaylistItem: vi.fn().mockReturnValue({
				custom: {
					ctx: {
						jwplayer: 'test-player-id',
						adzone: 'weather/video/ads',
					},
				},
			}),
		};

		// Call the beforePlay callback
		beforePlayEvent!.callback({ event: {}, player: mockBeforePlayPlayer });

		// Verify player.stop was called (to pause for ad)
		expect(mockBeforePlayPlayer.stop).toHaveBeenCalled();

		// Verify prerollAd was called with correct parameters
		expect(adUtils.prerollAd).toHaveBeenCalledWith({
			player: mockBeforePlayPlayer,
			user: mockUser,
			ctx: expect.objectContaining({
				jwplayer: 'test-player-id',
				adzone: 'weather/video/ads',
			}),
		});

		// Find the adComplete event
		const adCompleteEvent = events.find((e) => e.eventName === 'adComplete');
		expect(adCompleteEvent).toBeDefined();

		// Create a mock player with all required methods for adComplete
		const mockAdCompletePlayer: JWPlayerInstance = {
			...mockPlayerInstance,
			play: vi.fn(),
		};

		// Call the adComplete callback
		adCompleteEvent!.callback({
			event: { type: 'adComplete' },
			player: mockAdCompletePlayer,
		});

		// Verify player.play was called after ad completion
		expect(mockAdCompletePlayer.play).toHaveBeenCalled();
	});

	test('Custom fields from wxNode are properly passed through to ad system', async () => {
		// Create a mock player with all required methods
		const mockPlayer: JWPlayerInstance = {
			...mockPlayerInstance,
			playAd: vi.fn(),
		};

		// Create a mock context with the custom fields from wxNode
		const ctx = {
			jwplayer: 'test-player-id',
			adzone: 'weather/video/ads',
			iab: {
				v1: ['IAB15-10_Weather'],
				v2: ['390_Weather'],
			},
		};

		// Call prerollAd directly with the mock player and context
		await adUtils.prerollAd({ player: mockPlayer, user: mockUser, ctx });

		// Verify Helios emit was called with the correct ad unit path
		expect(mockHelios.emit).toHaveBeenCalledWith(
			'UPDATE_AD_UNIT_PATH',
			'/7646/web_weather_us/weather/video/ads',
		);

		// Verify IAB categories were updated
		expect(mockHelios.modules.iabc.update).toHaveBeenCalledWith({
			page: {
				v1: ['IAB15-10_Weather'],
				v2: ['390_Weather'],
			},
			site: { v1: ['IAB15-10_Weather'] },
		});

		// Verify bids were requested
		expect(mockHelios.video.requestVideoBids).toHaveBeenCalled();

		// Verify ad tag was created
		expect(mockHelios.modules.videoTag.createAdTag).toHaveBeenCalled();
	});

	test('Handles errors in the ad flow gracefully', async () => {
		// Mock an error in the ad tag creation
		mockHelios.modules.videoTag.createAdTag.mockRejectedValueOnce(
			new Error('Ad tag error'),
		);

		render(<VideoBlock {...mockProps} />);

		// Get the events passed to JWPlayer
		const events = mockJWPlayerEvents;

		// Find the adError event
		const adErrorEvent = events.find((e) => e.eventName === 'adError');
		expect(adErrorEvent).toBeDefined();

		// Create a mock player with all required methods
		const mockPlayer: JWPlayerInstance = {
			...mockPlayerInstance,
			play: vi.fn(),
		};

		// Call the callback with the new structure (object parameter)
		adErrorEvent!.callback({
			event: { message: 'Ad error occurred' },
			player: mockPlayer,
		});

		// Verify error was logged
		expect(mockDebugLogger.error).toHaveBeenCalledWith(
			'VideoBlock',
			'Ad error occurred',
			expect.anything(),
		);

		// Verify player.play was called as fallback
		expect(mockPlayer.play).toHaveBeenCalled();
	});
});
