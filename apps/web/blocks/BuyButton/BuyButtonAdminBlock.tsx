'use client';

import React, { FC, useEffect, useState } from 'react';
import { useAllFormFields } from '@payloadcms/ui';
import { reduceFieldsToValues } from 'payload/shared';
import { Button } from '@repo/ui/components/BuyButton';
import AdminBlock from '@/components/Payload/AdminBlock';
import Link from '@repo/ui/components/Link/Link';
import type { BuyButtonBlockConfig } from '@/payload-types';

const BuyButtonAdminBlock: FC<BuyButtonBlockConfig> = () => {
	const [fields] = useAllFormFields();
	const formData = reduceFieldsToValues(fields, true);

	const [buttonData, setButtonData] = useState({
		label: formData?.label || 'Button Label',
		url: formData?.url || '#',
		variant: formData?.variant || 'bg-gray-900',
		inline: formData?.inline || false,
		isAffiliate: formData?.isAffiliate !== false, // Default to true if undefined
	});

	useEffect(() => {
		setButtonData({
			label: formData?.label || 'Button Label',
			url: formData?.url || '#',
			variant: formData?.variant || 'bg-gray-900',
			inline: formData?.inline || false,
			isAffiliate: formData?.isAffiliate !== false, // Default to true if undefined
		});
	}, [
		formData?.label,
		formData?.url,
		formData?.variant,
		formData?.inline,
		formData?.isAffiliate,
	]);

	// Ensure variant is a valid type for the Button component
	const getValidVariant = (variant: BuyButtonBlockConfig['variant']) => {
		const validVariants = [
			'bg-brand-200',
			'bg-brand-300',
			'bg-yellow-400',
			'bg-green-600',
			'bg-violet-500',
			'bg-gray-900',
		];

		if (typeof variant !== 'string' || !variant) {
			return 'bg-gray-900'; // Default to gray if variant is not a string
		}

		return validVariants.includes(variant) ? variant : 'bg-gray-900';
	};

	// Set appropriate rel attributes based on isAffiliate flag
	const relAttributes = buttonData.isAffiliate
		? 'nofollow noopener noreferrer'
		: 'noopener';

	return (
		<AdminBlock name="Buy Button">
			<div className="mb-2">
				<span
					className="mb-1 block text-xs"
					style={{ color: 'var(--theme-elevation-500)' }}
				>
					Preview:
				</span>
				<div className={buttonData.inline ? 'inline-block' : 'block'}>
					<Link
						href={buttonData.url}
						target="_blank"
						rel={relAttributes}
						onClick={(e) => e.preventDefault()} // Prevent navigation in admin
					>
						<Button
							variant={getValidVariant(buttonData.variant)}
							inline={true} // Always use inline styling for the button itself
							className="cursor-pointer whitespace-nowrap"
						>
							{buttonData.label || 'Button Label'}
						</Button>
					</Link>
				</div>
			</div>
			<div
				className="mt-3 text-xs"
				style={{ color: 'var(--theme-elevation-500)' }}
			>
				<div className="mb-1 flex items-center justify-between">
					<span>Display:</span>
					<span className="font-medium">
						{buttonData.inline ? 'Inline' : 'Block'}
					</span>
				</div>
				<div className="flex items-center justify-between">
					<span>Link type:</span>
					<span className="font-medium">
						{buttonData.isAffiliate ? 'Affiliate (nofollow)' : 'Standard'}
					</span>
				</div>
			</div>
		</AdminBlock>
	);
};

export default BuyButtonAdminBlock;
