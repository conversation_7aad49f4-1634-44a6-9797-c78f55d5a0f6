'use client';

import React, { FC, useEffect, useMemo } from 'react';
import { usePathname } from 'next/navigation';
import type { Block } from 'payload';
import { getClientSideURL } from '@/utils/getURL';

export interface TaboolaPlacement {
	mode: string;
	container: string;
	placement: string;
	target_type: string;
}

// Define the TaboolaBlockConfig interface since it's not in payload-types.ts yet
interface TaboolaBlockConfig extends Block {
	blockType: 'Taboola';
	placements: Array<TaboolaPlacement>;
	loaderUrl: string;
	pageType: string;
	title?: string;
	height?: string;
	configs: {
		url: string;
		scriptId: string;
		commands: Array<Record<string, unknown>>;
	};
	article?: Article;
}
import { DebugCollector } from '@/components/FrontendAdminHeader/collectors/DebugCollector';
import useAllowTaboola from '@/components/Taboola/hooks/useAllowTaboola';
import TaboolaSdk from '@/components/Taboola/TaboolaSdk';
import TaboolaConfig from '@/components/Taboola/TaboolaConfig';
import { taboolaNewPageLoadPush } from '@/components/Taboola/utils';
import { cn } from '@repo/ui/lib/utils';
import { Article } from '@/payload-types';
import isAtmosphereArticle from '@/utils/isAtmosphereArticle';

interface TaboolaBlockProps
	extends Omit<
		TaboolaBlockConfig,
		'blockType' | 'loaderUrl' | 'configs' | 'fields' | 'slug'
	> {
	className?: string;
}

export const TaboolaBlock: FC<TaboolaBlockProps> = ({
	placements,
	pageType,
	className = '',
	article,
}) => {
	// Check if Taboola is allowed to display based on current user
	const allowTaboola = useAllowTaboola();
	const pathname = usePathname();
	const isAtmospherePage = isAtmosphereArticle(article);

	// TODO: Taboola loaderURL is currently always constant, but will be dynamic for non-Article pages and non-US locales
	const loaderUrl = '//cdn.taboola.com/libtrc/theweatherchannel/loader.js';

	// Setup Taboola and observers
	useEffect(() => {
		// Skip if Taboola is not allowed or if we don't have the necessary props
		if (isAtmospherePage || !allowTaboola || !loaderUrl) {
			return;
		}

		// Clean up on unmount or when props change
		return () => {
			taboolaNewPageLoadPush();
		};
	}, [allowTaboola, loaderUrl, isAtmospherePage]);

	// Calculate the page URL at the top level of the component
	const pageUrl = useMemo(() => {
		const baseUrl = getClientSideURL();
		return `${baseUrl}${pathname || ''}`;
	}, [pathname]);

	// Don't render anything if Taboola is not allowed or we're missing required props
	if (isAtmospherePage || !allowTaboola || !loaderUrl) return null;

	return (
		<section className={cn('@container', className)}>
			{allowTaboola && loaderUrl && (
				<>
					<TaboolaSdk url={loaderUrl} scriptId="tb_loader_script" />
					<TaboolaConfig
						pageType={pageType}
						placements={placements}
						pageUrl={pageUrl}
					/>
				</>
			)}
			<DebugCollector
				componentName="TaboolaBlock"
				data={{
					props: {
						placements,
						loaderUrl,
						pageType,
						className,
					},
				}}
			/>
		</section>
	);
};

export default TaboolaBlock;
