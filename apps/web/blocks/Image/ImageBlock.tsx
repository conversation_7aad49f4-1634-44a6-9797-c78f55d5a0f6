'use client';

import React, { FC } from 'react';
import Link from '@repo/ui/components/Link/Link';
import type { ImageBlockConfig, Image } from 'payload-types';
import { cn } from '@repo/ui/lib/utils';
import { Figure } from '@repo/ui/components/Figure/Figure';

// Create a type guard to check if the object is an Image object
const isImageObject = (
	obj: string | Image | null | undefined,
): obj is Image => {
	return typeof obj === 'object' && obj !== null && 'url' in obj;
};

export interface ImageBlockInjectedFields {
	imageAttributes?: Record<string, string | number | boolean>;
}

const ImageBlock: FC<ImageBlockConfig & ImageBlockInjectedFields> = ({
	imageType,
	internalImageId,
	externalImageUrl,
	controls,
	altText,
	isPriorityImage,
	imageAttributes,
	linkUrl,
}) => {
	// Check if data and data.fields exist
	if (!imageType || (!internalImageId && !externalImageUrl)) {
		return (
			<div className="text-center text-red-500">
				Error: Missing data for the image block.
			</div>
		);
	}

	const isInternalImage =
		imageType === 'internal' && isImageObject(internalImageId);

	const imageUrl = isInternalImage
		? internalImageId?.url || ''
		: externalImageUrl || '';

	// Always use the image's alt text for internal images
	const resolvedAltText = isInternalImage
		? internalImageId?.seo?.altText || ''
		: altText || '';

	// For internal images, use the image's caption if useImageCaption is true
	let caption = '';
	let credit = '';

	if (!controls?.showCaption) {
		// leave caption empty
	} else if (
		(isInternalImage && !controls?.useImageCaption) ||
		imageType === 'external'
	) {
		caption = controls?.caption || '';
		credit = controls?.credit || '';
	} else if (isInternalImage) {
		caption = internalImageId?.seo?.caption || '';
		credit = internalImageId?.seo?.credit || '';
	}

	const figureComponent = (
		<Figure
			src={imageUrl}
			alt={resolvedAltText}
			caption={caption}
			credit={credit}
			priority={!!isPriorityImage}
			className={cn('my-4 flex flex-col', {
				'max-w-[480px]': controls?.width === 'half',
			})}
			sizes="(max-width: 767px) 91vw, (max-width: 1280px) 64vw, 800px"
			imageAttributes={imageAttributes}
		/>
	);

	return linkUrl ? (
		<Link href={linkUrl} target="_blank" rel="noopener noreferrer nofollow">
			{figureComponent}
		</Link>
	) : (
		figureComponent
	);
};

export default ImageBlock;
