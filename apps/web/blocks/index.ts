import { FC } from 'react';
import MorningBriefBlock from '@/blocks/MorningBrief/MorningBriefBlock';
import MorningBriefBlockConfig from '@/blocks/MorningBrief/config';
import DailyForecastBlock from '@/blocks/DailyForecast/DailyForecastBlock';
import DailyForecastBlockConfig from '@/blocks/DailyForecast/config';
import CurrentConditionsBlock from '@/blocks/CurrentConditions/CurrentConditionsBlock';
import CurrentConditionsBlockConfig from '@/blocks/CurrentConditions/config';
import YouTubeBlock from '@/blocks/YouTube/YouTubeBlock';
import YouTubeBlockConfig from '@/blocks/YouTube/config';
import BuyButtonBlock from '@/blocks/BuyButton/BuyButtonBlock';
import BuyButtonBlockConfig from '@/blocks/BuyButton/config';
import AdBlock from '@/blocks/Ad/AdBlock';
import AdBlockConfig from '@/blocks/Ad/config';
import ImageBlock from '@/blocks/Image/ImageBlock';
import ImageBlockConfig from '@/blocks/Image/config';
import VideoBlock from '@/blocks/Video/VideoBlock';
import VideoBlockConfig from '@/blocks/Video/config';
import ContentMediaBlock from '@/blocks/ContentMedia/ContentMediaBlock';
import ContentMediaBlockConfig from '@/blocks/ContentMedia/config';
import CTABlock from './CTABlock/CTABlock';
import CTABlockConfig from './CTABlock/config';
import TaboolaBlock from '@/blocks/Taboola/TaboolaBlock';
import TaboolaBlockConfig from '@/blocks/Taboola/config';
import TwitterBlock from './Twitter/TwitterBlock';
import TwitterBlockConfig from './Twitter/config';
import LiveblogEntriesBlock from './LiveblogEntries/LiveblogEntriesBlock';
import LiveblogEntriesBlockConfig from './LiveblogEntries/config';
import SlideshowBlock from './Slideshow/SlideshowBlock';
import SlideshowBlockConfig from './Slideshow/config';
import PromoDriverBlock from './PromoDriver/PromoDriverBlock';
import PromoDriverBlockConfig from './PromoDriver/config';
import InstagramBlock from '@/blocks/Instagram/InstagramBlock';
import InstagramBlockConfig from '@/blocks/Instagram/config';

import type {
	MorningBriefBlockConfig as IMorningBrief,
	DailyForecastBlockConfig as IDailyForecast,
	CurrentConditionsBlockConfig as ICurrentConditions,
	AdBlockConfig as IAd,
	ImageBlockConfig as IImage,
	VideoBlockConfig as IVideo,
	ContentMediaBlockConfig as IContentMedia,
	LiveblogEntriesBlockConfig as ILiveblogEntries,
	CTABlockConfig as ICTA,
	SlideshowBlockConfig as ISlideshow,
	PromoDriverBlock as IPromoDriver,
	YouTubeBlockConfig as IYoutube,
	BuyButtonBlockConfig as IBuyButton,
	TwitterBlockConfig as ITwitter,
	InstagramBlockConfig as IInstagram,
	// missing Taboola as a block
} from '@/payload-types';

// Union of all block types - automatically derived from payload types
// missing Taboola as a block
export type BlockConfig =
	| IMorningBrief
	| IDailyForecast
	| ICurrentConditions
	| IAd
	| IImage
	| IVideo
	| IContentMedia
	| ILiveblogEntries
	| ICTA
	| ISlideshow
	| IPromoDriver
	| IYoutube
	| IBuyButton
	| ITwitter
	| IInstagram;

// Enhanced block registration interface
export interface BlockRegistration {
	slug: string;
	component: FC<any>; // eslint-disable-line @typescript-eslint/no-explicit-any
}

// Typed block registrations - this is now the single source of truth
export const blockRegistrations: BlockRegistration[] = [
	{
		slug: MorningBriefBlockConfig.slug,
		component: MorningBriefBlock,
	},
	{
		slug: DailyForecastBlockConfig.slug,
		component: DailyForecastBlock,
	},
	{
		slug: CurrentConditionsBlockConfig.slug,
		component: CurrentConditionsBlock,
	},
	{
		slug: YouTubeBlockConfig.slug,
		component: YouTubeBlock,
	},
	{
		slug: BuyButtonBlockConfig.slug,
		component: BuyButtonBlock,
	},
	{
		slug: AdBlockConfig.slug,
		component: AdBlock,
	},
	{
		slug: ImageBlockConfig.slug,
		component: ImageBlock,
	},
	{
		slug: VideoBlockConfig.slug,
		component: VideoBlock,
	},
	{
		slug: ContentMediaBlockConfig.slug,
		component: ContentMediaBlock,
	},
	{
		slug: TwitterBlockConfig.slug,
		component: TwitterBlock,
	},
	{
		slug: LiveblogEntriesBlockConfig.slug,
		component: LiveblogEntriesBlock,
	},
	{
		slug: CTABlockConfig.slug,
		component: CTABlock,
	},
	{
		slug: SlideshowBlockConfig.slug,
		component: SlideshowBlock,
	},
	{
		slug: TaboolaBlockConfig.slug,
		component: TaboolaBlock,
	},
	{
		slug: PromoDriverBlockConfig.slug,
		component: PromoDriverBlock,
	},
	{
		slug: InstagramBlockConfig.slug,
		component: InstagramBlock,
	},
];

// Generate the BLOCK_MAP from registrations
export const BLOCK_MAP = blockRegistrations.reduce(
	(acc, { slug, component }) => {
		acc[slug] = component;
		return acc;
	},
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	{} as Record<string, FC<any>>,
);
