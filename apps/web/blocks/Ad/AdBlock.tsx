'use client';

import React, {
	FC,
	useEffect,
	useState,
	useRef,
	useMemo,
	useCallback,
} from 'react';
import type { AdBlockConfig } from '@/payload-types';
import { DebugCollector } from '@/components/FrontendAdminHeader/collectors/DebugCollector';
import { cn } from '@repo/ui/lib/utils';
import CTABlock from '../CTABlock/CTABlock';
import { useUser } from '@/user/hooks/useUser';

interface AdBlockProps extends Pick<AdBlockConfig, 'adId'> {
	variant?: 'banner' | 'sidebar';
	title?: string | null;
	height?: string; // Initial height, will be adjusted dynamically
	className?: string;
}

export const AdBlock: FC<AdBlockProps> = ({
	adId,
	variant = 'banner',
	title = null,
	height = '120px', // Default initial height
	className = '',
}) => {
	const { user } = useUser();
	const { isUserPremium } = user;
	const [adState, setAdState] = useState({
		loaded: false,
		height: height,
		displayAd: false,
	});

	// Refs for DOM elements and observers
	const adContainerRef = useRef<HTMLDivElement>(null);
	const observersRef = useRef<{
		resize: ResizeObserver | null;
		adFrameMutation: MutationObserver | null; // first mutation for the insertion of the google_ads_iframe div
		adCreativeMutation: MutationObserver | null; // second mutation for the insertion of iframe for an ad creative
	}>({
		resize: null,
		adFrameMutation: null,
		adCreativeMutation: null,
	});

	// Memoize container styles to prevent recalculation on every render
	const containerStyles = useMemo(() => {
		if (adId === 'WX_Hidden' || !adState.displayAd) {
			return 'hidden';
		}

		// TODO: Remove the WX_WindowShade and SideBar specific styles
		if (adId === 'WX_WindowShade') {
			// region-contentTop class required for WX_WindowShade as it's used in IM ads' creative code
			return 'region-contentTop overflow-hidden mb-4';
		} else if (adId === 'WX_BottomLeader') {
			return 'bg-white rounded-md overflow-hidden px-4 pb-4 mt-4';
		} else if (adId === 'WX_SpotLight' || adId === 'WX_DriverUnit') {
			return 'overflow-hidden bg-white mb-4';
		} else if (adId === 'WX_Top300Variable') {
			// Sticky positioning for top right ad - positioned below header
			// header z-index is at 50 and Taboola at 40. 45 is between the two.
			// 82px since that is the height of the header on desktop
			return 'sticky top-[82px] z-45 bg-white rounded-md overflow-hidden px-4 pb-4';
		} else if (adId === 'MW_Position1') {
			// z-[98] to place ad just below the bottom nav menu (z-100)
			return 'bottom-[61px] block fixed w-full z-[98] bg-[#dee0e3] p-2';
		} else if (adId === 'MW_Position3') {
			return 'overflow-hidden mb-5';
		} else if (adId === 'MW_Position4' || adId === 'MW_Position5') {
			return 'rounded-lg overflow-hidden bg-white pt-0 px-4 pb-4';
		} else if (variant === 'banner') {
			return 'rounded-lg overflow-hidden bg-white shadow-md mb-4';
		}
		// Sidebar variant with bottom padding
		return 'bg-white rounded-md overflow-hidden px-4 pb-4';
	}, [adId, variant, adState.displayAd]);

	// Memoize content class to prevent string concatenation on every render
	const contentClass = useMemo(() => {
		const baseClasses =
			'text-center w-full h-full flex text-gray-600 text-base leading-none';
		const visibilityClass = !adState.loaded ? 'invisible' : 'visible';

		return `${baseClasses} ${adId !== 'WX_SpotLight' ? 'justify-center' : ''} ${visibilityClass}`;
	}, [adId, adState.loaded]);

	// Handle resize observations with useCallback to prevent recreation on each render
	const handleResize = useCallback((entries: ResizeObserverEntry[]) => {
		for (const entry of entries) {
			const contentHeight = entry.contentRect.height;
			if (contentHeight > 0) {
				setAdState((prev) => ({
					...prev,
					height: `${contentHeight + 10}px`,
					loaded: true,
				}));
			}
		}
	}, []);

	// Handle ad creative serve observations with useCallback to prevent recreation on each render
	const handleAdCreativeServe = useCallback((mutations: MutationRecord[]) => {
		// Check if ad creative has been injected
		const hasAdCreative = mutations.some(
			(mutation) =>
				mutation.type === 'childList' && mutation.addedNodes.length > 0,
		);

		// Only display the ad block if ad creative was actually served
		setAdState((prev) => ({
			...prev,
			displayAd: hasAdCreative,
		}));
	}, []);

	// Setup observers only once when component mounts or adId changes
	useEffect(() => {
		// Reset state when adId changes
		setAdState((prev) => ({
			...prev,
			loaded: false,
			height: height,
			displayAd: false,
		}));

		// Clean up previous observers
		if (observersRef.current.resize) {
			observersRef.current.resize.disconnect();
		}
		if (observersRef.current.adFrameMutation) {
			observersRef.current.adFrameMutation.disconnect();
		}
		if (observersRef.current.adCreativeMutation) {
			observersRef.current.adCreativeMutation.disconnect();
		}

		// Create resize observer once
		const resizeObserver = new ResizeObserver(handleResize);
		observersRef.current.resize = resizeObserver;

		// Create ad creative mutation observer once
		const adCreativeMutationObserver = new MutationObserver(
			handleAdCreativeServe,
		);
		observersRef.current.adCreativeMutation = adCreativeMutationObserver;

		// Create ad frame mutation observer once
		const adFrameMutationObserver = new MutationObserver((mutations) => {
			// Check if ad frame has been injected
			const hasAdFrame = mutations.some(
				(mutation) =>
					mutation.type === 'childList' &&
					mutation.addedNodes.length > 0 &&
					// Check if the added nodes are not just the default text
					(mutation.addedNodes[0]?.textContent !== 'Advertisement' ||
						mutation.addedNodes.length > 1),
			);

			if (
				hasAdFrame &&
				adContainerRef.current &&
				resizeObserver &&
				adCreativeMutationObserver
			) {
				// Start observing the size of the ad content
				resizeObserver.observe(adContainerRef.current);

				// Mark ad as loaded
				setAdState((prev) => ({ ...prev, loaded: true }));

				// Since google_ads_iframe div gets inserted when ad call is made regardless of whether creative actually served,
				// we need to start observing whether an actual creative is returned
				adCreativeMutationObserver.observe(adContainerRef.current, {
					childList: true,
					subtree: true,
				});
			}
		});
		observersRef.current.adFrameMutation = adFrameMutationObserver;

		// Start observing for mutations
		const adContainerElement = adContainerRef.current;
		if (adContainerElement && adFrameMutationObserver) {
			adFrameMutationObserver.observe(adContainerElement, {
				childList: true,
				subtree: true,
			});
		}

		// Clean up on unmount or when adId changes
		return () => {
			resizeObserver.disconnect();
			adFrameMutationObserver.disconnect();
			adCreativeMutationObserver.disconnect();
		};
	}, [handleAdCreativeServe, adId, height, handleResize]);

	// Do not render if we don't have an adId or if the user is a premium user
	if (!adId || isUserPremium) return null;

	return (
		<>
			<DebugCollector
				componentName="AdBlock"
				data={{
					props: {
						adId,
						variant,
						title,
						height,
						className,
					},
					state: {
						loaded: adState.loaded,
						height: adState.height,
						displayAd: adState.displayAd,
					},
					performance: {
						// Include any performance metrics you want to track
						renderTime: Date.now(),
					},
				}}
			/>
			<div className={cn(containerStyles, className)}>
				{/* Optional title above the ad - only show when loaded */}
				{title && adState.loaded && (
					<div className="py-0.75 text-xxs w-full px-4 text-right text-gray-400">
						{title}
					</div>
				)}

				{/* Special case title above the ad for PromoDriver ad unit to mimic CTABlock title styling */}
				{adId === 'WX_PromoDriver1' &&
					!title &&
					adState.loaded &&
					adState.displayAd && (
						<h2 className="my-6 px-2 text-2xl font-bold">Stay Safe</h2>
					)}

				{/* Ad content - hidden until loaded */}
				<div
					id={adId}
					className={contentClass}
					style={{ height: adState.loaded ? '100%' : '0px' }}
					ref={adContainerRef}
				></div>
			</div>
			{/* Fallback CTA Block if no PromoDriver ad unit filled */}
			{/* PromoDriver logic optimized for Article page; refactor if necessary for Home/Daily/Hourly pages */}
			{adId === 'WX_PromoDriver1' &&
				(!adState.loaded || !adState.displayAd) && (
					<CTABlock
						title="Stay Safe"
						ctaStyle="featured"
						linkUrl="/"
						backgroundColor="white"
						media="https://s-dev.w-x.co/wxnext/img/Screenshot2025-04-17at44929PMpng"
					/>
				)}
		</>
	);
};

export default AdBlock;
