'use client';

import React from 'react';
import Image from 'next/image';
import Link from '@repo/ui/components/Link/Link';
import { isImageObject } from '@/utils/blockGuards';
import type { PromoDriverBlock as PromoDriverBlockConfig } from '@/payload-types';

type PromoDriverProps = PromoDriverBlockConfig;

const PromoDriverBlock: React.FC<PromoDriverProps> = ({
	title,
	image,
	subtitle,
	bodyText,
	ctaButton,
}) => {
	// Determine if the URL is external or internal
	const isExternalUrl = ctaButton?.url && /^https?:\/\//i.test(ctaButton.url);
	const imageData = isImageObject(image) ? image : null;

	// Check if we have any content to display
	const hasContent =
		title || (imageData && imageData?.url) || subtitle || bodyText || ctaButton;

	if (!hasContent) {
		return null; // Don't render anything if no content is available
	}

	return (
		<div className="overflow-hidden rounded-lg bg-white">
			{title && (
				<div className="p-4">
					<h3 className="text-xl font-semibold text-gray-900">{title}</h3>
				</div>
			)}

			<div className="flex flex-col">
				{/* Image */}
				{imageData?.url ? (
					<div className="relative aspect-[42/13] w-full overflow-hidden bg-white px-4 pt-4">
						<div className="relative h-full w-full overflow-hidden rounded-lg">
							<a href={(ctaButton && ctaButton.url) || '#'}>
								<Image
									src={imageData.url}
									alt={title || 'Promo image'}
									fill
									className="object-cover"
								/>
							</a>
						</div>
					</div>
				) : null}

				{/* Content */}
				{(subtitle || bodyText || ctaButton) && (
					<div className="p-4">
						{subtitle && (
							<a href={(ctaButton && ctaButton.url) || '#'}>
								<h4 className="mb-2 text-base font-bold">{subtitle}</h4>
							</a>
						)}

						{bodyText && (
							<a href={(ctaButton && ctaButton.url) || '#'}>
								<p className="mb-4 text-base">{bodyText}</p>
							</a>
						)}

						{/* CTA Button */}
						{ctaButton && (
							<div className="flex justify-end">
								{isExternalUrl ? (
									<a
										href={ctaButton.url || '#'}
										target={ctaButton.openInNewTab ? '_blank' : '_self'}
										rel={ctaButton.openInNewTab ? 'noopener noreferrer' : ''}
										className="inline-block max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap rounded-lg bg-black px-4 py-2 text-base font-normal text-white"
									>
										{ctaButton.text}
									</a>
								) : (
									<Link
										href={ctaButton.url || '#'}
										target={ctaButton.openInNewTab ? '_blank' : '_self'}
										className="inline-block max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap rounded-lg bg-black px-4 py-2 text-base font-normal text-white"
									>
										{ctaButton.text}
									</Link>
								)}
							</div>
						)}
					</div>
				)}
			</div>
		</div>
	);
};

export default PromoDriverBlock;
