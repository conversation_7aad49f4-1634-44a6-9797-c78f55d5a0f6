import Link from '@repo/ui/components/Link/Link';
import { FOOTER_TEXT, linksMap, linksRegex } from '../constants';

export default function Footer() {
	return (
		<div className="mt-4 text-xs">
			{FOOTER_TEXT.split(linksRegex).map((text, index) => {
				const key = `{{${text}}}` as keyof typeof linksMap;
				const link = linksMap[key];

				if (link) {
					return (
						<Link key={index} href={link.url} className="underline">
							{link.text}
						</Link>
					);
				}

				return <span key={index}>{text}</span>;
			})}
		</div>
	);
}
