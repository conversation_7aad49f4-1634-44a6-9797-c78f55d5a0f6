import { Input } from '@repo/ui/components/Input/DeprecatedInput';

export interface TextInputProps
	extends React.InputHTMLAttributes<HTMLInputElement> {
	label?: string;
	error?: boolean;
}

export default function TextInput({
	label,
	error,
	className,
	...inputProps
}: TextInputProps) {
	return (
		<div className="w-full">
			{label && <label className="text-sm">{label}</label>}
			<Input
				{...inputProps}
				className={error ? `${className} border-red-500` : className}
			/>
		</div>
	);
}
