{
	"extends": "@repo/typescript-config/nextjs.json",
	"compilerOptions": {
		"baseUrl": ".",
		"paths": {
			// leave this alone since (payload) files were generated using this import
			"@payload-config": ["./payload.config.ts"],
			"@/payload-types": ["./payload-types.ts"],
			"@/*": ["./*"]
		},
		"plugins": [
			{
				"name": "next"
			}
		]
	},
	"include": [
		"next-env.d.ts",
		"assets.d.ts",
		"**/*.ts",
		"**/*.tsx",
		".next/types/**/*.ts"
	],
	"exclude": ["node_modules"]
}
