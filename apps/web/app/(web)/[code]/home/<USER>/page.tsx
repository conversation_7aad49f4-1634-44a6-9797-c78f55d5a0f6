import { Fragment } from 'react';
import { unstable_cacheTag as cacheTag } from 'next/cache';
import CurrentConditions from '@/components/CurrentConditions/CurrentConditions';
import { DailyForecast } from '@/components/DailyForecast';
import AdBlock from '@/blocks/Ad/AdBlock';
import { getContextualizedPage } from '@/utils/contextParameters/getContextualizedPage';
import { getContextParams } from '@/utils/contextParameters/extractParameters';
import { AtomHydrationBoundaries } from '@/components/AtomHydrationBoundaries';
import { mockContentQuery, mockContentQuery2 } from '../contentQuery';
import ContentMediaBlock from '@/blocks/ContentMedia/ContentMediaBlock';
import { DebugCollector } from '@/components/FrontendAdminHeader/collectors/DebugCollector';
import { getLocaleFromPathParams } from '@/utils/getLocaleFromPathParams';
import { renderBlock } from '@/utils/renderBlock';
import { Metadata } from 'next';
import { WebPageJsonLd } from '@/components/JsonLd/JsonLd';
import {
	buildPageData,
	buildPageMetadata,
} from '@/collections/Pages/utils/pageData';
import Helios from '@/components/Helios';
import { getServerSideURL } from '@/utils/getURL';

interface HomeParams {
	locale?: string;
	code: string;
}

interface HomeProps {
	params: Promise<HomeParams>;
}

const pageKey = '/home';

export const dynamic = 'force-static';

function getHomeUrl(locale: string): string {
	const url =
		locale === 'en-US' ? getServerSideURL() : `${getServerSideURL()}/${locale}`;

	return url;
}

export const generateMetadata = async ({
	params,
}: HomeProps): Promise<Metadata> => {
	const { code, locale: localeParam } = await params;
	const locale = getLocaleFromPathParams(localeParam);
	const contextParams = await getContextParams(code);
	const contextParamsResults = await getContextualizedPage({
		...contextParams,
		locale,
		pageKey,
	});

	return await buildPageMetadata(
		contextParamsResults?.page,
		{
			title:
				'National and Local Weather Radar, Daily Forecast, Hurricane and information from The Weather Channel and weather.com',
			description:
				'The Weather Channel and weather.com provide a national and local weather forecast for cities, as well as weather radar, report and hurricane coverage',
		},
		getHomeUrl(locale),
	);
};

export default async function Home({ params }: HomeProps) {
	'use cache';

	const { code, locale: localeParam } = await params;

	cacheTag(code);

	const locale = getLocaleFromPathParams(localeParam);
	const contextParams = await getContextParams(code);
	const contextParamsResults = await getContextualizedPage({
		...contextParams,
		locale,
		pageKey,
	});
	const pageLayout = contextParamsResults?.page?.content.layout;
	const hasMatchingLayouts = Array.isArray(pageLayout) && pageLayout.length > 0;
	const pageData = buildPageData(contextParamsResults?.page, locale);

	return (
		<>
			<DebugCollector
				componentName="HomePage"
				data={{
					pageType: 'home',
					locale,
					context: {
						match: contextParamsResults?.match,
						requestedParams: contextParamsResults?.requestedParams,
					},
				}}
				page={pageData}
			/>
			<AtomHydrationBoundaries pageId="home" pageLocale={locale} />
			<Helios code={code} />
			{/* labBG and wx-hero-content divs required for WX_WindowShade ads */}
			<div id="labBG" className="w-full" />
			<div id="wx-hero-content" />
			<div className="mx-auto max-w-7xl px-4 py-4">
				{/* WindowShade (Top banner) advertisement */}
				<AdBlock adId="WX_WindowShade" />
				<WebPageJsonLd name={pageData.title || ''} url={getHomeUrl(locale)} />

				{/* Render matching layouts from context parameters if available */}
				{hasMatchingLayouts ? (
					<div className="flex flex-col gap-4 md:flex-row">
						{/* Main content area */}
						<div className="flex flex-col space-y-4 md:w-[70%]">
							{/* Render main region blocks from matching layouts */}
							{pageLayout
								.filter((layout) => layout.region === 'main')
								.map((layout, index) => {
									const { blocks } = layout;
									return (
										<Fragment key={`${layout.id}-${index}`}>
											{blocks?.map((block, blockIndex) => {
												return renderBlock(block, `main-block-${blockIndex}`);
											})}
										</Fragment>
									);
								})}
						</div>

						{/* Sidebar area */}
						<div className="flex flex-col space-y-4 md:w-[30%]">
							{/* Render sidebar region blocks from matching layouts */}
							{pageLayout
								.filter((layout) => layout.region === 'sidebar')
								.map((layout, index) => {
									const { blocks } = layout;
									return (
										<Fragment key={`${layout.id}-${index}`}>
											{blocks?.map((block, blockIndex) => {
												return renderBlock(
													block,
													`sidebar-block-${blockIndex}`,
												);
											})}
										</Fragment>
									);
								})}
						</div>
					</div>
				) : (
					<DefaultPage />
				)}
			</div>
		</>
	);
}

const DefaultPage = () => (
	<div className="flex flex-col gap-4 md:flex-row">
		{/* Left content area (70-75% width) */}
		<div className="flex flex-col space-y-4 md:w-[70%]">
			{/* Weather information card */}
			<CurrentConditions />

			{/* Daily Forecast section */}
			<DailyForecast />

			{/* Map/Radar section */}
			<ContentMediaBlock
				contentQuery={mockContentQuery}
				title="Stay Up To Date"
				blockType="ContentMedia"
			/>
		</div>

		{/* Right sidebar (25-30% width) */}
		<div className="flex flex-col space-y-4 md:w-[30%]">
			{/* Advertisement placeholder with subtle label */}
			<AdBlock
				adId="WX_Top300Variable"
				variant="sidebar"
				title="Advertisement"
				height="300px"
				className="mb-4"
			/>

			{/* From Bad To Worse section */}
			<ContentMediaBlock
				contentQuery={mockContentQuery2}
				title="Winter Wellness"
				blockType="ContentMedia"
			/>

			{/* Advertisement */}
			<AdBlock
				adId="WX_Mid300"
				variant="sidebar"
				title="Advertisement"
				height="300px"
				className="mb-4"
			/>
		</div>
	</div>
);
