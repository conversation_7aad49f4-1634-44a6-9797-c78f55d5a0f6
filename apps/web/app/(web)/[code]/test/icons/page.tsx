import React from 'react';
import '@repo/ui/globals.css';
import { WxIcon } from '@repo/icons/WxIcon/WxIcon';
import { AlertIcon } from '@repo/icons/AlertIcon/AlertIcon';

// Import specific icon components from Avatar category
import { UserAvatar, UserAvatarFilled } from '@repo/icons/components/Avatar';

// Import specific icon components from Weather category
import {
	Humidity,
	Pressure,
	RainDrop,
	Temperature,
	Wind,
} from '@repo/icons/components/Weather';
import Link from '@/components/Link';

export default function IconTestPage() {
	return (
		<div className="container mx-auto p-8">
			<h1 className="mb-8 text-3xl font-bold">Icon System Test Page</h1>

			{/* Avatar Icons */}
			<section className="mb-12">
				<h2 className="mb-4 text-2xl font-semibold">Avatar Icons</h2>
				<div className="grid grid-cols-2 gap-6 rounded-lg bg-gray-100 p-4 md:grid-cols-4">
					<IconExample
						label="User Avatar"
						icon={<UserAvatar className="h-8 w-8" />}
					/>
					<IconExample
						label="User Avatar Circle"
						icon={<UserAvatarFilled className="h-8 w-8" />}
					/>
				</div>
			</section>

			{/* Weather Icons */}
			<section className="mb-12">
				<h2 className="mb-4 text-2xl font-semibold">Weather Icons</h2>
				<div className="grid grid-cols-2 gap-6 rounded-lg bg-gray-100 p-4 md:grid-cols-5">
					<IconExample
						label="Pressure"
						icon={<Pressure className="h-8 w-8" />}
					/>
					<IconExample
						label="Humidity"
						icon={<Humidity className="h-8 w-8" />}
					/>
					<IconExample
						label="RainDrop"
						icon={<RainDrop className="h-8 w-8" />}
					/>
					<IconExample
						label="Temperature"
						icon={<Temperature className="h-8 w-8" />}
					/>
					<IconExample label="Wind" icon={<Wind className="h-8 w-8" />} />
				</div>
			</section>

			{/* Icon Sizes */}
			<section className="mb-12">
				<h2 className="mb-4 text-2xl font-semibold">Icon Sizes</h2>
				<div className="flex items-end gap-6 rounded-lg bg-gray-100 p-4">
					<IconExample
						label="Small (16px)"
						icon={<Temperature className="h-4 w-4" />}
					/>
					<IconExample
						label="Medium (24px)"
						icon={<Temperature className="h-6 w-6" />}
					/>
					<IconExample
						label="Large (32px)"
						icon={<Temperature className="h-8 w-8" />}
					/>
					<IconExample
						label="Extra Large (64px)"
						icon={<Temperature className="h-16 w-16" />}
					/>
				</div>
			</section>

			{/* Icon Colors */}
			<section className="mb-12">
				<h2 className="mb-4 text-2xl font-semibold">Icon Colors</h2>
				<div className="grid grid-cols-2 gap-6 rounded-lg bg-gray-100 p-4 md:grid-cols-4">
					<IconExample label="Default" icon={<Wind className="h-8 w-8" />} />
					<IconExample
						label="Brand Color"
						icon={<Wind className="text-brand-dark h-8 w-8" />}
					/>
					<IconExample
						label="Custom Color"
						icon={<Wind className="h-8 w-8 text-blue-500" />}
					/>
					<IconExample
						label="Inverse"
						icon={
							<Wind className="h-8 w-8 rounded bg-gray-800 p-2 text-white" />
						}
					/>
				</div>
			</section>

			{/* WxIcon Usage */}
			<section className="mb-12">
				<h2 className="mb-4 text-2xl font-semibold">WxIcon Usage</h2>
				<div className="grid grid-cols-2 gap-6 rounded-lg bg-gray-100 p-4 md:grid-cols-4">
					<IconExample
						label="Sunny (32)"
						icon={<WxIcon iconCode={32} size="lg" />}
					/>
					<IconExample
						label="Cloudy (26)"
						icon={<WxIcon iconCode={26} size="lg" />}
					/>
					<IconExample
						label="Thunderstorms (4)"
						icon={<WxIcon iconCode={4} size="lg" />}
					/>
					<IconExample
						label="Snow (16)"
						icon={<WxIcon iconCode={16} size="lg" />}
					/>
				</div>
			</section>

			{/* WxIcon Themes */}
			<section className="mb-12">
				<h2 className="mb-4 text-2xl font-semibold">WxIcon Themes</h2>
				<div className="grid grid-cols-2 gap-6 rounded-lg bg-gray-100 p-4 md:grid-cols-4">
					<IconExample
						label="Light BG"
						icon={<WxIcon iconCode={32} size="lg" iconTheme="lightBG" />}
					/>
					<IconExample
						label="Dark BG"
						icon={
							<WxIcon
								iconCode={32}
								size="lg"
								iconTheme="darkBG"
								className="rounded bg-gray-800 p-2"
							/>
						}
					/>
					<IconExample
						label="White"
						icon={
							<WxIcon
								iconCode={32}
								size="lg"
								iconTheme="white"
								className="rounded bg-gray-800 p-2"
							/>
						}
					/>
					<IconExample
						label="Panther"
						icon={
							<WxIcon
								iconCode={32}
								size="lg"
								iconTheme="panther"
								className="rounded bg-gray-800 p-2"
							/>
						}
					/>
				</div>
			</section>

			{/* Alert Icons */}
			<section className="mb-12">
				<h2 className="mb-4 text-2xl font-semibold">Alert Icons</h2>
				<div className="grid grid-cols-2 gap-6 rounded-lg bg-gray-100 p-4 md:grid-cols-4">
					<IconExample
						label="Freezing Rain"
						icon={<WxIcon iconCode={10} iconCodeExtend={1000} size="lg" />}
					/>
					<IconExample
						label="Heavy Snow"
						icon={<WxIcon iconCode={16} iconCodeExtend={4200} size="lg" />}
					/>
					<IconExample
						label="Blizzard"
						icon={<WxIcon iconCode={43} iconCodeExtend={4300} size="lg" />}
					/>
					<IconExample
						label="Thunder Snow"
						icon={<WxIcon iconCode={16} iconCodeExtend={5600} size="lg" />}
					/>
				</div>
			</section>

			{/* Alert Icon */}
			<section className="mb-12">
				<h2 className="mb-4 text-2xl font-semibold">Alert Icon</h2>
				<div className="grid grid-cols-2 gap-6 rounded-lg bg-gray-100 p-4 md:grid-cols-4">
					<IconExample
						label="Alert Level 1"
						icon={<AlertIcon alertLevel={1} className="h-8 w-8" />}
					/>
					<IconExample
						label="Alert Level 2"
						icon={<AlertIcon alertLevel={2} className="h-8 w-8" />}
					/>
					<IconExample
						label="Alert Level 3"
						icon={<AlertIcon alertLevel={3} className="h-8 w-8" />}
					/>
					<IconExample
						label="Custom Color"
						icon={
							<AlertIcon alertLevel={1} className="h-8 w-8 text-blue-500" />
						}
					/>
				</div>
			</section>

			{/* Accessibility Features */}
			<section className="mb-12">
				<h2 className="mb-4 text-2xl font-semibold">Accessibility Features</h2>
				<div className="grid grid-cols-2 gap-6 rounded-lg bg-gray-100 p-4 md:grid-cols-3">
					<IconExample
						label="With Title"
						icon={<Humidity className="h-8 w-8" title="Humidity Icon" />}
					/>
					<IconExample
						label="With Title & Description"
						icon={
							<Wind
								className="h-8 w-8"
								title="Wind"
								desc="Wind speed indicator"
							/>
						}
					/>
					<IconExample
						label="Decorative (aria-hidden)"
						icon={<Temperature className="h-8 w-8" aria-hidden="true" />}
					/>
				</div>
			</section>

			<div className="mt-8 rounded-lg bg-blue-100 p-4">
				<p>
					This page demonstrates the new icon system with non-destructive
					optimization. Original SVGs are preserved in src/svg while optimized
					versions are generated in src/assets.
				</p>
				<p className="mt-2">
					Check out the{' '}
					<Link href="/test/icons/client" className="text-blue-600 underline">
						Client Component Examples
					</Link>{' '}
					to see SWR implementation.
				</p>
			</div>
		</div>
	);
}

// Helper component for consistent display
function IconExample({
	icon,
	label,
}: {
	icon: React.ReactNode;
	label: string;
}) {
	return (
		<div className="flex flex-col items-center">
			{icon}
			<span className="mt-2 text-sm">{label}</span>
		</div>
	);
}
