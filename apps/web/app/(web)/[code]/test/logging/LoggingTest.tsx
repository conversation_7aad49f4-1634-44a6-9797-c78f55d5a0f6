'use client';

import { createLogger } from '@repo/logger';

const logger = createLogger('Test');

export const LoggingTest = () => {
	logger('this is logger without any specific method');
	logger.info('this is info message');
	logger.warn('this is warn message');
	logger.error('this should be an error', new Error('test error message'));
	logger.lifecycle('this is a lifecycle message');

	return (
		<div>
			<p>
				In order for logs to show up, open the Developer Console. <br />
				Type in <code>localStorage.debug = &apos;*&apos;;</code>
			</p>
			<p>
				Change the * to any namespace you specify. Eg. <code>wx-next:*</code> or{' '}
				<code>wx-next:*:Test</code>
			</p>
		</div>
	);
};
