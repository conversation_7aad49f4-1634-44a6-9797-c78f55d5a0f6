import React, { Fragment } from 'react';
import { notFound } from 'next/navigation';
import { draftMode } from 'next/headers';
import RefreshRouteOnSave from '@/components/Payload/RefreshRoute';
import Blocks from '@/components/Blocks/Blocks';
import { DebugCollector } from '@/components/FrontendAdminHeader/collectors/DebugCollector';
import { getLocaleFromPathParams } from '@/utils/getLocaleFromPathParams';
import { WebPageJsonLd } from '@/components/JsonLd/JsonLd';
import {
	buildPageData,
	buildPageMetadata,
} from '@/collections/Pages/utils/pageData';
import Helios from '@/components/Helios';
import { getServerSideURL } from '@/utils/getURL';
import { queryPageByAssetId } from '../../../utils/getPayloadPage';

export const dynamic = 'force-static';

interface PageParams {
	locale?: string;
	assetId: string;
}

interface PageProps {
	params: Promise<PageParams>;
}

function getPageUrl(locale: string, assetName: string): string {
	const url = `${getServerSideURL()}${assetName}`;
	return url;
}

export async function generateMetadata({ params }: PageProps) {
	const segments = await params;
	const locale = getLocaleFromPathParams(segments.locale);
	const assetId = segments.assetId;
	const page = await queryPageByAssetId({
		assetId,
	});

	if (!page) {
		return notFound();
	}

	return await buildPageMetadata(
		page,
		undefined,
		getPageUrl(locale, page.assetName),
	);
}

export default async function Page({ params }: PageProps) {
	const { isEnabled: draft } = await draftMode();
	const segments = await params;
	const locale = getLocaleFromPathParams(segments.locale);
	const assetId = segments.assetId;
	const page = await queryPageByAssetId({
		assetId,
	});

	if (!page) {
		return notFound();
	}

	const pageData = buildPageData(page, locale, {
		title: page.title,
		id: page.id,
		assetName: page.assetName,
		collection: 'pages',
	});

	const sidebar = page.content.layout?.find(
		(config) => config.region === 'sidebar',
	);
	const main = page.content.layout?.find((config) => config.region === 'main');

	return (
		<Fragment>
			{draft && <RefreshRouteOnSave />}
			<>
				<DebugCollector
					componentName="PageComponent"
					data={{ pageType: 'content' }}
					page={pageData}
				/>
				<WebPageJsonLd
					name={pageData.title || ''}
					url={getPageUrl(locale, page.assetName)}
				/>
				<Helios page={page} />
				<div className="mx-auto max-w-7xl space-y-4 px-4">
					{/* Full width area (70-75% width) */}
					{page.title && <h1 className="text-2xl">{page.title}</h1>}\
					<div className="flex flex-col gap-4 md:flex-row">
						{/* Left content area (70-75% width) */}
						<div className="space-y-4 md:w-[70%]">
							<Blocks blocks={main ? main.blocks : null} />
						</div>

						{/* Right sidebar (25-30% width) */}
						<div className="md:w-[30%]">
							<Blocks blocks={sidebar ? sidebar.blocks : null} />
						</div>
					</div>
				</div>
			</>
		</Fragment>
	);
}
