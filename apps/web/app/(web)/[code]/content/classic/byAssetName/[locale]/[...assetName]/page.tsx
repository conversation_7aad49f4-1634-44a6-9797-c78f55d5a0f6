import React from 'react';
import { unstable_cacheTag as cacheTag } from 'next/cache';
import { notFound } from 'next/navigation';
import { getLexicalDsxArticleByAssetName } from '@/app/(web)/[code]/content/classic/utils/getLexicalDsxArticle';
import { AtomHydrationBoundaries } from '@/components/AtomHydrationBoundaries';
import { ArticleHeader } from '@/components/ArticleHeader/ArticleHeader';
import { getLocaleFromPathParams } from '@/utils/getLocaleFromPathParams';
import { DebugCollector } from '@/components/FrontendAdminHeader/collectors/DebugCollector';
import { getContextualizedPage } from '@/utils/contextParameters/getContextualizedPage';
import { Metadata } from 'next';
import { ArticleJsonLd } from '@/components/JsonLd/JsonLd';
import TaboolaBlock from '@/blocks/Taboola/TaboolaBlock';
import { deviceClassFlag, precomputeFlags } from '@/flags';
import {
	buildArticlePageMetadata,
	getArticlePageDebugData,
} from '@/collections/Articles/utils/articleData';
import ArticleBody from '@/components/ArticleBody/ArticleBody';
import Helios from '@/components/Helios';
import { getContextParams } from '@/utils/contextParameters/extractParameters';

const pageKey = '/content';

export const dynamic = 'force-static';

interface ClassicArticleByAssetNameParams {
	locale?: string;
	assetName: string[];
	code: string;
}

interface ClassicArticleByAssetNameProps {
	params: Promise<ClassicArticleByAssetNameParams>;
}

export async function generateMetadata({
	params,
}: ClassicArticleByAssetNameProps): Promise<Metadata> {
	const segments = await params;
	const assetName = `/${segments.assetName.join('/')}`;
	const locale = getLocaleFromPathParams(segments.locale);
	const article = await getLexicalDsxArticleByAssetName(assetName, locale);

	return buildArticlePageMetadata(article);
}

export default async function ClassicArticlePage({
	params,
}: ClassicArticleByAssetNameProps) {
	'use cache';

	const { code, locale: localeParam, assetName: assetNameParam } = await params;

	cacheTag(code);

	const assetName = `/${assetNameParam.join('/')}`;
	const locale = getLocaleFromPathParams(localeParam);
	const article = await getLexicalDsxArticleByAssetName(assetName, locale);

	if (!article) {
		return notFound();
	}

	const articleDebugData = getArticlePageDebugData(article);
	const contextParams = await getContextParams(code);
	const contextParamsResults = await getContextualizedPage({
		...contextParams,
		locale,
		pageKey,
	});

	const deviceClass = await deviceClassFlag(code, precomputeFlags);
	const isMobile = deviceClass === 'mobile';

	return (
		<article className="prose w-full max-w-4xl rounded-md bg-white p-5">
			<DebugCollector
				componentName="ClassicArticlePage"
				data={{
					pageType: 'content',
					locale,
					context: {
						match: contextParamsResults?.match,
					},
				}}
				page={articleDebugData}
			/>

			<AtomHydrationBoundaries
				pageId={'article'}
				pageLocale={locale}
				metricsArticleData={{
					id: article?.id,
					authors: article?.authors,
					category: article?.category,
					createdAt: article?.createdAt,
					publishDate: article?.publishDate,
					coreMetadata: article?.coreMetadata,
				}}
			/>
			<Helios code={code} article={article} />
			<TaboolaBlock
				article={article}
				placements={[
					{
						mode: 'thumbnails-d',
						container: 'taboola-below-content-thumbnails-article',
						placement: 'Below Content Thumbnails - article',
						target_type: 'mix',
					},
					...(!isMobile
						? [
								{
									mode: 'thumbnails-rr2',
									container: 'taboola-right-rail-thumbnails---article',
									placement: 'Right Rail Thumbnails - article',
									target_type: 'mix',
								},
							]
						: []),
				]}
				pageType="article"
			/>
			<ArticleJsonLd article={article} />
			<ArticleHeader article={article} />
			<ArticleBody article={article} classic />
		</article>
	);
}
