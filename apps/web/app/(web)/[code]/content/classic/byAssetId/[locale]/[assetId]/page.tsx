import React from 'react';
import { unstable_cacheTag as cacheTag } from 'next/cache';
import { notFound } from 'next/navigation';
import { getLexicalDsxArticleByAssetId } from '@/app/(web)/[code]/content/classic/utils/getLexicalDsxArticle';
import { AtomHydrationBoundaries } from '@/components/AtomHydrationBoundaries';
import { ArticleHeader } from '@/components/ArticleHeader/ArticleHeader';
import {
	buildArticlePageMetadata,
	getArticlePageDebugData,
} from '@/collections/Articles/utils/articleData';
import { DebugCollector } from '@/components/FrontendAdminHeader/collectors/DebugCollector';
import { getContextualizedPage } from '@/utils/contextParameters/getContextualizedPage';
import { getLocaleFromPathParams } from '@/utils/getLocaleFromPathParams';
import { ArticleJsonLd } from '@/components/JsonLd/JsonLd';
import ArticleBody from '@/components/ArticleBody/ArticleBody';
import Helios from '@/components/Helios';
import { getContextParams } from '@/utils/contextParameters/extractParameters';

const pageKey = '/content';

export const dynamic = 'force-static';

interface ClassicArticleByAssetIdParams {
	locale?: string;
	assetId: string;
	code: string;
}

interface ClassicArticleByAssetIdProps {
	params: Promise<ClassicArticleByAssetIdParams>;
}

export async function generateMetadata({
	params,
}: ClassicArticleByAssetIdProps) {
	const segments = await params;
	const locale = getLocaleFromPathParams(segments.locale);
	const article = await getLexicalDsxArticleByAssetId(segments.assetId, locale);

	return buildArticlePageMetadata(article);
}

export default async function ClassicArticlePage({
	params,
}: ClassicArticleByAssetIdProps) {
	'use cache';

	const { code, locale: localeParam, assetId } = await params;

	cacheTag(code);

	const locale = getLocaleFromPathParams(localeParam);
	const article = await getLexicalDsxArticleByAssetId(assetId, locale);

	if (!article) {
		return notFound();
	}

	const articleDebugData = getArticlePageDebugData(article);
	const contextParams = await getContextParams(code);
	const contextParamsResults = await getContextualizedPage({
		...contextParams,
		locale,
		pageKey,
	});

	return (
		<article className="prose w-full max-w-4xl rounded-md bg-white p-5">
			<DebugCollector
				componentName="ClassicArticlePage"
				data={{
					pageType: 'content',
					locale,
					context: {
						match: contextParamsResults?.match,
					},
				}}
				page={articleDebugData}
			/>
			<AtomHydrationBoundaries
				pageId="content"
				pageLocale={locale}
				metricsArticleData={{
					id: article?.id,
					authors: article?.authors,
					category: article?.category,
					createdAt: article?.createdAt,
					publishDate: article?.publishDate,
					coreMetadata: article?.coreMetadata,
				}}
			/>
			<Helios code={code} article={article} />
			<ArticleJsonLd article={article} />
			<ArticleHeader article={article} />
			<ArticleBody article={article} classic />
		</article>
	);
}
