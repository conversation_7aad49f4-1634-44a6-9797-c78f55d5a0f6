import { describe, it, expect, vi, test, beforeEach, afterEach } from 'vitest';
import * as assetModule from '@repo/dal/content/assets/byId';
import * as videoModule from '@repo/dal/content/videos/byIds';
import * as collectionModule from '@repo/dal/content/videos/byCollectionName';
import * as slideshowModule from '@repo/dal/content/slideshow/assets';
import * as lexicalModule from './transformHtmlStringToLexical';
import * as handleApiCallModule from '@/utils/handleApiCall';
import {
	type DSXArticle,
	type DSXWxNode,
	type DSXWxNodeSlideshow,
} from '@repo/dal/content/articles/types';
import type { DSXVideo } from '@repo/dal/content/videos/types';
import {
	transformDsxArticle,
	processWxNodeData,
	getCtxFromDsx,
	convertFiguresToSyntheticWxNodes,
	type DSXCollection,
} from './transformDsxArticle';
import type { Article } from '@/payload-types';
import { dsxArticleMock } from '@repo/mocks';

// --- Helper Functions ---

// Helper function to create a mock image asset
const createMockImageAsset = (overrides: Partial<any> = {}) => ({
	// Use 'any' to avoid defining all properties
	id: 'image123',
	variants: { '0': 'https://example.com/image.jpg' },
	caption: 'Asset caption',
	imageByline: 'Asset credit',
	alt_text: 'Asset alt text',
	schema_version: '1.0',
	type: 'image',
	assetName: 'test-image',
	...overrides,
});

// Deep clone the partnerByline and recursively replace all IDs with 'mockedId'
const mockIds = (obj: any): any => {
	if (!obj || typeof obj !== 'object') return obj;

	// If it's an array, map over its elements
	if (Array.isArray(obj)) {
		return obj.map((item) => mockIds(item));
	}

	// If it's an object, process each property
	const result = { ...obj };
	if (result.id) {
		result.id = 'mockedId';
	}

	// Process nested properties
	Object.keys(result).forEach((key) => {
		if (typeof result[key] === 'object' && result[key] !== null) {
			result[key] = mockIds(result[key]);
		}
	});

	return result;
};

// Helper function to create a mock slideshow asset
const createMockSlideshow = (overrides: Partial<any> = {}) => ({
	// Use 'any'
	id: 'slideshow123',
	title: 'Test Slideshow',
	assets: [],
	schema_version: '1.0',
	type: 'slideshow',
	author: 'Test Author',
	providerid: 'test-provider',
	publishDate: '2025-01-01',
	lastmodifieddate: '2025-01-02',
	providername: 'Test Provider',
	colls: [],
	tags: {
		keyword: [],
		iab: { v1: [], v2: [], v3: [] },
		storm: [],
		severe: '',
		locations: { regions: [], states: [] },
	},
	flags: {},
	...overrides,
});

// Helper function to create a mock video asset
const createMockVideoAsset = (overrides: Partial<any> = {}) => ({
	id: 'video123',
	title: 'Test Video',
	teaserTitle: 'Test Video Teaser',
	description: 'Test video description',
	variants: { '0': 'https://example.com/video-thumbnail.jpg' },
	format_urls: {
		m3u8: 'https://example.com/video.m3u8',
		mp4: 'https://example.com/video.mp4',
	},
	cc_url: 'https://example.com/captions.vtt',
	schema_version: '1.0',
	type: 'video',
	assetName: 'test-video',
	...overrides,
});

// Mock the modules that make network requests
vi.mock('@repo/dal/content/assets/byId', () => ({
	getAssetById: vi.fn(),
}));

vi.mock('@repo/dal/content/videos/byIds', () => ({
	getVideosByIds: vi.fn(),
}));

vi.mock('server-only', () => {
	return {
		// mock server-only module
	};
});

vi.mock('@repo/dal/content/videos/byCollectionName', () => ({
	getOrderedCollectionByCollectionName: vi.fn(),
	getVideosByCollectionName: vi.fn(),
}));

vi.mock('@repo/dal/content/slideshow/assets', () => ({
	getSlideshowAssets: vi.fn(),
}));

// Mock the handleApiCall utility
vi.mock('@/utils/handleApiCall', () => ({
	handleApiCall: vi.fn(),
	is400Error: vi.fn(),
}));

// --- Test Suites ---
describe('transformDsxArticle', () => {
	it('should set header layout to default for dsx articles', async () => {
		const article: Article = await transformDsxArticle(
			dsxArticleMock as unknown as DSXArticle,
			'en-US',
		);

		expect(article.headerLayout).toBe('default');
	});

	it('should transform article sub headline correctly', async () => {
		const subHeadlineMock = 'Sub Headline';
		const dsxAuthorsData = {
			...dsxArticleMock,
			subHeadline: subHeadlineMock,
			seometa: { description: subHeadlineMock },
		} as unknown as DSXArticle;

		const article: Article = await transformDsxArticle(dsxAuthorsData, 'en-US');

		expect(article.subHeadline).toBe(subHeadlineMock);
	});

	it('should transform article sub headline correctly when subHeadline is missing', async () => {
		const subHeadlineMock = 'Sub Headline';
		const dsxAuthorsData = {
			...dsxArticleMock,
			subHeadline: undefined,
			seometa: { description: subHeadlineMock },
		} as unknown as DSXArticle;

		const article: Article = await transformDsxArticle(dsxAuthorsData, 'en-US');

		expect(article.subHeadline).toBe(subHeadlineMock);
	});

	it('should transform article sub headline correctly when subHeadline and seometa description are missing', async () => {
		const dsxAuthorsData = {
			...dsxArticleMock,
			subHeadline: undefined,
			seometa: { description: undefined },
		} as unknown as DSXArticle;

		const article: Article = await transformDsxArticle(dsxAuthorsData, 'en-US');

		expect(article.subHeadline).toBe(undefined);
	});

	it('should transform article category correctly', async () => {
		const dsxAuthorsData = {
			...dsxArticleMock,
			pcollid: 'news/weather',
		} as unknown as DSXArticle;

		const article: Article = await transformDsxArticle(dsxAuthorsData, 'en-US');

		expect(article.category).toEqual({
			relationTo: 'tags',
			value: {
				fullPath: 'news/weather',
				id: 'news/weather',
				name: 'Weather News',
				slug: 'Weather News',
			},
		});
	});

	it('should transform article category correctly when pcollid is missing', async () => {
		const dsxAuthorsData = {
			...dsxArticleMock,
			pcollid: '',
		} as unknown as DSXArticle;

		const article: Article = await transformDsxArticle(dsxAuthorsData, 'en-US');

		expect(article.category).toEqual({
			relationTo: 'tags',
			value: {
				id: '',
				name: '',
			},
		});
	});

	it('should transform partner byline correctly', async () => {
		const partner_byline = [
			'<p><em>This article was medically reviewed by </em><a href="https://weather.com/bios/medical-reviewer/news/2024-10-23-tiffany-clay-ramsey"><em>Tiffany Clay-Ramsey, MD, FAAD</em></a><em>.</em></p>',
		];

		const dsxAuthorsData = {
			...dsxArticleMock,
			partner_byline,
		} as unknown as DSXArticle;

		const article: Article = await transformDsxArticle(dsxAuthorsData, 'en-US');

		const partnerBylineWithMockedIds = mockIds(article.partnerByline);

		const expectedPartnerByline = {
			type: 'text',
			text: '<p><em>This article was medically reviewed by </em><a href="https://weather.com/bios/medical-reviewer/news/2024-10-23-tiffany-clay-ramsey"><em>Tiffany Clay-Ramsey, MD, FAAD</em></a><em>.</em></p>',
		};

		// Use toEqual instead of toBe for deep object comparison
		expect(partnerBylineWithMockedIds).toEqual(expectedPartnerByline);
	});

	// Setup mock data
	const mockDsxArticle = {
		id: 'test-article-id',
		title: 'Test Article',
		body: '<div>Test content</div>',
		wxnodes: [
			{
				id: 'wxn1',
				type: 'wxnode_internal_image',
				assetid: 'test-image-id',
				synopsis: 'Test image caption',
				credit: 'Test credit',
			},
		],
		author_data: [
			{
				fullName: 'John Doe',
				email: '<EMAIL>',
				byline: 'John Doe',
				authorImage: [
					{
						variants: ['https://example.com/image.jpg'],
						alt_text: 'John Doe',
					},
				],
			},
		],
		lastmodifieddate: new Date().toString(),
		publishdate: new Date().toString(),
		variants: { '0': 'https://example.com/featured.jpg' },
		seometa: {
			title: 'SEO Title',
			description: 'SEO Description',
		},
	};

	const locale = 'en-US';

	// Create a properly typed network error
	interface NetworkError extends Error {
		code?: string;
		syscall?: string;
		hostname?: string;
	}

	const networkError = new Error(
		'getaddrinfo EBUSY dsx.weather.com',
	) as NetworkError;
	networkError.code = 'EBUSY';
	networkError.syscall = 'getaddrinfo';
	networkError.hostname = 'dsx.weather.com';

	// Create a 400-level error
	const notFoundError = new Error('Not found (status 404)');

	// Create a 500-level error
	const serverError = new Error('Server error (status 500)');

	// Reset mocks before each test
	beforeEach(() => {
		vi.resetAllMocks();

		// Default mock for handleApiCall - pass through the promise result
		vi.mocked(handleApiCallModule.handleApiCall).mockImplementation(
			async (promise) => {
				try {
					const data = await promise;
					return { data, error: null };
				} catch (err) {
					const error = err as Error;
					if (handleApiCallModule.is400Error(error)) {
						return { data: null, error };
					}
					throw error;
				}
			},
		);

		// Mock is400Error to check for 400-level status codes
		vi.mocked(handleApiCallModule.is400Error).mockImplementation(
			(error: Error) => {
				const errorMessage = error.message || '';
				return (
					errorMessage.includes('status 400') ||
					errorMessage.includes('status 401') ||
					errorMessage.includes('status 403') ||
					errorMessage.includes('status 404')
				);
			},
		);
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	it('should successfully transform a DSX article', async () => {
		// Mock successful API responses
		const mockImageAsset = {
			variants: { '0': 'https://example.com/image.jpg' },
			caption: 'Test caption',
			imageByline: 'Test byline',
			alt_text: 'Test alt text',
		};

		vi.mocked(assetModule.getAssetById).mockResolvedValue(mockImageAsset);

		// Call the function
		const result = await transformDsxArticle(
			mockDsxArticle as unknown as DSXArticle,
			locale,
		);

		// Verify the result
		expect(result).toBeDefined();
		expect(result.id).toBe(mockDsxArticle.id);
		expect(result.title).toBe(mockDsxArticle.title);
		expect(result.featuredImage).toBeDefined();
		// Type assertion to handle the featuredImage type
		if (typeof result.featuredImage !== 'string') {
			expect(result.featuredImage.url).toBe(mockDsxArticle.variants['0']);
		}
		expect(result.authors).toBeDefined();
		if (result.authors && result.authors.length > 0) {
			const author = result.authors[0];
			if (author && typeof author !== 'string') {
				expect(author.email).toBe('<EMAIL>');
			}
		}
	});

	it('should handle 400-level errors transparently and continue execution', async () => {
		// Mock getAssetById to be wrapped by handleApiCall that returns a 404 error
		vi.mocked(assetModule.getAssetById).mockRejectedValue(notFoundError);

		// Call the function - it should not throw
		const result = await transformDsxArticle(
			mockDsxArticle as unknown as DSXArticle,
			locale,
		);

		// Verify we got a valid result despite the 404 error
		expect(result).toBeDefined();
		expect(result.id).toBe(mockDsxArticle.id);
		expect(result.title).toBe(mockDsxArticle.title);
	});

	it('should throw when 500-level errors occur', async () => {
		// Mock getAssetById to throw a 500 error
		vi.mocked(assetModule.getAssetById).mockRejectedValue(serverError);

		// Call the function and expect it to throw
		await expect(
			transformDsxArticle(mockDsxArticle as unknown as DSXArticle, locale),
		).rejects.toThrow('Server error (status 500)');
	});

	it('should throw when network errors occur', async () => {
		// Mock getAssetById to throw a network error
		vi.mocked(assetModule.getAssetById).mockRejectedValue(networkError);

		// Call the function and expect it to throw
		await expect(
			transformDsxArticle(mockDsxArticle as unknown as DSXArticle, locale),
		).rejects.toThrow('getaddrinfo EBUSY dsx.weather.com');
	});

	it('should handle wxnode_slideshow correctly', async () => {
		// Create a mock article with a slideshow wxnode
		const mockArticleWithSlideshow = {
			...mockDsxArticle,
			wxnodes: [
				{
					id: 'wxn2',
					type: 'wxnode_slideshow',
					slideshow: 'test-slideshow-id',
					title: 'Test Slideshow Title',
				},
			],
		};

		// Mock slideshow data with assets already included as objects
		const mockSlideshowData = {
			id: 'test-slideshow-id',
			type: 'slideshow',
			locale: 'en_US',
			title: 'Test Slideshow',
			author: 'Test Author',
			schema_version: '1.0',
			assets: [
				{
					type: 'image',
					variants: { '0': 'https://example.com/slide1.jpg' },
					caption: 'Slide 1 caption',
					alt_text: 'Slide 1 alt text',
					title: 'Slide 1',
				},
				{
					type: 'image',
					variants: { '0': 'https://example.com/slide2.jpg' },
					caption: 'Slide 2 caption',
					alt_text: 'Slide 2 alt text',
					title: 'Slide 2',
				},
			],
			providerid: 'test-provider',
			publishDate: new Date().toString(),
			lastmodifieddate: new Date().toString(),
			providername: 'Test Provider',
			colls: [],
			tags: {
				keyword: [],
				iab: {
					v1: [],
					v2: [],
					v3: [],
				},
				storm: [],
				severe: '',
				locations: {
					regions: [],
					states: [],
				},
			},
			flags: {},
		};

		// Setup the mock to return slideshow data with assets included
		vi.mocked(slideshowModule.getSlideshowAssets).mockResolvedValue(
			mockSlideshowData,
		);

		// Call the function
		const result = await transformDsxArticle(
			mockArticleWithSlideshow as unknown as DSXArticle,
			locale,
		);

		// Verify the result
		expect(result).toBeDefined();
		expect(result.id).toBe(mockArticleWithSlideshow.id);

		// Check that getSlideshowAssets was called with the correct parameters
		expect(slideshowModule.getSlideshowAssets).toHaveBeenCalledWith(
			'test-slideshow-id',
			0,
			500,
		);
	});

	it('should handle wxnode_video correctly', async () => {
		// Create a mock article with a video wxnode
		const mockArticleWithVideo = {
			...mockDsxArticle,
			wxnodes: [
				{
					id: 'wxn3',
					type: 'wxnode_video',
					assetid: 'test-video-id',
				},
			],
		};

		// Mock video data
		const mockVideoData = {
			id: 'test-video-id',
			title: 'Test Video',
			description: 'Test video description',
			format_urls: {
				m3u8: 'https://example.com/video.m3u8',
			},
			variants: ['https://example.com/thumbnail.jpg'],
			playlists: [{ playlist1: 'playlist1' }],
		};

		// Mock playlist data
		const mockPlaylistVideos = ['video1', 'video2'];

		// Mock video collection with required DSXVideo properties
		const mockVideoCollection = [
			{
				id: 'video1',
				title: 'Video 1',
				format_urls: { mp4: 'https://example.com/video1.mp4' },
				variants: ['https://example.com/thumb1.jpg'],
				assetName: 'video1',
				schema_version: '1.0',
				jwpmediaid: 'jwp1',
				type: 'video',
				description: 'Video 1 description',
				publishdate: new Date().toString(),
				lastmodifieddate: new Date().toString(),
				duration: '00:01:00',
				tags: {},
				adsmetrics: {},
				locale: 'en_US',
				teaserTitle: 'Video 1',
				pcollid: 'pcoll1',
				partnercolls: [],
				premium: false,
				providername: 'Test Provider',
				cc_url: '',
			},
			{
				id: 'video2',
				title: 'Video 2',
				format_urls: { mp4: 'https://example.com/video2.mp4' },
				variants: ['https://example.com/thumb2.jpg'],
				assetName: 'video2',
				schema_version: '1.0',
				jwpmediaid: 'jwp2',
				type: 'video',
				description: 'Video 2 description',
				publishdate: new Date().toString(),
				lastmodifieddate: new Date().toString(),
				duration: '00:01:00',
				tags: {},
				adsmetrics: {},
				locale: 'en_US',
				teaserTitle: 'Video 2',
				pcollid: 'pcoll2',
				partnercolls: [],
				premium: false,
				providername: 'Test Provider',
				cc_url: '',
			},
		] as unknown as DSXVideo[];

		// Setup the mocks
		vi.mocked(assetModule.getAssetById).mockResolvedValue(mockVideoData);
		vi.mocked(
			collectionModule.getOrderedCollectionByCollectionName,
		).mockResolvedValue({
			video: mockPlaylistVideos,
		});
		vi.mocked(videoModule.getVideosByIds).mockResolvedValue(
			mockVideoCollection,
		);

		// Call the function
		const result = await transformDsxArticle(
			mockArticleWithVideo as unknown as DSXArticle,
			locale,
		);

		// Verify the result
		expect(result).toBeDefined();
		expect(result.id).toBe(mockArticleWithVideo.id);
	});

	it('should handle video collection correctly', async () => {
		// Create a mock article with a video collection wxnode
		const mockArticleWithVideoCollection = {
			...mockDsxArticle,
			wxnodes: [
				{
					id: 'wxn4',
					type: 'wxnode_video',
					collection_name: 'test-collection',
				},
			],
		};

		// Mock video collection with required DSXVideo properties
		const mockVideoCollection = [
			{
				id: 'video1',
				title: 'Video 1',
				format_urls: { mp4: 'https://example.com/video1.mp4' },
				variants: ['https://example.com/thumb1.jpg'],
				assetName: 'video1',
				schema_version: '1.0',
				jwpmediaid: 'jwp1',
				type: 'video',
				description: 'Video 1 description',
				publishdate: new Date().toString(),
				lastmodifieddate: new Date().toString(),
				duration: '00:01:00',
				tags: {},
				adsmetrics: {},
				locale: 'en_US',
				teaserTitle: 'Video 1',
				pcollid: 'pcoll1',
				partnercolls: [],
				premium: false,
				providername: 'Test Provider',
				cc_url: '',
			},
		] as unknown as DSXVideo[];

		// Setup the mock
		vi.mocked(collectionModule.getVideosByCollectionName).mockResolvedValue(
			mockVideoCollection,
		);

		// Call the function
		const result = await transformDsxArticle(
			mockArticleWithVideoCollection as unknown as DSXArticle,
			locale,
		);

		// Verify the result
		expect(result).toBeDefined();
		expect(result.id).toBe(mockArticleWithVideoCollection.id);
	});

	it('should handle HTML transformation errors gracefully', async () => {
		// Create a 400-level error for HTML transformation
		const htmlError = new Error('HTML transformation failed (status 400)');

		// Mock transformHtmlStringToLexical to throw the error
		const transformHtmlStringSpy = vi
			.spyOn(lexicalModule, 'transformHtmlStringToLexical')
			.mockRejectedValue(htmlError);

		// Spy on console.error
		const consoleErrorSpy = vi
			.spyOn(console, 'error')
			.mockImplementation(() => {});

		// Mock handleApiCall specifically for this test to handle the HTML error as a 400-level error
		vi.mocked(handleApiCallModule.handleApiCall).mockImplementation(
			async (promise) => {
				if (String(promise).includes('transformHtmlStringToLexical')) {
					return {
						data: null,
						error: htmlError,
					};
				}

				// Default implementation for other promises
				try {
					const data = await promise;
					return { data, error: null };
				} catch (err) {
					const error = err as Error;
					if (handleApiCallModule.is400Error(error)) {
						return { data: null, error };
					}
					throw error;
				}
			},
		);

		// Mock is400Error to recognize our HTML error
		vi.mocked(handleApiCallModule.is400Error).mockImplementation(
			(error: Error) => {
				return error === htmlError || error.message.includes('status 40');
			},
		);

		// Call the function - it should not throw
		const result = await transformDsxArticle(
			mockDsxArticle as unknown as DSXArticle,
			locale,
		);

		// Verify we got a valid result with a fallback lexical body
		expect(result).toBeDefined();
		expect(result.id).toBe(mockDsxArticle.id);
		expect(result.content.body).toBeDefined();
		expect(result.content.body.root).toBeDefined();
		expect(result.content.body.root.type).toBe('root');

		// restore mocks
		transformHtmlStringSpy.mockRestore();
		consoleErrorSpy.mockRestore();
	});

	it('should handle missing wxnodes gracefully', async () => {
		// Create a mock article with no wxnodes
		const mockArticleWithoutWxnodes = {
			...mockDsxArticle,
			wxnodes: undefined,
		};

		// Call the function - it should not throw
		const result = await transformDsxArticle(
			mockArticleWithoutWxnodes as unknown as DSXArticle,
			locale,
		);

		// Verify we got a valid result
		expect(result).toBeDefined();
		expect(result.id).toBe(mockArticleWithoutWxnodes.id);
	});

	it('should handle null wxnodes gracefully', async () => {
		// Create a mock article with null wxnodes
		const mockArticleWithNullWxnodes = {
			...mockDsxArticle,
			wxnodes: [
				null,
				{
					id: 'wxn1',
					type: 'wxnode_internal_image',
					assetid: 'test-image-id',
				},
			],
		};

		// Call the function - it should not throw
		const result = await transformDsxArticle(
			mockArticleWithNullWxnodes as unknown as DSXArticle,
			locale,
		);

		// Verify we got a valid result
		expect(result).toBeDefined();
		expect(result.id).toBe(mockArticleWithNullWxnodes.id);
	});

	it('should set externalAuthors when author is present but author_data is not', async () => {
		const authorMock = ['John Doe', 'Jane Smith'];
		const dsxArticleData = {
			...dsxArticleMock,
			author: authorMock,
			author_data: undefined, // Ensure author_data is not present
		} as unknown as DSXArticle;

		const article: Article = await transformDsxArticle(dsxArticleData, 'en-US');

		expect(article.authors).toEqual([]); // Should be an empty array
		expect(article.externalAuthors).toBe('John Doe, Jane Smith'); // Should join the author array
	});

	it('should prioritize author_data over author when both are present', async () => {
		const authorDataMock = [
			{
				fullName: 'John Doe',
				email: '<EMAIL>',
				byline: 'John Doe',
				authorImage: [
					{
						variants: ['https://example.com/image.jpg'],
						alt_text: 'John Doe',
					},
				],
			},
		];

		const authorMock = ['External Author'];

		const dsxArticleData = {
			...dsxArticleMock,
			author: authorMock,
			author_data: authorDataMock,
		} as unknown as DSXArticle;

		const article: Article = await transformDsxArticle(dsxArticleData, 'en-US');

		expect(article.authors).toBeDefined();
		expect(article.authors?.length).toBeGreaterThan(0);
		expect(article.externalAuthors).toBe(''); // Should be empty when author_data is present
	});
});

describe('processWxNodeData', () => {
	// Setup before each test in this describe block
	beforeEach(() => {
		// Reset all mocks
		vi.resetAllMocks();

		vi.mocked(handleApiCallModule.is400Error).mockImplementation(
			(error: Error) => {
				return error.message.includes('status 40');
			},
		);

		// Setup handleApiCall to pass through to the actual implementation
		vi.mocked(handleApiCallModule.handleApiCall).mockImplementation(
			async (promise) => {
				try {
					const data = await promise;
					return { data, error: null };
				} catch (err) {
					const error = err as Error;
					if (handleApiCallModule.is400Error(error)) {
						return { data: null, error };
					}
					throw error;
				}
			},
		);
	});

	describe('wxnode_internal_image', () => {
		it.each([
			{
				nodeSynopsis: 'Node synopsis',
				nodeCredit: 'Node credit',
				expectedCaption: 'Node synopsis',
				expectedCredit: 'Node credit',
			},
			{
				nodeSynopsis: undefined,
				nodeCredit: undefined,
				expectedCaption: 'Asset caption',
				expectedCredit: 'Asset credit',
			},
		])(
			'should transform internal image wxNode using node or asset caption/credit',
			async ({
				nodeSynopsis,
				nodeCredit,
				expectedCaption,
				expectedCredit,
			}: {
				nodeSynopsis: string | undefined;
				nodeCredit: string | undefined;
				expectedCaption: string;
				expectedCredit: string;
			}) => {
				// Arrange: Mock the asset data
				vi.mocked(assetModule.getAssetById).mockResolvedValue(
					createMockImageAsset({
						caption: 'Asset caption',
						imageByline: 'Asset credit',
					}),
				);

				// Create a wxNode for testing
				const wxNode = {
					id: 'wxn01',
					type: 'wxnode_internal_image',
					assetid: 'image123',
					synopsis: nodeSynopsis,
					credit: nodeCredit,
					schema_version: '1.0',
					assetName: 'test-wxnode',
				} as DSXWxNode;

				// Act: Process the wxNode
				const result = await processWxNodeData([wxNode], 'en-US');

				// Assert: Verify the transformation
				expect(result[0]).toEqual({
					...wxNode,
					__wxnext: {
						priority: true, // First image (index === 0) gets priority=true
						imageUrl: 'https://example.com/image.jpg',
						caption: expectedCaption,
						credit: expectedCredit,
						altText: 'Asset alt text',
						linkUrl: null,
					},
				});
				expect(assetModule.getAssetById).toHaveBeenCalledWith(
					'image123',
					'en-US',
				);
			},
		);

		it('should return the original wxNode when asset is not found', async () => {
			// Arrange: Mock getAssetById to return null
			vi.mocked(assetModule.getAssetById).mockResolvedValue(null);

			// Create a wxNode for testing
			const wxNode = {
				id: 'wxn01',
				type: 'wxnode_internal_image',
				assetid: 'image123',
				schema_version: '1.0',
				assetName: 'test-wxnode',
			} as DSXWxNode;

			// Act: Process the wxNode
			const result = await processWxNodeData([wxNode as DSXWxNode], 'en-US');

			// Assert: Verify the original wxNode is returned unchanged
			expect(result[0]).toEqual(wxNode);
		});
	});

	describe('wxnode_slideshow', () => {
		it('should transform slideshow wxNode with valid slideshow data and assets', async () => {
			// Create a wxNode for testing
			const wxNode = {
				id: 'wxn02',
				type: 'wxnode_slideshow',
				assetid: '',
				slideshow: 'slideshow123',
				title: 'Slideshow Title',
				schema_version: '1.0',
				assetName: 'test-wxnode',
			} as DSXWxNode;

			// Mock getSlideshowAssets instead of getAssetById
			vi.mocked(slideshowModule.getSlideshowAssets).mockResolvedValue({
				assets: [
					{
						type: 'image',
						variants: { '0': 'https://example.com/image.jpg' },
						caption: 'Caption 1',
						alt_text: 'Alt Text 1',
						title: 'Image 1',
					},
					{
						type: 'image',
						variants: { '0': 'https://example.com/image.jpg' },
						caption: 'Caption 2',
						alt_text: 'Alt Text 2',
						title: 'Image 2',
					},
				],
			});

			// Mock handleApiCall to return slideshow data
			vi.mocked(handleApiCallModule.handleApiCall).mockImplementation(
				async (promise) => {
					const data = await promise;
					return { data, error: null };
				},
			);

			// Act: Process the wxNode
			const result = await processWxNodeData([wxNode], 'en-US');

			// Assert: Verify the transformation
			expect(result[0]).toEqual({
				...wxNode,
				__wxnext: {
					images: [
						{
							imageUrl: 'https://example.com/image.jpg',
							caption: 'Caption 1',
							altText: 'Alt Text 1',
							id: 'int-image-1',
							title: 'Image 1',
						},
						{
							imageUrl: 'https://example.com/image.jpg',
							caption: 'Caption 2',
							altText: 'Alt Text 2',
							id: 'int-image-2',
							title: 'Image 2',
						},
					],
					title: 'Slideshow Title',
				},
			});

			// Verify getSlideshowAssets was called correctly
			expect(slideshowModule.getSlideshowAssets).toHaveBeenCalledWith(
				'slideshow123',
				0,
				500, // This matches your new implementation limit
			);
		});

		it('should return the original wxNode when slideshow has no assets', async () => {
			// Arrange: Mock the slideshow data with no assets
			vi.mocked(assetModule.getAssetById).mockResolvedValue(
				createMockSlideshow({ assets: [] }),
			);

			// Create a wxNode for testing
			const wxNode = {
				id: 'wxn02',
				type: 'wxnode_slideshow',
				assetid: '', // Required by DSXWxNode type
				slideshow: 'slideshow123',
				schema_version: '1.0',
				assetName: 'test-wxnode',
			} as DSXWxNode;

			// Act: Process the wxNode
			const result = await processWxNodeData([wxNode as DSXWxNode], 'en-US');

			// Assert: Verify the original wxNode is returned unchanged
			expect(result[0]).toEqual(wxNode);
		});

		it('should return the original wxNode when slideshow ID is missing', async () => {
			// Create a wxNode without slideshow ID
			const wxNode = {
				id: 'wxn02',
				type: 'wxnode_slideshow',
				assetid: '', // Required by DSXWxNode type
				schema_version: '1.0',
				assetName: 'test-wxnode',
			} as DSXWxNode;

			// Act: Process the wxNode
			const result = await processWxNodeData([wxNode as DSXWxNode], 'en-US');

			// Assert: Verify the original wxNode is returned unchanged
			expect(result[0]).toEqual(wxNode);
		});

		it('should return the original wxNode when slideshow asset fetch fails', async () => {
			// Arrange: Mock getAssetById to return null for slideshow
			vi.mocked(assetModule.getAssetById).mockResolvedValue(null);

			// Create a wxNode for testing
			const wxNode = {
				id: 'wxn02',
				type: 'wxnode_slideshow',
				assetid: '', // Required by DSXWxNode type
				slideshow: 'slideshow123',
				schema_version: '1.0',
				assetName: 'test-wxnode',
			} as DSXWxNode;

			// Act: Process the wxNode
			const result = await processWxNodeData([wxNode as DSXWxNode], 'en-US');

			// Assert: Verify the original wxNode is returned unchanged
			expect(result[0]).toEqual(wxNode);
		});

		it('should handle errors during slideshow processing', async () => {
			// Create a wxNode for testing
			const wxNode = {
				id: 'wxn02',
				type: 'wxnode_slideshow',
				assetid: '',
				slideshow: 'slideshow123',
				schema_version: '1.0',
				assetName: 'test-wxnode',
			} as DSXWxNodeSlideshow;

			// Create a 400-level error
			const testError = new Error('Not found (status 404)');

			// Mock handleApiCall to return the 400 error
			vi.mocked(handleApiCallModule.handleApiCall).mockResolvedValue({
				data: null,
				error: testError,
			});

			// This should return TRUE for 400 errors
			vi.mocked(handleApiCallModule.is400Error).mockReturnValue(true);

			// Spy on console.error
			const consoleErrorSpy = vi
				.spyOn(console, 'error')
				.mockImplementation(() => {});

			// Act: Process the wxNode
			const result = await processWxNodeData([wxNode], 'en-US');

			// Assert: Verify the original wxNode is returned unchanged
			expect(result[0]).toEqual(wxNode);

			// Verify error was logged
			expect(consoleErrorSpy).toHaveBeenCalledWith(
				`Error fetching slideshow data ${wxNode.slideshow}:`,
				testError,
			);

			// Clean up
			consoleErrorSpy.mockRestore();
		});

		it('should return original wxNode for any error, including non-400 errors', async () => {
			// Create a wxNode for testing
			const wxNode = {
				id: 'wxn02',
				type: 'wxnode_slideshow',
				assetid: '',
				slideshow: 'slideshow123',
				schema_version: '1.0',
				assetName: 'test-wxnode',
			} as DSXWxNodeSlideshow;

			// Create a non-400 error
			const testError = new Error('Server error (status 500)');

			// Mock handleApiCall to return error
			vi.mocked(handleApiCallModule.handleApiCall).mockResolvedValue({
				data: null,
				error: testError,
			});

			// This setting doesn't matter anymore since we're not checking error types
			vi.mocked(handleApiCallModule.is400Error).mockReturnValue(false);

			// Spy on console.error
			const consoleErrorSpy = vi
				.spyOn(console, 'error')
				.mockImplementation(() => {});

			// Act: Process the wxNode
			const result = await processWxNodeData([wxNode], 'en-US');

			// Assert: Verify the original wxNode is returned unchanged
			expect(result[0]).toEqual(wxNode);

			// Verify error was logged
			expect(consoleErrorSpy).toHaveBeenCalledWith(
				`Error fetching slideshow data ${wxNode.slideshow}:`,
				testError,
			);

			// Clean up
			consoleErrorSpy.mockRestore();
		});
	});

	describe('wxnode_video', () => {
		it('should handle 404 errors from getVideosByCollectionName', async () => {
			// Arrange: Create a wxNode with collection_name but no assetid
			const wxNode = {
				id: 'wxn03',
				type: 'wxnode_video',
				assetid: '', // No assetid
				collection_name: 'test-collection', // Has collection_name
				schema_version: '1.0',
				assetName: 'test-wxnode',
			} as DSXWxNode;

			vi.mocked(handleApiCallModule.handleApiCall).mockResolvedValueOnce({
				data: [] as DSXVideo[],
				error: null,
			});

			// Mock getVideosByCollectionName to throw a 404 error
			const notFoundError = new Error('Not found (status 404)');
			vi.mocked(collectionModule.getVideosByCollectionName).mockRejectedValue(
				notFoundError,
			);

			vi.mocked(handleApiCallModule.handleApiCall).mockImplementation(
				async (promise) => {
					try {
						const data = await promise;
						return { data, error: null };
					} catch (err) {
						const error = err as Error;
						if (handleApiCallModule.is400Error(error)) {
							return { data: null, error };
						}
						throw error;
					}
				},
			);

			// Act: Process the wxNode
			const result = await processWxNodeData([wxNode], 'en-US');

			// Assert: Verify the expected result
			// Note: Based on the implementation, even with a 404 error,
			// the wxNode gets an empty __wxnext.playlist property
			expect(result[0]).toEqual({
				...wxNode,
				__wxnext: {
					playlist: [],
				},
			});

			// Verify getVideosByCollectionName was called
			expect(collectionModule.getVideosByCollectionName).toHaveBeenCalledWith(
				'test-collection',
				'en-US',
			);
		});

		it('should transform video wxNode with valid collection data', async () => {
			// Arrange: Create a wxNode with collection_name but no assetid
			const wxNode = {
				id: 'wxn03',
				type: 'wxnode_video',
				assetid: '', // No assetid
				collection_name: 'test-collection', // Has collection_name
				schema_version: '1.0',
				assetName: 'test-wxnode',
			} as DSXWxNode;

			// Create mock video collection
			const mockVideoCollection = [
				createMockVideoAsset({
					id: 'video1',
					title: 'Video 1',
					teaserTitle: 'Video 1 Teaser',
				}),
				createMockVideoAsset({
					id: 'video2',
					title: 'Video 2',
					teaserTitle: 'Video 2 Teaser',
				}),
			];

			vi.mocked(handleApiCallModule.handleApiCall).mockResolvedValueOnce({
				data: mockVideoCollection as unknown as DSXVideo[],
				error: null,
			});

			// Act: Process the wxNode
			const result = await processWxNodeData([wxNode], 'en-US');

			// Assert: Verify the transformation
			expect(result[0]).toEqual({
				...wxNode,
				__wxnext: {
					file: 'https://example.com/video.m3u8',
					image: 'https://example.com/video-thumbnail.jpg',
					title: 'Video 1 Teaser',
					description: 'Test video description',
					tracks: [
						{
							file: 'https://example.com/captions.vtt',
							label: 'English',
							kind: 'captions',
							default: false,
						},
					],
					custom: {
						ctx: expect.any(Object),
						wxnode: wxNode,
						collection: undefined,
					},
					playlist: [
						expect.objectContaining({
							file: 'https://example.com/video.m3u8',
							image: 'https://example.com/video-thumbnail.jpg',
							title: 'Video 1 Teaser',
						}),
						expect.objectContaining({
							file: 'https://example.com/video.m3u8',
							image: 'https://example.com/video-thumbnail.jpg',
							title: 'Video 2 Teaser',
						}),
					],
				},
			});

			// Verify getVideosByCollectionName was called
			expect(collectionModule.getVideosByCollectionName).toHaveBeenCalledWith(
				'test-collection',
				'en-US',
			);
		});
	});
});

// Helper function to create a deep copy of an object
const deepCopy = <T>(obj: T): T => JSON.parse(JSON.stringify(obj));

describe('getCtxFromDsx', () => {
	// Create mock data for testing
	const createFullDsxVideo = (): DSXVideo => ({
		id: 'test-id-123',
		assetName: 'Test Video',
		schema_version: '1.0',
		jwpmediaid: 'jwp-123',
		type: 'video',
		locale: 'en-US',
		title: 'Test Video Title',
		teaserTitle: 'Test Teaser',
		pcollid: 'news/climate',
		partnercolls: [],
		url: 'https://example.com/video',
		publishdate: new Date('2025-01-01'),
		createddate: new Date('2025-01-01'),
		lastmodifieddate: new Date('2025-01-02'),
		expires_date: '2026-01-01',
		adsmetrics: {
			adzone: 'weather/news/climate',
			adconfigid: 'test-config',
			pagecode: 'test-page',
		},
		providerid: 'provider-123',
		providername: 'TWC - Digital',
		distro: true,
		premium: false,
		aspect_ratio: '16:9',
		seometa: {
			title: 'SEO Title',
			keywords: 'weather, climate',
			description: 'SEO Description',
			'og:image': 'https://example.com/og-image.jpg',
			'og:description': 'OG Description',
			canonical: '',
		},
		interests: {
			categoryName: 'Weather',
			categoryId: 'weather-123',
			backgroundColorName: 'Blue',
			backgroundColorCode: '#0000FF',
		},
		tags: {
			geo: [],
			locations: {
				regions: [],
				states: [],
			},
			keyword: ['weather', 'climate'],
			iab: {
				v1: ['IAB15-10_Weather', 'IAB15_Science'],
				v2: ['390_Weather', '464_Science'],
				v3: ['390_Weather', '467_Environment'],
			},
			ai: {
				v1: [],
				v2: [],
				v3: [],
			},
			storm: [],
			entitlements: [],
		},
		duration: '00:00:56',
		ingest_date: new Date('2025-01-01'),
		source_guid: 'source-123',
		source_name: 'Test Source',
		mezzanine_url: 'https://example.com/mezzanine',
		cc_url: 'https://example.com/cc',
		variants: {
			'0': 'https://example.com/variant-0',
			'1': 'https://example.com/variant-1',
			'2': 'https://example.com/variant-2',
			'3': 'https://example.com/variant-3',
			'4': 'https://example.com/variant-4',
			'5': 'https://example.com/variant-5',
			'6': 'https://example.com/variant-6',
			'7': 'https://example.com/variant-7',
			'8': 'https://example.com/variant-8',
			'9': 'https://example.com/variant-9',
			'10': 'https://example.com/variant-10',
			'11': 'https://example.com/variant-11',
			'12': 'https://example.com/variant-12',
			'13': 'https://example.com/variant-13',
			'14': 'https://example.com/variant-14',
			'15': 'https://example.com/variant-15',
			'16': 'https://example.com/variant-16',
			'17': 'https://example.com/variant-17',
			'18': 'https://example.com/variant-18',
			'19': 'https://example.com/variant-19',
			'20': 'https://example.com/variant-20',
			'21': 'https://example.com/variant-21',
			'200': 'https://example.com/variant-200',
			'400': 'https://example.com/variant-400',
			video: 'https://example.com/video',
			ios: 'https://example.com/ios',
			hds_new: 'https://example.com/hds_new',
			android: 'https://example.com/android',
			hls: 'https://example.com/hls',
			hls_new: 'https://example.com/hls_new',
			android_abr: 'https://example.com/android_abr',
			hds: 'https://example.com/hds',
			legacy: 'https://example.com/legacy',
			web: 'https://example.com/web',
			mweb: 'https://example.com/mweb',
			ios_app: 'https://example.com/ios_app',
			android_app: 'https://example.com/android_app',
			fallback_web: 'https://example.com/fallback_web',
			fallback_mweb: 'https://example.com/fallback_mweb',
			fallback_android_app: 'https://example.com/fallback_android_app',
			fallback_ios_app: 'https://example.com/fallback_ios_app',
		},
		format_urls: {
			m3u8: 'https://example.com/m3u8',
			mp4: 'https://example.com/mp4',
		},
		query_strings: {
			android_app: 'android_app=1',
			fallback_android_app: 'fallback_android_app=1',
			fallback_ios_app: 'fallback_ios_app=1',
			fallback_mweb: 'fallback_mweb=1',
			fallback_web: 'fallback_web=1',
			ios_app: 'ios_app=1',
			mweb: 'mweb=1',
			web: 'web=1',
		},
		animated_gif_variants: {},
		unicornID: 'unicorn-123',
		description: 'Test Description',
		transcript: 'Test Transcript',
		auth_required: false,
		flags: {
			Facebook: true,
			Intranet: false,
			'TV App': true,
			Twitter: true,
			YouTube: false,
			'Google+': false,
			'Big Web': true,
			'Little Web': true,
			'Mobile Apps': true,
			orig_source: 'test-source',
		},
		is_live_stream: false,
		samsung_insight_category: 'weather',
		playlists: [{ 'playlist-1': 'value-1' }],
	});

	// Create mock wxNode and collection for testing
	const createMockWxNode = (): DSXWxNode => ({
		id: 'test-wxnode-id',
		type: 'wxnode_video',
		schema_version: '1.0',
		assetid: 'test-video-asset-id',
		collection_name: 'test-collection',
		collection: 'test-collection-id',
	});

	const createMockCollection = () => ({
		id: 'test-collection-id',
		schema_version: '1.0',
		isPlaylist: true,
		title: 'Test Collection',
		description: 'Test Collection Description',
		url: 'https://example.com/collection',
		ad_metrics: {
			article: {
				metrics: 'test-metrics',
				pagecode: 'test-pagecode',
				zone: 'test-collection-zone',
			},
			video: {
				zone: 'test-video-zone',
			},
		},
	});

	test('should return empty object when input is null', () => {
		const result = getCtxFromDsx(
			null,
			createMockWxNode(),
			createMockCollection(),
		);
		expect(result).toEqual({});
	});

	test('should return empty object when input is undefined', () => {
		const result = getCtxFromDsx(
			undefined,
			createMockWxNode(),
			createMockCollection(),
		);
		expect(result).toEqual({});
	});

	test('should extract all properties from a complete DSXVideo object', () => {
		const fullDsxVideo = createFullDsxVideo();
		const mockWxNode = createMockWxNode();
		const mockCollection = createMockCollection();
		const result = getCtxFromDsx(fullDsxVideo, mockWxNode, mockCollection);

		expect(result).toEqual({
			jwplayer: '',
			pcollid: 'news/climate',
			adzone: 'weather/news/climate',
			assetName: 'Test Video',
			description: 'Test Description',
			iab: {
				v1: ['IAB15-10_Weather', 'IAB15_Science'],
				v2: ['390_Weather', '464_Science'],
				v3: ['390_Weather', '467_Environment'],
			},
			id: 'test-id-123',
			duration: '00:00:56',
			entitlements: [],
			lastmodifieddate: new Date('2025-01-02'),
			playlists: [
				{
					'playlist-1': 'value-1',
				},
			],
			premium: false,
			providername: 'TWC - Digital',
			publishdate: new Date('2025-01-01'),
			tagsGeo: [],
			tagsKeyword: ['weather', 'climate'],
			tagsStorm: [],
			teaserTitle: 'Test Teaser',
			title: 'Test Video Title',
			videoIndex: -1,
			collectionId: 'test-collection-id',
			videoId: 'test-id-123',
			collectionAdZone: 'test-collection-zone',
		});
	});

	test('should handle missing pcollid', () => {
		const fullDsxVideo = createFullDsxVideo();
		const videoWithoutPcollid = {
			...deepCopy(fullDsxVideo),
			pcollid: undefined as unknown as string,
		};
		const mockWxNode = createMockWxNode();
		const mockCollection = createMockCollection();

		const result = getCtxFromDsx(
			videoWithoutPcollid,
			mockWxNode,
			mockCollection,
		);

		expect(result.pcollid).toBe('');
	});

	test('should handle missing adsmetrics', () => {
		const fullDsxVideo = createFullDsxVideo();
		const videoWithoutAdsmetrics = {
			...deepCopy(fullDsxVideo),
			adsmetrics: undefined as unknown as typeof fullDsxVideo.adsmetrics,
		};
		const mockWxNode = createMockWxNode();
		const mockCollection = createMockCollection();

		const result = getCtxFromDsx(
			videoWithoutAdsmetrics,
			mockWxNode,
			mockCollection,
		);

		expect(result.adzone).toBe('');
	});

	test('should handle missing adsmetrics.adzone', () => {
		const fullDsxVideo = createFullDsxVideo();
		const videoWithoutAdzone = deepCopy(fullDsxVideo);
		videoWithoutAdzone.adsmetrics = {
			...videoWithoutAdzone.adsmetrics,
			adzone: undefined as unknown as string,
		};
		const mockWxNode = createMockWxNode();
		const mockCollection = createMockCollection();

		const result = getCtxFromDsx(
			videoWithoutAdzone,
			mockWxNode,
			mockCollection,
		);

		expect(result.adzone).toBe('');
	});

	test('should handle missing tags', () => {
		const fullDsxVideo = createFullDsxVideo();
		const videoWithoutTags = {
			...deepCopy(fullDsxVideo),
			tags: undefined as unknown as typeof fullDsxVideo.tags,
		};
		const mockWxNode = createMockWxNode();
		const mockCollection = createMockCollection();

		const result = getCtxFromDsx(videoWithoutTags, mockWxNode, mockCollection);

		expect(result.iab).toEqual({});
	});

	test('should handle missing tags.iab', () => {
		const fullDsxVideo = createFullDsxVideo();
		const videoWithoutIab = deepCopy(fullDsxVideo);
		videoWithoutIab.tags = {
			...videoWithoutIab.tags,
			iab: undefined as unknown as typeof videoWithoutIab.tags.iab,
		};
		const mockWxNode = createMockWxNode();
		const mockCollection = createMockCollection();

		const result = getCtxFromDsx(videoWithoutIab, mockWxNode, mockCollection);

		expect(result.iab).toEqual({});
	});

	test('should handle missing providername', () => {
		const fullDsxVideo = createFullDsxVideo();
		const videoWithoutProvidername = {
			...deepCopy(fullDsxVideo),
			providername: undefined as unknown as string,
		};
		const mockWxNode = createMockWxNode();
		const mockCollection = createMockCollection();

		const result = getCtxFromDsx(
			videoWithoutProvidername,
			mockWxNode,
			mockCollection,
		);

		expect(result.providername).toBe('no-provider-data');
	});

	test('should handle missing duration', () => {
		const fullDsxVideo = createFullDsxVideo();
		const videoWithoutDuration = {
			...deepCopy(fullDsxVideo),
			duration: undefined as unknown as string,
		};
		const mockWxNode = createMockWxNode();
		const mockCollection = createMockCollection();

		const result = getCtxFromDsx(
			videoWithoutDuration,
			mockWxNode,
			mockCollection,
		);

		expect(result.duration).toBe('');
	});

	test('should handle missing id', () => {
		const fullDsxVideo = createFullDsxVideo();
		const videoWithoutId = {
			...deepCopy(fullDsxVideo),
			id: undefined as unknown as string,
		};
		const mockWxNode = createMockWxNode();
		const mockCollection = createMockCollection();

		const result = getCtxFromDsx(videoWithoutId, mockWxNode, mockCollection);

		expect(result.id).toBe('');
	});

	test('should handle empty string values', () => {
		const partialDsxVideo = createFullDsxVideo();
		partialDsxVideo.pcollid = '';
		partialDsxVideo.adsmetrics.adzone = '';
		partialDsxVideo.duration = '';
		partialDsxVideo.id = '';
		const mockWxNode = createMockWxNode();
		const mockCollection = createMockCollection();

		const result = getCtxFromDsx(partialDsxVideo, mockWxNode, mockCollection);

		expect(result).toEqual({
			jwplayer: '',
			pcollid: '',
			adzone: '',
			assetName: 'Test Video',
			description: 'Test Description',
			entitlements: [],
			iab: {
				v1: ['IAB15-10_Weather', 'IAB15_Science'],
				v2: ['390_Weather', '464_Science'],
				v3: ['390_Weather', '467_Environment'],
			},
			duration: '',
			id: '',
			videoIndex: -1,
			collectionId: 'test-collection-id',
			videoId: '',
			collectionAdZone: 'test-collection-zone',
			lastmodifieddate: new Date('2025-01-02'),
			playlists: [
				{
					'playlist-1': 'value-1',
				},
			],
			premium: false,
			providername: 'TWC - Digital',
			publishdate: new Date('2025-01-01'),
			tagsGeo: [],
			tagsKeyword: ['weather', 'climate'],
			tagsStorm: [],
			teaserTitle: 'Test Teaser',
			title: 'Test Video Title',
		});
	});

	test('should handle missing collection ad zone', () => {
		const fullDsxVideo = createFullDsxVideo();
		const mockWxNode = createMockWxNode();
		const collectionWithoutAdZone = {
			...createMockCollection(),
			ad_metrics: {
				article: {
					metrics: 'test-metrics',
					pagecode: 'test-pagecode',
					// zone is missing
				},
			},
		};

		const result = getCtxFromDsx(
			fullDsxVideo,
			mockWxNode,
			collectionWithoutAdZone as DSXCollection,
		);

		expect(result.collectionAdZone).toBeUndefined();
	});

	test('should handle missing collection', () => {
		const fullDsxVideo = createFullDsxVideo();
		const mockWxNode = createMockWxNode();
		// Pass undefined as collection
		const result = getCtxFromDsx(fullDsxVideo, mockWxNode, undefined);

		expect(result.collectionAdZone).toBeUndefined();
		// Other properties should still be present
		expect(result.pcollid).toBe('news/climate');
		expect(result.adzone).toBe('weather/news/climate');
	});
});

describe('convertFiguresToSyntheticWxNodes', () => {
	const testCases = [
		{
			name: 'standard figure with img and figcaption',
			input:
				'<figure><img src="https://example.com/image.jpg" alt="Test alt" /><figcaption>Test caption</figcaption></figure>',
			expectedCaption: 'Test caption',
			expectedHtml: '<div id="wxn01" />',
		},
		{
			name: 'figure using p tag for caption',
			input:
				'<figure><img src="https://example.com/image.jpg" alt="Test alt" /><p>P tag caption</p></figure>',
			expectedCaption: 'P tag caption',
			expectedHtml: '<div id="wxn01" />',
		},
		{
			name: 'figure with no caption',
			input:
				'<figure><img src="https://example.com/image.jpg" alt="Test alt" /></figure>',
			expectedCaption: '',
			expectedHtml: '<div id="wxn01" />',
		},
	];

	it.each(testCases)(
		'should handle $name',
		({ input, expectedCaption, expectedHtml }) => {
			const result = convertFiguresToSyntheticWxNodes(input);

			expect(result.modifiedHtml).toBe(expectedHtml);
			expect(result.syntheticWxNodes).toHaveLength(1);
			expect(result.syntheticWxNodes[0]).toEqual({
				id: 'wxn01',
				type: 'wxnode_internal_image',
				assetid: '',
				synopsis: expectedCaption,
				credit: '',
				linkurl: '',
				schema_version: '1.0',
				__wxnext: {
					priority: false,
					imageUrl: 'https://example.com/image.jpg',
					caption: expectedCaption,
					credit: null,
					altText: 'Test alt',
					linkUrl: null,
				},
			});
		},
	);

	it('should handle multiple figure tags', () => {
		const input =
			'<figure><img src="https://example.com/image1.jpg" alt="Alt 1" /><figcaption>Caption 1</figcaption></figure><p>Text between</p><figure><img src="https://example.com/image2.jpg" alt="Alt 2" /><p>Caption 2</p></figure>';

		const result = convertFiguresToSyntheticWxNodes(input);

		expect(result.modifiedHtml).toBe(
			'<div id="wxn01" /><p>Text between</p><div id="wxn02" />',
		);
		expect(result.syntheticWxNodes).toHaveLength(2);
		expect(result.syntheticWxNodes[0]?.id).toBe('wxn01');
		expect((result.syntheticWxNodes[0] as any)?.__wxnext?.caption).toBe(
			'Caption 1',
		);
		expect(result.syntheticWxNodes[1]?.id).toBe('wxn02');
		expect((result.syntheticWxNodes[1] as any)?.__wxnext?.caption).toBe(
			'Caption 2',
		);
	});

	it('should handle captions with HTML entities', () => {
		const input =
			'<figure><img src="https://example.com/image.jpg" alt="Test alt" /><figcaption>Caption with &amp; entities &lt;test&gt; &quot;quotes&quot;</figcaption></figure>';

		const result = convertFiguresToSyntheticWxNodes(input);

		expect((result.syntheticWxNodes[0] as any)?.__wxnext?.caption).toBe(
			'Caption with & entities <test> "quotes"',
		);
		expect(result.syntheticWxNodes[0]?.synopsis).toBe(
			'Caption with & entities <test> "quotes"',
		);
	});

	it('should avoid ID collisions with existing wxnodes', () => {
		const input =
			'<figure><img src="https://example.com/image1.jpg" alt="Alt 1" /><figcaption>Caption 1</figcaption></figure><figure><img src="https://example.com/image2.jpg" alt="Alt 2" /><figcaption>Caption 2</figcaption></figure>';

		// Simulate existing wxnodes with IDs wxn01 and wxn03
		const existingWxNodeIds = ['wxn01', 'wxn03'];

		const result = convertFiguresToSyntheticWxNodes(input, existingWxNodeIds);

		expect(result.modifiedHtml).toBe('<div id="wxn02" /><div id="wxn04" />');
		expect(result.syntheticWxNodes).toHaveLength(2);
		expect(result.syntheticWxNodes[0]?.id).toBe('wxn02'); // Should skip wxn01
		expect(result.syntheticWxNodes[1]?.id).toBe('wxn04'); // Should skip wxn03
	});

	it('should handle empty existing wxnode IDs array', () => {
		const input =
			'<figure><img src="https://example.com/image.jpg" alt="Test alt" /><figcaption>Test caption</figcaption></figure>';

		const result = convertFiguresToSyntheticWxNodes(input, []);

		expect(result.modifiedHtml).toBe('<div id="wxn01" />');
		expect(result.syntheticWxNodes[0]?.id).toBe('wxn01');
	});

	it('should handle consecutive existing wxnode IDs', () => {
		const input =
			'<figure><img src="https://example.com/image.jpg" alt="Test alt" /><figcaption>Test caption</figcaption></figure>';

		// All IDs from wxn01 to wxn05 are taken
		const existingWxNodeIds = ['wxn01', 'wxn02', 'wxn03', 'wxn04', 'wxn05'];

		const result = convertFiguresToSyntheticWxNodes(input, existingWxNodeIds);

		expect(result.modifiedHtml).toBe('<div id="wxn06" />');
		expect(result.syntheticWxNodes[0]?.id).toBe('wxn06');
	});

	it('should handle gaps in existing wxnode IDs', () => {
		const input =
			'<figure><img src="https://example.com/image1.jpg" alt="Alt 1" /><figcaption>Caption 1</figcaption></figure><figure><img src="https://example.com/image2.jpg" alt="Alt 2" /><figcaption>Caption 2</figcaption></figure>';

		// Existing IDs with gaps: wxn01, wxn04, wxn06
		const existingWxNodeIds = ['wxn01', 'wxn04', 'wxn06'];

		const result = convertFiguresToSyntheticWxNodes(input, existingWxNodeIds);

		expect(result.modifiedHtml).toBe('<div id="wxn02" /><div id="wxn03" />');
		expect(result.syntheticWxNodes).toHaveLength(2);
		expect(result.syntheticWxNodes[0]?.id).toBe('wxn02'); // Fill first gap
		expect(result.syntheticWxNodes[1]?.id).toBe('wxn03'); // Fill second gap
	});

	it('should work with default parameter when no existing IDs provided', () => {
		const input =
			'<figure><img src="https://example.com/image.jpg" alt="Test alt" /><figcaption>Test caption</figcaption></figure>';

		// Call without second parameter (should use default empty array)
		const result = convertFiguresToSyntheticWxNodes(input);

		expect(result.modifiedHtml).toBe('<div id="wxn01" />');
		expect(result.syntheticWxNodes[0]?.id).toBe('wxn01');
	});

	describe('collision avoidance with HTML div IDs', () => {
		it('should avoid collisions with existing div IDs in HTML content', () => {
			const htmlWithDivsAndFigures = `
				<div id="wxn01">Existing content</div>
				<p>Some content</p>
				<figure>
					<img src="https://example.com/image1.jpg" alt="Test image 1" />
					<figcaption>Caption 1</figcaption>
				</figure>
				<div id="wxn03">More existing content</div>
				<figure>
					<img src="https://example.com/image2.jpg" alt="Test image 2" />
					<figcaption>Caption 2</figcaption>
				</figure>
			`;

			const result = convertFiguresToSyntheticWxNodes(htmlWithDivsAndFigures);

			expect(result.syntheticWxNodes).toHaveLength(2);
			expect(result.syntheticWxNodes[0]?.id).toBe('wxn02'); // Avoids wxn01 from HTML
			expect(result.syntheticWxNodes[1]?.id).toBe('wxn04'); // Avoids wxn03 from HTML
		});

		it('should combine HTML div IDs with existing wxnode IDs', () => {
			const htmlWithDivsAndFigures = `
				<div id="wxn02">Existing HTML content</div>
				<figure>
					<img src="https://example.com/image1.jpg" alt="Test image 1" />
					<figcaption>Caption 1</figcaption>
				</figure>
				<figure>
					<img src="https://example.com/image2.jpg" alt="Test image 2" />
					<figcaption>Caption 2</figcaption>
				</figure>
			`;

			// Also have existing wxnode IDs
			const existingWxNodeIds = ['wxn01', 'wxn04'];
			const result = convertFiguresToSyntheticWxNodes(
				htmlWithDivsAndFigures,
				existingWxNodeIds,
			);

			expect(result.syntheticWxNodes).toHaveLength(2);
			expect(result.syntheticWxNodes[0]?.id).toBe('wxn03'); // Avoids wxn01 (wxnode), wxn02 (HTML)
			expect(result.syntheticWxNodes[1]?.id).toBe('wxn05'); // Avoids wxn04 (wxnode)
		});

		it('should handle multiple div IDs in HTML content', () => {
			const htmlWithMultipleDivs = `
				<div id="wxn01">Content 1</div>
				<div id="wxn02">Content 2</div>
				<div id="wxn05">Content 5</div>
				<figure>
					<img src="https://example.com/image1.jpg" alt="Test image 1" />
					<figcaption>Caption 1</figcaption>
				</figure>
				<figure>
					<img src="https://example.com/image2.jpg" alt="Test image 2" />
					<figcaption>Caption 2</figcaption>
				</figure>
			`;

			const result = convertFiguresToSyntheticWxNodes(htmlWithMultipleDivs);

			expect(result.syntheticWxNodes).toHaveLength(2);
			expect(result.syntheticWxNodes[0]?.id).toBe('wxn03'); // Fills gap between wxn02 and wxn05
			expect(result.syntheticWxNodes[1]?.id).toBe('wxn04'); // Fills next gap
		});

		it('should handle div IDs with different formats (ignore non-wxnode IDs)', () => {
			const htmlWithMixedDivs = `
				<div id="wxn01">Wxnode content</div>
				<div id="regular-div">Regular content</div>
				<div id="another-id">Another content</div>
				<figure>
					<img src="https://example.com/image1.jpg" alt="Test image 1" />
					<figcaption>Caption 1</figcaption>
				</figure>
			`;

			const result = convertFiguresToSyntheticWxNodes(htmlWithMixedDivs);

			expect(result.syntheticWxNodes).toHaveLength(1);
			expect(result.syntheticWxNodes[0]?.id).toBe('wxn02'); // Only avoids wxn01, ignores other IDs
		});

		it('should handle HTML with no existing wxnode div IDs', () => {
			const htmlWithoutWxnodeDivs = `
				<div id="regular-div">Regular content</div>
				<p>Some paragraph</p>
				<figure>
					<img src="https://example.com/image1.jpg" alt="Test image 1" />
					<figcaption>Caption 1</figcaption>
				</figure>
			`;

			const result = convertFiguresToSyntheticWxNodes(htmlWithoutWxnodeDivs);

			expect(result.syntheticWxNodes).toHaveLength(1);
			expect(result.syntheticWxNodes[0]?.id).toBe('wxn01'); // Starts with wxn01 since no conflicts
		});
	});
});
