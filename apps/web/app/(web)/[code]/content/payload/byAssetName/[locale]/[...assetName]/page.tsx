import React from 'react';
import { unstable_cacheTag as cacheTag } from 'next/cache';
import { notFound } from 'next/navigation';
import { queryArticlesByAssetName } from '../../../utils/getPayloadArticles';
import { AtomHydrationBoundaries } from '@/components/AtomHydrationBoundaries';
import { getLocaleFromPathParams } from '@/utils/getLocaleFromPathParams';
import { DebugCollector } from '@/components/FrontendAdminHeader/collectors/DebugCollector';
import { ArticleHeader } from '@/components/ArticleHeader/ArticleHeader';
import { getContextualizedPage } from '@/utils/contextParameters/getContextualizedPage';
import {
	buildArticlePageMetadata,
	getArticlePageDebugData,
} from '@/collections/Articles/utils/articleData';
import { ArticleJsonLd } from '@/components/JsonLd/JsonLd';
import ArticleBody from '@/components/ArticleBody/ArticleBody';
import Helios from '@/components/Helios';
import { getContextParams } from '@/utils/contextParameters/extractParameters';

const pageKey = '/content';

export const dynamic = 'force-static';

interface ArticleByAssetNameParams {
	assetName: string[];
	locale?: string;
	code: string;
}

interface ArticleByAssetNameProps {
	params: Promise<ArticleByAssetNameParams>;
}

export async function generateMetadata({ params }: ArticleByAssetNameProps) {
	const segments = await params;
	const assetName = `/${segments.assetName.join('/')}`;
	const locale = getLocaleFromPathParams(segments.locale);
	const article = await queryArticlesByAssetName({
		assetName,
		locale,
	});

	return buildArticlePageMetadata(article || null);
}

export default async function ArticlePage({ params }: ArticleByAssetNameProps) {
	'use cache';

	const { code, assetName: assetNameParam, locale: localeParam } = await params;

	cacheTag(code);

	const assetName = `/${assetNameParam.join('/')}`;
	const locale = getLocaleFromPathParams(localeParam);
	const contextParams = await getContextParams(code);
	const contextParamsResults = await getContextualizedPage({
		...contextParams,
		locale,
		pageKey,
	});

	const article = await queryArticlesByAssetName({
		assetName,
		locale,
	});

	if (!article) {
		return notFound();
	}

	const pageMetadata = getArticlePageDebugData(article);

	return (
		<article className="not-prose w-full max-w-4xl rounded-md bg-white p-5">
			<DebugCollector
				componentName="ContentPage"
				data={{
					pageType: 'content',
					locale,
					context: {
						match: contextParamsResults?.match,
					},
				}}
				page={pageMetadata}
			/>
			<AtomHydrationBoundaries
				pageId={'article'}
				pageLocale={locale}
				metricsArticleData={{
					id: article?.id,
					authors: article?.authors,
					category: article?.category,
					createdAt: article?.createdAt,
					publishDate: article?.publishDate,
					coreMetadata: article?.coreMetadata,
				}}
			/>
			<Helios code={code} article={article} />
			<ArticleJsonLd article={article} />
			<ArticleHeader article={article} />
			<ArticleBody article={article} />
		</article>
	);
}
