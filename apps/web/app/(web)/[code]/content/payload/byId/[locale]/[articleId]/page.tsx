import React from 'react';
import { unstable_cacheTag as cacheTag } from 'next/cache';
import { notFound } from 'next/navigation';
import { queryArticlesByArticleId } from '../../../utils/getPayloadArticles';
import { AtomHydrationBoundaries } from '@/components/AtomHydrationBoundaries';
import { getLocaleFromPathParams } from '@/utils/getLocaleFromPathParams';
import {
	buildArticlePageMetadata,
	getArticlePageDebugData,
} from '@/collections/Articles/utils/articleData';
import { ArticleJsonLd } from '@/components/JsonLd/JsonLd';
import { DebugCollector } from '@/components/FrontendAdminHeader/collectors/DebugCollector';
import { getContextualizedPage } from '@/utils/contextParameters/getContextualizedPage';
import Helios from '@/components/Helios';
import { ArticleHeader } from '@/components/ArticleHeader/ArticleHeader';
import ArticleBody from '@/components/ArticleBody/ArticleBody';
import { getContextParams } from '@/utils/contextParameters/extractParameters';

const pageKey = '/content';

export const dynamic = 'force-static';

interface ArticleByIDParams {
	articleId: string;
	locale?: string;
	code: string;
}

interface ArticleByIDProps {
	params: Promise<ArticleByIDParams>;
}

export async function generateMetadata({ params }: ArticleByIDProps) {
	const segments = await params;
	const locale = getLocaleFromPathParams(segments.locale);
	const article = await queryArticlesByArticleId({
		articleId: segments.articleId,
		locale,
	});

	return buildArticlePageMetadata(article);
}

export default async function ArticlePage({ params }: ArticleByIDProps) {
	'use cache';

	const { code, articleId, locale: localeParam } = await params;

	cacheTag(code);

	const locale = getLocaleFromPathParams(localeParam);
	const article = await queryArticlesByArticleId({
		articleId: articleId,
		locale,
	});

	if (!article) {
		return notFound();
	}

	const contextParams = await getContextParams(code);
	const contextParamsResults = await getContextualizedPage({
		...contextParams,
		locale,
		pageKey,
	});
	const pageMetadata = getArticlePageDebugData(article);

	return (
		<article className="not-prose w-full max-w-4xl rounded-md bg-white p-5">
			<DebugCollector
				componentName="ContentPage"
				data={{
					pageType: 'content',
					locale,
					context: {
						match: contextParamsResults?.match,
					},
				}}
				page={pageMetadata}
			/>
			<AtomHydrationBoundaries
				pageId={'article'}
				pageLocale={locale}
				metricsArticleData={{
					id: article?.id,
					authors: article?.authors,
					category: article?.category,
					createdAt: article?.createdAt,
					publishDate: article?.publishDate,
					coreMetadata: article?.coreMetadata,
				}}
			/>
			<Helios code={code} article={article} />
			<ArticleJsonLd article={article} />
			<ArticleHeader article={article} />
			<ArticleBody article={article} />
		</article>
	);
}
