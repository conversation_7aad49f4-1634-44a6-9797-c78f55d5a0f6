import 'server-only';

import { draftMode } from 'next/headers';
import configPromise from '@payload-config';
import { getPayload } from 'payload';
import { getLocale } from '@/utils/getSupportedLocale';

export const queryArticlesByArticleId = async ({
	articleId,
	locale,
}: {
	articleId: string;
	locale?: string;
}) => {
	const { isEnabled: draft } = await draftMode();

	const payload = await getPayload({ config: configPromise });

	console.log(
		`Querying article by ID: ${articleId}, locale: ${locale || 'default'}`,
	);

	const response = await payload.find({
		collection: 'articles',
		depth: 2,
		limit: 1,
		overrideAccess: true,
		pagination: false,
		draft,
		where: {
			id: {
				equals: articleId,
			},
		},
		locale: getLocale(locale),
	});

	console.log(`Found ${response.docs.length} articles by ID: ${articleId}`);
	return response.docs?.[0] || null;
};

export const queryArticlesByAssetName = async ({
	assetName,
	locale,
}: {
	assetName: string;
	locale?: string;
}) => {
	const { isEnabled: draft } = await draftMode();

	const payload = await getPayload({ config: configPromise });
	const validPayloadLocale = getLocale(locale);

	console.log(
		`Querying article by assetName: ${assetName}, locale: ${validPayloadLocale}`,
	);

	// assetName has the locale in it for non en-US
	const payloadAssetName =
		validPayloadLocale !== 'en-US'
			? `/${validPayloadLocale}${assetName}`
			: assetName;

	// First try with the exact assetName
	const response = await payload.find({
		collection: 'articles',
		depth: 2,
		limit: 1,
		overrideAccess: true,
		pagination: false,
		draft,
		where: {
			// eg. assetName.en-US
			[`assetName.${validPayloadLocale}`]: {
				equals: payloadAssetName,
			},
		},
		locale: validPayloadLocale,
	});

	if (response.docs.length > 0) {
		return response.docs[0];
	}

	return null;
};
