import { NextRequest, NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@payload-config';
import { ContentQuery } from '@/payload-types';
import { getMediaAssetsById } from '@repo/dal/content/media/getMediaAssetsById';

export async function GET(request: NextRequest) {
	let logger;
	try {
		// Get query parameters
		const searchParams = request.nextUrl.searchParams;
		const assetId = searchParams.get('assetId');
		// TODO: come back to this as we will probably want to get content queries by language
		//const language = searchParams.get('language') || 'en-US';
		const apiKey = searchParams.get('apiKey');

		// Validate required parameters
		if (!assetId) {
			return NextResponse.json(
				{ error: 'Missing required parameter: assetId' },
				{ status: 400 },
			);
		}

		// Optional: Validate API key if needed
		if (!apiKey) {
			return NextResponse.json(
				{ error: 'Missing required parameter: apiKey' },
				{ status: 401 },
			);
		}

		try {
			const payload = await getPayload({ config });
			logger = payload.logger;
			// First, try to find a content query with this ID in Payload
			const payloadQuery = (await payload.findByID({
				collection: 'content-queries',
				id: assetId,
				depth: 2, // We need to fetch related data
			})) as ContentQuery;

			if (
				payloadQuery &&
				payloadQuery.mergedContent &&
				payloadQuery.mergedContent.length > 0
			) {
				// Return the merged content directly without transformation
				return NextResponse.json(payloadQuery.mergedContent);
			}
		} catch (_error) {
			// If the ID is not a valid Payload ID, this will throw an error
			// We'll just continue to the next step
			logger?.debug(`No Payload content query found with ID: ${assetId}`);
		}

		// If not found in Payload, try to fetch from Kalliope using getQuerySetIds
		try {
			const contentMedia = await getMediaAssetsById(assetId);

			return NextResponse.json(contentMedia);
		} catch (error) {
			logger?.error('Error fetching from Kalliope:', error);
			return NextResponse.json(
				{ error: 'Content not found or error fetching content' },
				{ status: 404 },
			);
		}
	} catch (error) {
		return NextResponse.json(
			{ error: `Internal server error: ${error}` },
			{ status: 500 },
		);
	}
}
