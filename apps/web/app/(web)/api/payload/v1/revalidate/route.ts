import { NextRequest, NextResponse } from 'next/server';
import { revalidatePath, revalidateTag } from 'next/cache';
import { createLogger } from '@repo/logger';

const logger = createLogger('RevalidateAPI');

/**
 * Validate the secret parameter against the environment variable
 */
function validateSecret(secret: string | null): boolean {
	const expectedSecret = process.env.PAYLOAD_SECRET;

	if (!expectedSecret) {
		logger.error('PAYLOAD_SECRET environment variable is not set');
		return false;
	}

	if (!secret) {
		logger.warn('No secret provided in request');
		return false;
	}

	// Use constant-time comparison to prevent timing attacks
	if (secret.length !== expectedSecret.length) {
		return false;
	}

	let result = 0;
	for (let i = 0; i < secret.length; i++) {
		result |= secret.charCodeAt(i) ^ expectedSecret.charCodeAt(i);
	}

	return result === 0;
}

/**
 * Validate and sanitize the path parameter
 */
function validatePath(path: string): {
	isValid: boolean;
	sanitizedPath?: string;
	error?: string;
} {
	if (!path) {
		return { isValid: false, error: 'Path parameter is required' };
	}

	// Basic path validation - ensure it starts with /
	const sanitizedPath = path.startsWith('/') ? path : `/${path}`;

	// Additional validation - prevent path traversal attacks
	if (sanitizedPath.includes('..') || sanitizedPath.includes('\\')) {
		return { isValid: false, error: 'Invalid path format' };
	}

	// Ensure path is reasonable length
	if (sanitizedPath.length > 1000) {
		return { isValid: false, error: 'Path too long' };
	}

	return { isValid: true, sanitizedPath };
}

/**
 * Validate and sanitize the tag parameter
 */
function validateTag(tag: string): {
	isValid: boolean;
	sanitizedTag?: string;
	error?: string;
} {
	if (!tag) {
		return { isValid: false, error: 'Tag parameter is required' };
	}

	// Basic tag validation - alphanumeric, hyphens, underscores only
	const tagRegex = /^[a-zA-Z0-9_-]+$/;
	if (!tagRegex.test(tag)) {
		return { isValid: false, error: 'Tag contains invalid characters' };
	}

	// Ensure tag is reasonable length
	if (tag.length > 100) {
		return { isValid: false, error: 'Tag too long' };
	}

	return { isValid: true, sanitizedTag: tag };
}

/**
 * POST - Revalidate path or tag
 * Supports both path and tag revalidation based on search parameters
 *
 * Query Parameters:
 * - secret: Required authentication secret (must match PAYLOAD_SECRET env var)
 * - path: Optional path to revalidate (e.g., "/weather/today")
 * - tag: Optional tag to revalidate (e.g., "articles-sitemap")
 *
 * Note: Either path or tag must be provided, but not both
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
	try {
		const { searchParams } = new URL(request.url);
		const secret = searchParams.get('secret');
		const path = searchParams.get('path');
		const tag = searchParams.get('tag');

		logger.info('Revalidate request received', {
			hasSecret: !!secret,
			hasPath: !!path,
			hasTag: !!tag,
			userAgent: request.headers.get('user-agent'),
		});

		// Validate secret first
		if (!validateSecret(secret)) {
			logger.warn('Invalid or missing secret in revalidate request');
			return NextResponse.json(
				{ error: 'Unauthorized: Invalid or missing secret' },
				{ status: 401 },
			);
		}

		// Ensure exactly one of path or tag is provided
		if (!path && !tag) {
			logger.warn('Neither path nor tag provided in revalidate request');
			return NextResponse.json(
				{ error: 'Bad Request: Either path or tag parameter is required' },
				{ status: 400 },
			);
		}

		if (path && tag) {
			logger.warn('Both path and tag provided in revalidate request');
			return NextResponse.json(
				{ error: 'Bad Request: Provide either path or tag, not both' },
				{ status: 400 },
			);
		}

		// Handle path revalidation
		if (path) {
			const pathValidation = validatePath(path);
			if (!pathValidation.isValid) {
				logger.warn('Invalid path in revalidate request', {
					path,
					error: pathValidation.error,
				});
				return NextResponse.json(
					{ error: `Bad Request: ${pathValidation.error}` },
					{ status: 400 },
				);
			}

			logger.info('Revalidating path', { path: pathValidation.sanitizedPath });

			try {
				revalidatePath(pathValidation.sanitizedPath!);
				logger.info('Path revalidation successful', {
					path: pathValidation.sanitizedPath,
				});

				return NextResponse.json(
					{
						success: true,
						message: 'Path revalidated successfully',
						path: pathValidation.sanitizedPath,
					},
					{ status: 200 },
				);
			} catch (revalidateError) {
				logger.error('Path revalidation failed', {
					path: pathValidation.sanitizedPath,
					error:
						revalidateError instanceof Error
							? revalidateError.message
							: 'Unknown error',
				});

				return NextResponse.json(
					{ error: 'Internal Server Error: Path revalidation failed' },
					{ status: 500 },
				);
			}
		}

		// Handle tag revalidation
		if (tag) {
			const tagValidation = validateTag(tag);
			if (!tagValidation.isValid) {
				logger.warn('Invalid tag in revalidate request', {
					tag,
					error: tagValidation.error,
				});
				return NextResponse.json(
					{ error: `Bad Request: ${tagValidation.error}` },
					{ status: 400 },
				);
			}

			logger.info('Revalidating tag', { tag: tagValidation.sanitizedTag });

			try {
				revalidateTag(tagValidation.sanitizedTag!);
				logger.info('Tag revalidation successful', {
					tag: tagValidation.sanitizedTag,
				});

				return NextResponse.json(
					{
						success: true,
						message: 'Tag revalidated successfully',
						tag: tagValidation.sanitizedTag,
					},
					{ status: 200 },
				);
			} catch (revalidateError) {
				logger.error('Tag revalidation failed', {
					tag: tagValidation.sanitizedTag,
					error:
						revalidateError instanceof Error
							? revalidateError.message
							: 'Unknown error',
				});

				return NextResponse.json(
					{ error: 'Internal Server Error: Tag revalidation failed' },
					{ status: 500 },
				);
			}
		}

		// This should never be reached due to earlier validation
		return NextResponse.json(
			{ error: 'Internal Server Error: Unexpected state' },
			{ status: 500 },
		);
	} catch (error) {
		logger.error('Unexpected error in revalidate API', {
			error: error instanceof Error ? error.message : 'Unknown error',
			stack: error instanceof Error ? error.stack : undefined,
		});

		return NextResponse.json(
			{ error: 'Internal Server Error: Unexpected error occurred' },
			{ status: 500 },
		);
	}
}

/**
 * GET - Health check and usage information
 * Returns information about how to use the revalidate endpoint
 */
export async function GET(): Promise<NextResponse> {
	return NextResponse.json(
		{
			message: 'Revalidate API endpoint',
			usage: {
				method: 'POST',
				parameters: {
					secret: 'Required - Authentication secret (PAYLOAD_SECRET)',
					path: 'Optional - Path to revalidate (e.g., "/weather/today")',
					tag: 'Optional - Tag to revalidate (e.g., "articles-sitemap")',
				},
				notes: [
					'Either path or tag must be provided, but not both',
					'Secret must match the PAYLOAD_SECRET environment variable',
					'Paths are automatically prefixed with "/" if not provided',
					'Tags must contain only alphanumeric characters, hyphens, and underscores',
				],
			},
			examples: {
				pathRevalidation:
					'/api/payload/v1/revalidate?secret=your-secret&path=/weather/today',
				tagRevalidation:
					'/api/payload/v1/revalidate?secret=your-secret&tag=articles-sitemap',
			},
		},
		{ status: 200 },
	);
}
