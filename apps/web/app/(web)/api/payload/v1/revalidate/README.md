# Revalidate API Endpoint

This API endpoint provides a secure way to revalidate Next.js cache entries using either path-based or tag-based revalidation.

## Endpoint

```text
POST /api/payload/v1/revalidate
GET /api/payload/v1/revalidate (for documentation)
```

## Authentication

The endpoint requires a `secret` query parameter that must match the `PAYLOAD_SECRET` environment variable.

## Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `secret` | string | Yes | Authentication secret (must match `PAYLOAD_SECRET` env var) |
| `path` | string | No* | Path to revalidate (e.g., `/weather/today`) |
| `tag` | string | No* | Tag to revalidate (e.g., `articles-sitemap`) |

*Either `path` or `tag` must be provided, but not both.

## Usage Examples

### Path Revalidation

```bash
curl -X POST "https://your-domain.com/api/payload/v1/revalidate?secret=your-secret&path=/weather/today"
```

### Tag Revalidation

```bash
curl -X POST "https://your-domain.com/api/payload/v1/revalidate?secret=your-secret&tag=articles-sitemap"
```

### Get Documentation

```bash
curl -X GET "https://your-domain.com/api/payload/v1/revalidate"
```

## Response Format

### Success Response

```json
{
  "success": true,
  "message": "Path revalidated successfully",
  "path": "/weather/today"
}
```

or

```json
{
  "success": true,
  "message": "Tag revalidated successfully",
  "tag": "articles-sitemap"
}
```

### Error Response

```json
{
  "error": "Unauthorized: Invalid or missing secret"
}
```

## HTTP Status Codes

- `200` - Success
- `400` - Bad Request (missing/invalid parameters)
- `401` - Unauthorized (invalid secret)
- `500` - Internal Server Error

## Security Features

1. **Secret Validation**: Requires matching `PAYLOAD_SECRET` environment variable
2. **Input Sanitization**: Validates and sanitizes all input parameters
3. **Path Traversal Protection**: Prevents directory traversal attacks
4. **Length Limits**: Enforces reasonable length limits on inputs
5. **Character Validation**: Tags must contain only alphanumeric characters, hyphens, and underscores
6. **Comprehensive Logging**: All requests and errors are logged for monitoring

## Integration with Payload Hooks

This endpoint is designed to work with the existing `revalidatePathCompat` and `revalidateTagCompat` functions in Payload hooks. The hooks will automatically fall back to using this API endpoint when direct Next.js cache methods are not available.

## Environment Variables

Make sure the following environment variable is set:

```bash
PAYLOAD_SECRET=your-secret-key-here
```

## Error Handling

The endpoint includes comprehensive error handling and logging:

- Invalid secrets are logged and rejected
- Malformed paths are sanitized or rejected
- Invalid tags are validated against allowed characters
- All revalidation failures are caught and logged
- Detailed error messages help with debugging

## Performance Considerations

- The endpoint validates inputs before attempting revalidation
- Logging is structured for easy monitoring and debugging
- Error responses are immediate to avoid timeouts
- Both path and tag revalidation use Next.js native methods for optimal performance
