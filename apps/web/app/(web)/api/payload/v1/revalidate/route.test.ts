import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { NextRequest } from 'next/server';
import { POST, GET } from './route';

// Mock Next.js cache functions
vi.mock('next/cache', () => ({
	revalidatePath: vi.fn(),
	revalidateTag: vi.fn(),
}));

// Mock environment variables
const originalEnv = process.env;

beforeEach(() => {
	vi.resetModules();
	process.env = {
		...originalEnv,
		PAYLOAD_SECRET: 'test-secret-123',
	};
});

afterEach(() => {
	process.env = originalEnv;
});

describe('/api/payload/v1/revalidate', () => {
	describe('GET', () => {
		it('should return documentation', async () => {
			const response = await GET();
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data.message).toBe('Revalidate API endpoint');
			expect(data.usage).toBeDefined();
			expect(data.examples).toBeDefined();
		});
	});

	describe('POST', () => {
		it('should reject requests without secret', async () => {
			const request = new NextRequest(
				'http://localhost:3000/api/payload/v1/revalidate?path=/test',
			);
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(401);
			expect(data.error).toContain('Unauthorized');
		});

		it('should reject requests with invalid secret', async () => {
			const request = new NextRequest(
				'http://localhost:3000/api/payload/v1/revalidate?secret=wrong&path=/test',
			);
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(401);
			expect(data.error).toContain('Unauthorized');
		});

		it('should reject requests without path or tag', async () => {
			const request = new NextRequest(
				'http://localhost:3000/api/payload/v1/revalidate?secret=test-secret-123',
			);
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('Either path or tag parameter is required');
		});

		it('should reject requests with both path and tag', async () => {
			const request = new NextRequest(
				'http://localhost:3000/api/payload/v1/revalidate?secret=test-secret-123&path=/test&tag=test-tag',
			);
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('Provide either path or tag, not both');
		});

		it('should successfully revalidate a path', async () => {
			const request = new NextRequest(
				'http://localhost:3000/api/payload/v1/revalidate?secret=test-secret-123&path=/weather/today',
			);
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data.success).toBe(true);
			expect(data.message).toBe('Path revalidated successfully');
			expect(data.path).toBe('/weather/today');
		});

		it('should successfully revalidate a tag', async () => {
			const request = new NextRequest(
				'http://localhost:3000/api/payload/v1/revalidate?secret=test-secret-123&tag=articles-sitemap',
			);
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data.success).toBe(true);
			expect(data.message).toBe('Tag revalidated successfully');
			expect(data.tag).toBe('articles-sitemap');
		});

		it('should sanitize paths by adding leading slash', async () => {
			const request = new NextRequest(
				'http://localhost:3000/api/payload/v1/revalidate?secret=test-secret-123&path=weather/today',
			);
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data.path).toBe('/weather/today');
		});

		it('should reject invalid paths with traversal attempts', async () => {
			const request = new NextRequest(
				'http://localhost:3000/api/payload/v1/revalidate?secret=test-secret-123&path=../../../etc/passwd',
			);
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('Invalid path format');
		});

		it('should reject invalid tags with special characters', async () => {
			const request = new NextRequest(
				'http://localhost:3000/api/payload/v1/revalidate?secret=test-secret-123&tag=invalid@tag!',
			);
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('Tag contains invalid characters');
		});
	});
});
