import { getPayload } from 'payload';
import { draftMode } from 'next/headers';
import { redirect } from 'next/navigation';
import { tryCatch } from '@/utils/tryCatch';

import configPromise from '@payload-config';

export async function GET(
	req: {
		cookies: {
			get: (name: string) => {
				value: string;
			};
		};
	} & Request,
): Promise<Response> {
	const payload = await getPayload({ config: configPromise });

	const { searchParams } = new URL(req.url);

	const path = searchParams.get('path');
	const previewSecret = searchParams.get('previewSecret');

	if (previewSecret !== process.env.PREVIEW_SECRET || !previewSecret) {
		return new Response('You are not allowed to preview this page', {
			status: 403,
		});
	}

	if (!path) {
		return new Response('Insufficient search params', { status: 404 });
	}

	if (!path.startsWith('/')) {
		return new Response(
			'This endpoint can only be used for relative previews',
			{ status: 500 },
		);
	}

	const { error, data } = await tryCatch(
		payload.auth({
			headers: req.headers,
		}),
	);

	if (error) {
		payload.logger.error(
			{ err: error },
			'Error verifying token for live preview',
		);
		return new Response('You are not allowed to preview this page', {
			status: 403,
		});
	}

	const draft = await draftMode();

	if (data && !data.user) {
		draft.disable();

		return new Response('You are not allowed to preview this page', {
			status: 403,
		});
	}

	draft.enable();

	redirect(path);
}
