import { draftMode } from 'next/headers';
import { NextResponse } from 'next/server';
import { getCurrentUser } from '@/utils/auth/getPayloadUser';

/**
 * Helper function to check if user has required roles
 */
async function validateUserRoles(): Promise<boolean> {
	try {
		const { user, userRoles } = await getCurrentUser();

		if (!user) {
			return false;
		}

		// Check if user has any of the required roles
		return userRoles.some((role) =>
			['admin', 'editor', 'web-developer'].includes(role),
		);
	} catch (error) {
		console.error('Error validating user roles:', error);
		return false;
	}
}

/**
 * GET - Check draft mode status
 * Returns whether draft mode is currently enabled
 * No authentication required for status check
 */
export async function GET(): Promise<NextResponse> {
	// Get draft mode status - must await it
	const draft = await draftMode();

	return NextResponse.json(
		{ data: { isEnabled: draft.isEnabled } },
		{ status: 200 },
	);
}

/**
 * POST - Enable draft mode
 * Sets the draft mode cookie to enable draft mode
 * Requires authentication and proper role
 */
export async function POST(): Promise<NextResponse> {
	try {
		// Validate user has required roles
		const isAuthorized = await validateUserRoles();

		if (!isAuthorized) {
			return NextResponse.json(
				{
					error:
						'Unauthorized: You must be logged in with appropriate permissions',
				},
				{ status: 401 },
			);
		}

		// Enable draft mode - must await it
		const draft = await draftMode();
		draft.enable();

		return NextResponse.json({ data: { isEnabled: true } }, { status: 200 });
	} catch (error) {
		return NextResponse.json(
			{
				error:
					error instanceof Error
						? error.message
						: 'Failed to enable draft mode',
			},
			{ status: 500 },
		);
	}
}

/**
 * DELETE - Disable draft mode
 * Clears the draft mode cookie to disable draft mode
 * Requires authentication and proper role
 */
export async function DELETE(): Promise<NextResponse> {
	try {
		// Validate user has required roles
		const isAuthorized = await validateUserRoles();

		if (!isAuthorized) {
			return NextResponse.json(
				{
					error:
						'Unauthorized: You must be logged in with appropriate permissions',
				},
				{ status: 401 },
			);
		}

		// Disable draft mode - must await it
		const draft = await draftMode();
		draft.disable();

		return NextResponse.json({ data: { isEnabled: false } }, { status: 200 });
	} catch (error) {
		return NextResponse.json(
			{
				error:
					error instanceof Error
						? error.message
						: 'Failed to disable draft mode',
			},
			{ status: 500 },
		);
	}
}
