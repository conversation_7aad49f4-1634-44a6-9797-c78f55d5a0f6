# Content Query API

This API provides access to content queries from both **Payload CMS** and **Kalliope** sources.

---

## 📌 Overview

The **Content Query API** allows clients to request content by either:

1. **Payload Content Query ID**
2. **Kalliope CMQS ID**

The API returns content in a **consistent format** regardless of the source.

---

## ⚙️ How It Works

### 📍 Endpoint

```text
GET /api/payload/v1/contentquery
```

### 🧾 Query Parameters

| Parameter  | Required | Description                                               |
| ---------- | -------- | --------------------------------------------------------- |
| `assetId`  | ✅ Yes   | The ID of the content query (Payload or Kalliope CMQS ID) |
| `language` | ❌ No    | The language code for the content (default: `en-US`)      |
| `apiKey`   | ✅ Yes   | API key for authentication                                |

---

### 🔄 Processing Flow

1. The API first checks if the `assetId` matches a **Payload** content query ID.
2. If found, it returns the **merged content** from that query.
3. If not found in Payload, it attempts to fetch from **Kalliope** using `getQuerySetIds`.
4. If that fails, it returns a **404 error**.

---

## 📦 Response Format

### For **Payload** Content Queries

The API returns an array of `mergedContent` objects:

```json
[
	{
		"id": "string",
		"source": "payload",
		"title": "string",
		"overrideTitle": "string",
		"description": "string",
		"overrideDescription": "string",
		"thumbnail": "string",
		"overrideThumbnail": "string",
		"url": "string",
		"sourceQueryID": "string",
		"pinned": true
	}
]
```

### For **Kalliope** Content

Returns the response from the **Kalliope API** as-is.

---

## 💡 Example Usage

### Fetch by Payload ID

```js
fetch(
	"/api/payload/v1/contentquery?assetId=64f8a1b2c3d4e5f6&language=en-US&apiKey=your-api-key",
)
	.then((response) => response.json())
	.then((data) => console.log(data));
```

### Fetch by Kalliope CMQS ID

```js
fetch(
	"/api/payload/v1/contentquery?assetId=f5b37cac-82ce-419d-b772-dd4e0d043b65&language=en-US&apiKey=your-api-key",
)
	.then((response) => response.json())
	.then((data) => console.log(data));
```

---

## 🚨 Error Handling

| Code | Message                    |
| ---- | -------------------------- |
| 400  | Missing required parameter |
| 401  | Invalid or missing API key |
| 404  | Content not found          |
| 500  | Internal server error      |

---

## 🛠️ TODO

- Add comprehensive language support beyond current implementation
- Improve locale conversion (e.g., `en-US` to `en_US` for Kalliope)
- Support multiple languages in content queries
- Implement language fallbacks when content is unavailable in the requested locale

---

## 📁 Related Files

- `apps/web/app/api/payload/v1/contentquery/route.ts` – API route handler
- `packages/dal/src/content/querysets/getQuerySetIds.ts` – Fetch Kalliope content
- `apps/web/collections/ContentQueries/` – Payload content query collection
- `apps/web/jobs/contentbytags/` – Background jobs for processing queries
- `apps/web/utils/jobs/queryHandlers.ts` – Query handling utilities

---

## 🧱 Content Query System Architecture

### 1. **Content Query Collection**

- Defines the schema in Payload CMS
- Supports multiple sub-queries (data, ID, Kalliope)
- Uses operators (AND/OR) for combining tags, categories, and topics

### 2. **Background Jobs**

- `getContentByTags`: Fetches by tags, categories, topics
- `getContentByIds`: Fetches by specific content IDs
- `getCMAssetByID`: Fetches from Kalliope via CMQS ID

### 3. **Query Handlers**

- `buildWhereConditions`: Builds MongoDB conditions
- `findQueryAndLock`: Ensures query processing is exclusive
- `updateQueryWithContent`: Updates stored query with new content

### 4. **API Route**

- Serves content from Payload or Kalliope based on ID
- Uses preprocessed query results for speed and reliability

> 🧵 The system uses a job queue for background processing, making responses fast and scalable.
