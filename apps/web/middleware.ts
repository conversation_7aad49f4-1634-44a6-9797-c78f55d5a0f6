import { type NextRequest, NextResponse } from 'next/server';
import { precompute } from 'flags/next';
import { precomputeFlags } from '@/flags';
import { determinePrivacyRegimeCode } from './utils/privacy/determinePrivacyRegimeCode';
import { formatGeocode } from './location/utils/formatGeocode';
import { getUserData } from './user/utils/upsxCookies';
import { deriveDeviceClass } from './utils/userAgent';
import { afterFiles } from './middleware/rewrites/index.mjs';
import {
	compileDestination,
	compileRewrites,
	findMatch,
	checkForRedirect,
	type RewriteRule,
} from './middleware/utils';

// Compile rewrite rules once at module load time for better performance
const compiledRewrites = compileRewrites(afterFiles as RewriteRule[]);

export const config = {
	matcher:
		'/((?!api|payload/admin|_next/static|.well-known|_next/image|favicon.ico|sitemap.xml|robots.txt).*)',
	unstable_allowDynamic: [
		// Bug with edge runtime and bloom-filters
		// https://github.com/Callidon/bloom-filters/issues/82
		// https://github.com/vercel/next.js/issues/51904
		'**/node_modules/.pnpm/lodash@*/node_modules/lodash/*.js',
		'**/node_modules/.pnpm/reflect-metadata@*/node_modules/reflect-metadata/Reflect.js',
	],
};

const WEATHER_COOKIE_OPTIONS = {
	domain: '.weather.com',
	secure: true,
};
export default async function edgeMiddleware(request: NextRequest) {
	// Extract Vercel GeoIP information
	const latitude = request.headers.get('x-vercel-ip-latitude');
	const longitude = request.headers.get('x-vercel-ip-longitude');
	const countryCode = request.headers.get('x-vercel-ip-country');
	const regionCode = request.headers.get('x-vercel-ip-country-region');

	// get privacy regime code, to be stored as cookie, and as value in precompute code
	const privacyRegimeCode = determinePrivacyRegimeCode(countryCode, regionCode);
	request.headers.set('x-twc-privacy', privacyRegimeCode);

	// Get the original pathname
	const originalPathname = request.nextUrl.pathname;
	const originalSearch = request.nextUrl.search;

	request.headers.set(
		'x-search-params',
		request.nextUrl.searchParams.toString(),
	);

	// store device class in header
	const deviceClass = deriveDeviceClass(request);
	request.headers.set('x-device-class', deviceClass);

	// store user tier in header
	const userStatus = await getUserData();
	const userSubscriptionTier = userStatus.subscriptionTier.toString();
	request.headers.set('x-user-tier', userSubscriptionTier);

	const headerObj = Object.fromEntries(request.headers.entries());
	console.log(
		'[vercel edge middleware]:',
		originalPathname,
		JSON.stringify(headerObj, null, 2),
	);

	// Check for redirects
	const redirectResponse = await checkForRedirect(request);

	if (redirectResponse) {
		return redirectResponse;
	}

	// Compute flags code
	const code = await precompute(precomputeFlags);

	// Path to be matched against our custom router (includes the flag code)
	const pathForRouter = `/${code}${originalPathname}`;

	let rewrittenUrl: URL;

	// Use the new custom routing logic with compiled rewrites
	const customMatch = findMatch(pathForRouter, compiledRewrites, request);

	if (customMatch) {
		// A custom rewrite rule matched
		const { rule, params } = customMatch;
		// Ensure the 'code' param from the pathForRouter is part of the params for destination
		const finalParams = { code, ...params };
		const rewrittenPathAndQuery = compileDestination(
			rule.destination,
			finalParams,
			originalSearch,
		);
		rewrittenUrl = new URL(rewrittenPathAndQuery, request.url);
	} else {
		// No custom rewrite rule matched, use default flag-based rewrite
		rewrittenUrl = new URL(
			`/${code}${originalPathname}${originalSearch}`,
			request.url,
		);
	}

	const response = NextResponse.rewrite(rewrittenUrl, { request });

	// Set geocode cookie if latitude and longitude are available
	if (latitude && longitude) {
		const geocode = formatGeocode(`${latitude},${longitude}`);
		response.cookies.set(
			'twc-location-geocode',
			geocode,
			WEATHER_COOKIE_OPTIONS,
		);
	} else {
		// Fallback to a default geocode of Brookhaven, GA
		// if latitude and longitude are not available
		response.cookies.set(
			'twc-location-geocode',
			'33.86,-84.34',
			WEATHER_COOKIE_OPTIONS,
		);
	}

	if (countryCode) {
		response.cookies.set(
			'twc-location-country',
			countryCode,
			WEATHER_COOKIE_OPTIONS,
		);
	}

	if (regionCode) {
		response.cookies.set(
			'twc-location-region',
			regionCode,
			WEATHER_COOKIE_OPTIONS,
		);
	}

	// Set privacy cookie based on country and region codes
	response.cookies.set(
		'twc-privacy',
		privacyRegimeCode,
		WEATHER_COOKIE_OPTIONS,
	);

	// Check if payload-token cookie exists and set debug mode cookie
	if (request.cookies.has('payload-token')) {
		response.cookies.set('twc-debug-mode', 'true', WEATHER_COOKIE_OPTIONS);
	}

	if (userStatus?.isUserLoggedIn) {
		response.cookies.set(
			'twc-user',
			userSubscriptionTier,
			WEATHER_COOKIE_OPTIONS,
		);
	} else {
		response.cookies.set('twc-user', userSubscriptionTier, {
			...WEATHER_COOKIE_OPTIONS,
			expires: -1,
		});
	}

	return response;
}
