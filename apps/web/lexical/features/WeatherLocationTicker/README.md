# Weather Location Ticker Feature

This Lexical editor feature allows content editors to convert location names like "Atlanta" into interactive links with weather icons and current temperature.

## Table of Contents

- [Overview](#overview)
  - [Purpose](#purpose)
  - [Key Features](#key-features)
- [Component Structure](#component-structure)
  - [Node Implementation](#node-implementation)
  - [Plugin Implementation](#plugin-implementation)
  - [Toolbar Integration](#toolbar-integration)
- [Usage](#usage)
  - [For Content Editors](#for-content-editors)
  - [For Developers](#for-developers)
- [Data Flow](#data-flow)
  - [Location Lookup](#location-lookup)
  - [Weather Data Fetching](#weather-data-fetching)
  - [Caching Strategy](#caching-strategy)
- [Implementation Details](#implementation-details)
  - [SWR Integration](#swr-integration)
  - [Error Handling](#error-handling)
  - [Styling](#styling)
- [Examples](#examples)
- [Best Practices](#best-practices)
- [Technical Notes](#technical-notes)

## Overview

### Purpose

The Weather Location Ticker feature enhances content by transforming plain location names into interactive elements that display real-time weather information. This provides readers with immediate context about weather conditions at mentioned locations without leaving the page.

### Key Features

- Convert text to weather location links with a toolbar button
- Automatically display current temperature and weather icon
- Link to the location's weather page
- Handle location lookup and geocoding
- Cache weather data to minimize API calls
- Support for removing weather location formatting
- Graceful handling of loading and error states

## Component Structure

The Weather Location Ticker feature follows the standard Lexical feature architecture with three main components:

### Node Implementation

`WeatherLocationTickerNode.tsx` defines a custom Lexical `DecoratorNode` that:

- Stores location name, geocode, and placeId
- Renders a React component with weather data
- Handles serialization/deserialization
- Manages DOM conversion and export

```typescript
export class WeatherLocationTickerNode extends DecoratorNode<React.ReactElement> {
  __locationName: string;
  __geocode?: string;
  __placeId?: string;

  // Node implementation...

  decorate(): React.ReactElement<any, any> {
    return (
      <WeatherLocationTickerLexical
        locationName={this.__locationName}
        geocode={this.__geocode}
        placeId={this.__placeId}
        nodeKey={this.__key}
      />
    );
  }
}
```

### Plugin Implementation

`plugins/index.tsx` provides a Lexical plugin that:

- Registers a custom command for creating weather location nodes
- Handles text transformation when the command is triggered
- Manages editor state updates

```typescript
export const WeatherLocationTickerPlugin: PluginComponent = () => {
	const [editor] = useLexicalComposerContext();

	useEffect(() => {
		// Register command listener
		const commandListener = editor.registerCommand<string>(
			WEATHER_LOCATION_COMMAND,
			(locationName) => {
				handleWeatherLocationTransform(locationName);
				return true;
			},
			COMMAND_PRIORITY_EDITOR,
		);

		// Plugin implementation...

		return () => {
			mergeRegister(commandListener);
		};
	}, [editor]);

	return null;
};
```

### Toolbar Integration

`toolbar/WeatherLocationTickerToggleButton.tsx` provides UI controls that:

- Add a button to the Lexical toolbar
- Toggle between adding and removing weather location formatting
- Detect when weather location nodes are selected
- Dispatch appropriate commands based on selection state

```typescript
export function WeatherLocationTickerToggleButton(): React.ReactElement | null {
  const [editor] = useLexicalComposerContext();
  const [isWeatherLocationSelected, setIsWeatherLocationSelected] = useState(false);
  const [hasValidSelection, setHasValidSelection] = useState(false);

  // Button implementation...

  return (
    <button
      type="button"
      className={`toolbar-popup__button ${isWeatherLocationSelected ? 'remove-weather-location-button' : 'weather-location-button'}`}
      onClick={handleClick}
      title={isWeatherLocationSelected ? 'Remove Weather Location Ticker' : 'Convert to Weather Location Ticker'}
    >
      {/* Button content */}
    </button>
  );
}
```

## Usage

### For Content Editors

1. **Adding a Weather Location**:

   - Select a location name in your text (e.g., "Atlanta")
   - Click the Weather Location button in the toolbar (cloud with rain icon)
   - The text will be converted to a weather location with current conditions

2. **Removing a Weather Location**:

   - Click on an existing weather location to select it
   - Click the Remove Weather Location button in the toolbar (crossed-out map pin icon)
   - The weather location will be converted back to plain text

3. **What You'll See**:

   - Location name
   - Current weather icon
   - Current temperature

4. **Behavior**:
   - In the editor, the weather location appears as a styled element that can be selected
   - In the published content, it functions as a link to the location's weather page
   - Weather data refreshes automatically when viewed

### For Developers

#### Integration

The Weather Location Ticker feature is already integrated into the default Lexical editor configuration. It uses:

- `@repo/dal/weather/current` for current weather conditions
- `@repo/dal/locations` for location lookup
- `@repo/icons/WxIcon` for weather icons (via the `<WxIcon iconCode={...} />` component)

To use this feature in a custom Lexical editor configuration:

```typescript
import { WeatherLocationTickerClientFeature } from "@/lexical/features/weatherLocationTicker/feature.client";

// In your Lexical editor configuration
const features = [
	// Other features...
	WeatherLocationTickerClientFeature,
];

// Create editor with features
const Editor = createEditor({
	features,
	// Other configuration...
});
```

## Data Flow

### Location Lookup

1. When a location is selected and converted:

   - A `WeatherLocationTickerNode` is created with just the location name
   - The node is inserted into the editor

2. When the node is rendered:
   - If no geocode exists, it looks up the location using `getLocationsByQuery`
   - Once location data is available, it updates the node with geocode and placeId

```typescript
// Fetch location data if we don't have geocode yet
const { data: locationData } = useSWR(
	!geocode && locationName ? `location-${locationName}` : null,
	async () => {
		const locations = await getLocationsByQuery(locationName);
		if (locations && locations.length > 0) {
			return locations[0];
		}
		return null;
	},
	{
		revalidateOnFocus: false,
		dedupingInterval: 3600000, // 1 hour
	},
);
```

### Weather Data Fetching

1. Once geocode is available (either from the node or from location lookup):

   - The component fetches current weather using `getCurrentObservations`
   - Weather data includes temperature and icon code

2. The component then:
   - Displays the temperature
   - Fetches the appropriate weather icon
   - Renders the complete weather location ticker

```typescript
// Fetch weather data if we have geocode
const { data: weatherData } = useSWR(
	effectiveGeocode ? `weather-${effectiveGeocode}` : null,
	async () => {
		const observations = await getCurrentObservations({
			geocode: effectiveGeocode!,
			units: Units.IMPERIAL,
			language: "en-US",
		});
		return observations;
	},
	{
		revalidateOnFocus: false,
		dedupingInterval: 300000, // 5 minutes
	},
);
```

### Caching Strategy

The feature uses SWR with carefully tuned caching parameters:

1. **Location Data**:

   - Cache duration: 1 hour (`dedupingInterval: 3600000`)
   - No revalidation on focus (`revalidateOnFocus: false`)
   - This reduces API calls for location lookups that rarely change

2. **Weather Data**:

   - Cache duration: 5 minutes (`dedupingInterval: 300000`)
   - No revalidation on focus (`revalidateOnFocus: false`)
   - This balances freshness with API efficiency

3. **Weather Icons**:
   - Cache duration: Infinite (`dedupingInterval: Infinity`)
   - Icons are static assets that don't change

## Implementation Details

### SWR Integration

The feature uses SWR (stale-while-revalidate) for all data fetching, following the project's standard pattern:

```typescript
const { data, error, isLoading } = useSWR(
	key, // A unique key for this data request
	fetcher, // Async function that returns the data
	options, // Configuration options
);
```

Key benefits of this approach:

- **Automatic Caching**: Prevents redundant API calls
- **Loading States**: Built-in isLoading state for UI feedback
- **Error Handling**: Standardized error handling
- **Deduplication**: Prevents duplicate requests during rendering

### Error Handling

The feature implements graceful error handling at multiple levels:

1. **Location Lookup Errors**:

   - If location lookup fails, the component displays just the location name
   - The node remains functional but without weather data

2. **Weather Data Errors**:

   - If weather data fetching fails, the component shows an error indicator
   - The location name remains clickable

3. **Icon Loading Errors**:
   - If icon loading fails, the component still displays temperature
   - Fallback visual indicators are shown when needed

### Styling

The component uses Tailwind CSS classes for styling:

```typescript
<span
  className={`inline-flex items-center gap-1 py-0.5 px-1.5 rounded text-blue-700 no-underline font-medium transition-colors duration-200 cursor-default ${
    isSelected ? 'outline-blue-700' : ''
  }`}
  onClick={handleClick}
>
  {/* Component content */}
</span>
```

Key styling features:

- **Visual Distinction**: Weather locations are styled with a light blue background
- **Selection State**: Selected nodes have a blue outline
- **Responsive Layout**: Flexbox layout adapts to content size
- **Loading States**: Visual indicators for loading and error states

## Examples

### Before and After

```jsx
// Before: Plain text
Atlanta

// After: Weather location ticker
Atlanta 🌤️ 72°
```

### HTML Output

When exported to HTML, the node becomes:

```html
<a
	href="/weather/today/l/USGA0028:1:US"
	class="weather-location-ticker"
	data-location-name="Atlanta"
	data-place-id="USGA0028:1:US"
	data-geocode="33.749,-84.388"
	>Atlanta</a
>
```

### Node Creation

```typescript
// Creating a weather location node programmatically
const weatherLocationNode = $createWeatherLocationTickerNode("Atlanta");
selection.insertNodes([weatherLocationNode]);
```

## Best Practices

1. **Use for Relevant Locations**:

   - Apply to location names that benefit from weather context
   - Avoid overusing on every mention of a place

2. **Consider Context**:

   - Weather locations work best in weather-related content
   - May be distracting in unrelated contexts

3. **Check Selection**:

   - Ensure you've selected only the location name
   - Avoid selecting surrounding punctuation or spaces

4. **Performance Considerations**:
   - Each weather location makes API calls
   - Use judiciously in content with many locations

## Technical Notes

- **Node Serialization**: Nodes store location name, geocode, and placeId
- **API Integration**: Uses the Data Access Layer (DAL) for weather and location data
- **Icon Handling**: Weather icons are rendered using the `<WxIcon iconCode={...} />` component from `@repo/icons/WxIcon`.
- **Lexical Integration**: Follows PayloadCMS Lexical feature pattern
- **Server/Client Split**: Server feature registration with client-side rendering
- **Accessibility**: Includes proper ARIA attributes and keyboard navigation
