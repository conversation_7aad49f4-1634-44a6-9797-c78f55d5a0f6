'use client';

import React, { useEffect, useState } from 'react';
import type {
	DOMConversionMap,
	DOMConversionOutput,
	DOMExportOutput,
	LexicalNode,
	NodeKey,
	SerializedLexicalNode,
} from '@payloadcms/richtext-lexical/lexical';
import {
	DecoratorNode,
	$applyNodeReplacement,
} from '@payloadcms/richtext-lexical/lexical';
import { $getNodeByKey } from '@payloadcms/richtext-lexical/lexical';
import { useLexicalNodeSelection } from '@payloadcms/richtext-lexical/lexical/react/useLexicalNodeSelection';
import { useLexicalComposerContext } from '@payloadcms/richtext-lexical/lexical/react/LexicalComposerContext';
import { getCurrentObservations } from '@repo/dal/weather/current/observations';
import { getLocationsByQuery } from '@repo/dal/locations/search';
import { ICON_CODE_MAP, Units } from '@repo/dal/weather/types';
import useSWR from 'swr';
import { WxIcon } from '@repo/icons/WxIcon';
import { NoData } from '@repo/icons/Data';
import { useAtomValue } from 'jotai';
import { userUnitPreferenceAtom } from '@/user/atoms/preferences/units';
import { unitsSystemByName } from '@/constants/unitData';

export type SerializedWeatherLocationTickerNode = SerializedLexicalNode & {
	type: 'weatherLocationTicker';
	locationName: string;
	geocode?: string;
	placeId?: string;
	version: 1;
};

export class WeatherLocationTickerNode extends DecoratorNode<React.ReactElement> {
	__locationName: string;
	__geocode?: string;
	__placeId?: string;

	static getType(): string {
		return 'weatherLocationTicker';
	}

	static clone(node: WeatherLocationTickerNode): WeatherLocationTickerNode {
		return new WeatherLocationTickerNode(
			node.__locationName,
			node.__geocode,
			node.__placeId,
			node.__key,
		);
	}

	constructor(
		locationName: string,
		geocode?: string,
		placeId?: string,
		key?: NodeKey,
	) {
		super(key);
		this.__locationName = locationName;
		this.__geocode = geocode;
		this.__placeId = placeId;
	}

	createDOM(): HTMLElement {
		const dom = document.createElement('span');
		dom.className =
			'inline-flex items-center gap-1 py-0.5 px-1.5 rounded bg-blue-500/10 text-blue-700 no-underline font-medium transition-colors duration-200 cursor-default';
		dom.setAttribute('data-location-name', this.__locationName);
		if (this.__geocode) {
			dom.setAttribute('data-geocode', this.__geocode);
		}
		if (this.__placeId) {
			dom.setAttribute('data-place-id', this.__placeId);
		}
		return dom;
	}

	updateDOM(): false {
		return false;
	}

	static importDOM(): DOMConversionMap | null {
		return {
			span: (domNode: HTMLElement) => {
				// Check for either the old CSS class or the new Tailwind classes
				if (
					domNode.classList.contains('weather-location-ticker') ||
					(domNode.classList.contains('inline-flex') &&
						domNode.classList.contains('bg-blue-500/10') &&
						domNode.hasAttribute('data-location-name'))
				) {
					return {
						conversion: convertWeatherLocationElement,
						priority: 1,
					};
				}
				return null;
			},
		};
	}

	exportDOM(): DOMExportOutput {
		const element = document.createElement('span');
		element.classList.add(
			'inline-flex',
			'items-center',
			'gap-1',
			'py-0.5',
			'px-1.5',
			'rounded',
			'bg-blue-500/10',
			'text-blue-700',
			'no-underline',
			'font-medium',
			'transition-colors',
			'duration-200',
			'cursor-default',
		);
		element.setAttribute('data-location-name', this.__locationName);

		if (this.__geocode) {
			element.setAttribute('data-geocode', this.__geocode);
		}

		if (this.__placeId) {
			element.setAttribute('data-place-id', this.__placeId);
		}

		element.textContent = this.__locationName;

		return { element };
	}

	exportJSON(): SerializedWeatherLocationTickerNode {
		return {
			type: 'weatherLocationTicker',
			locationName: this.__locationName,
			geocode: this.__geocode,
			placeId: this.__placeId,
			version: 1,
		};
	}

	static importJSON(
		serializedNode: SerializedWeatherLocationTickerNode,
	): WeatherLocationTickerNode {
		return new WeatherLocationTickerNode(
			serializedNode.locationName,
			serializedNode.geocode,
			serializedNode.placeId,
		);
	}

	getLocationName(): string {
		return this.__locationName;
	}

	getGeocode(): string | undefined {
		return this.__geocode;
	}

	getPlaceId(): string | undefined {
		return this.__placeId;
	}

	getTextContent(): string {
		return this.__locationName;
	}

	setGeocode(geocode: string): void {
		const self = this.getWritable();
		self.__geocode = geocode;
	}

	setPlaceId(placeId: string): void {
		const self = this.getWritable();
		self.__placeId = placeId;
	}

	decorate(): React.ReactElement<typeof WeatherLocationTickerLexical> {
		return (
			<WeatherLocationTickerLexical
				locationName={this.__locationName}
				geocode={this.__geocode}
				placeId={this.__placeId}
				nodeKey={this.__key}
			/>
		);
	}
}

interface WeatherLocationTickerLexicalProps {
	locationName: string;
	geocode?: string;
	placeId?: string;
	nodeKey: NodeKey;
}

// Lexical component to render weather location ticker
// This component is responsible for fetching the weather data and displaying it
export function WeatherLocationTickerLexical({
	locationName,
	geocode,
	nodeKey,
}: WeatherLocationTickerLexicalProps): React.ReactElement {
	const [editor] = useLexicalComposerContext();
	const [isSelected, setSelected] = useLexicalNodeSelection(nodeKey);
	const [temperature, setTemperature] = useState<number | null>(null);
	const [isLoading, setIsLoading] = useState<boolean>(true);
	const [error, setError] = useState<Error | null>(null);

	// Fetch location data if we don't have geocode yet
	const { data: locationData, error: locationError } = useSWR(
		!geocode && locationName ? `location-${locationName}` : null,
		async () => {
			try {
				const locations = await getLocationsByQuery(locationName);
				if (locations && locations.length > 0) {
					return locations[0];
				}
				return null;
			} catch (err) {
				console.error('Error fetching location:', err);
				return null;
			}
		},
		{
			revalidateOnFocus: false,
			dedupingInterval: 3600000, // 1 hour
		},
	);

	// Update node with geocode and placeId if we got location data
	useEffect(() => {
		if (locationData && !geocode && editor) {
			editor.update(() => {
				const node = $getNodeByKey<WeatherLocationTickerNode>(nodeKey);
				if (node?.getType() === 'weatherLocationTicker') {
					const newGeocode = `${locationData.latitude},${locationData.longitude}`;

					node.setGeocode(newGeocode);
					node.setPlaceId(locationData.placeId);
				}
			});
		}
	}, [locationData, geocode, editor, nodeKey]);

	// Effective geocode is either the one stored in the node or the one we just fetched
	const effectiveGeocode =
		geocode ||
		(locationData
			? `${locationData.latitude},${locationData.longitude}`
			: undefined);

	const unit = useAtomValue(userUnitPreferenceAtom);
	const unitCode = unitsSystemByName(unit)?.code as Units;

	// Fetch weather data if we have geocode
	const { data: weatherData, error: weatherError } = useSWR(
		effectiveGeocode ? ['currentCondition', effectiveGeocode, unitCode] : null,
		async ([_, geocode, units]) => {
			try {
				const observations = await getCurrentObservations({
					geocode,
					units,
					language: 'en-US',
				});
				return observations;
			} catch (err) {
				console.error('Error fetching weather:', err);
				return null;
			}
		},
		{
			revalidateOnFocus: false,
			dedupingInterval: 300000, // 5 minutes
		},
	);

	// Update temperature and icon when weather data is available
	useEffect(() => {
		if (weatherData) {
			setTemperature(Math.round(weatherData.temperature));
			setIsLoading(false);
		} else if (weatherError) {
			setError(weatherError);
			setIsLoading(false);
		}
	}, [weatherData, weatherError]);

	// Handle click to select node
	const handleClick = (event: React.MouseEvent): void => {
		event.preventDefault();
		if (!isSelected) {
			setSelected(true);
		}
	};

	// If we're still loading initial location data
	if (!geocode && !locationData && !locationError && isLoading) {
		return (
			<span className="inline-flex items-center gap-1 rounded bg-black/5 px-1.5 py-0.5 text-gray-600">
				{locationName}...
			</span>
		);
	}

	// If there was an error finding the location
	if (locationError || (!locationData && !geocode)) {
		return (
			<span className="inline-flex items-center gap-1 rounded bg-transparent px-1.5 py-0.5 text-gray-600 underline decoration-dotted">
				{locationName}
			</span>
		);
	}

	return (
		<span
			className={`inline-flex cursor-default items-center gap-1 rounded px-1.5 py-0.5 font-medium text-blue-700 no-underline transition-colors duration-200 ${
				isSelected ? 'outline-blue-700' : ''
			}`}
			onClick={handleClick}
		>
			<span className="mr-0.5">{locationName}</span>
			{isLoading ? (
				<span>...</span>
			) : error ? (
				<span>!</span>
			) : (
				<>
					{weatherData?.iconCode !== undefined && (
						<span className="flex items-center justify-center">
							{(ICON_CODE_MAP[weatherData.iconCode] || 'na') !== 'na' ? (
								<WxIcon
									iconCode={weatherData.iconCode}
									className="lightBG"
									aria-label={
										ICON_CODE_MAP[weatherData.iconCode] ||
										`Weather condition ${weatherData.iconCode}`
									}
									iconTheme="lightBG"
									size="sm"
								/>
							) : (
								<NoData
									className="lightBG"
									aria-label="Weather icon not available"
									size="sm"
								/>
							)}
						</span>
					)}
					<span className="font-semibold">{temperature}°</span>
				</>
			)}
		</span>
	);
}

function convertWeatherLocationElement(
	domNode: HTMLElement,
): DOMConversionOutput {
	const locationName =
		domNode.getAttribute('data-location-name') || domNode.textContent || '';
	const geocode = domNode.getAttribute('data-geocode') || undefined;
	const placeId = domNode.getAttribute('data-place-id') || undefined;

	return {
		node: $createWeatherLocationTickerNode(locationName, geocode, placeId),
	};
}

export function $createWeatherLocationTickerNode(
	locationName: string,
	geocode?: string,
	placeId?: string,
): WeatherLocationTickerNode {
	return $applyNodeReplacement(
		new WeatherLocationTickerNode(locationName, geocode, placeId),
	);
}

export function $isWeatherLocationTickerNode(
	node: LexicalNode | null | undefined,
): node is WeatherLocationTickerNode {
	return node instanceof WeatherLocationTickerNode;
}
