'use client';

import React, { useState, useEffect } from 'react';
import type { SerializedWeatherLocationTickerNode } from '../nodes/WeatherLocationTickerNode';
import useSWR from 'swr';
import { getCurrentObservations } from '@repo/dal/weather/current/observations';
import { ICON_CODE_MAP, Units } from '@repo/dal/weather/types';
import { useAtomValue } from 'jotai';
import { WxIcon } from '@repo/icons/WxIcon';
import { NoData } from '@repo/icons/Data';
import { userUnitPreferenceAtom } from '@/user/atoms/preferences/units';
import { unitsSystemByName } from '@/constants/unitData';

// This is a client component that can use hooks, state, etc.
export function WeatherLocationTickerClientComponent({
	node,
}: {
	node: SerializedWeatherLocationTickerNode;
}) {
	const [temperature, setTemperature] = useState<number | null>(null);
	const [isLoading, setIsLoading] = useState<boolean>(true);
	const [error, setError] = useState<Error | null>(null);

	const unit = useAtomValue(userUnitPreferenceAtom);
	const unitCode = unitsSystemByName(unit)?.code as Units;

	// Fetch weather data if we have geocode
	const { data: weatherData, error: weatherError } = useSWR(
		node.geocode ? ['currentConditions', node.geocode, unitCode] : null,
		async ([_, geocode, units]) => {
			try {
				const observations = await getCurrentObservations({
					geocode,
					units,
					language: 'en-US',
				});
				return observations;
			} catch (err) {
				console.error('Error fetching weather:', err);
				return null;
			}
		},
		{
			revalidateOnFocus: false,
			dedupingInterval: 300000, // 5 minutes
		},
	);

	// Update temperature and icon when weather data is available
	useEffect(() => {
		if (weatherData) {
			setTemperature(Math.round(weatherData.temperature));
			setIsLoading(false);
		} else if (weatherError) {
			setError(weatherError);
			setIsLoading(false);
		}
	}, [weatherData, weatherError]);

	// Generate URL for the location
	const locationUrl = node.placeId ? `/weather/today/l/${node.placeId}` : '#';

	return (
		<a href={locationUrl} className="inline-flex items-center gap-1">
			<span className="border-b-2 border-dotted border-black">
				{node.locationName}
			</span>
			{isLoading ? (
				<span className="text-gray-400">...</span>
			) : error ? (
				<span className="text-gray-400">!</span>
			) : (
				<span className="inline-flex items-center rounded-full bg-gray-100 px-2">
					{weatherData?.iconCode !== undefined && (
						<span className="flex items-center">
							{(ICON_CODE_MAP[weatherData.iconCode] || 'na') !== 'na' ? (
								<WxIcon
									iconCode={weatherData.iconCode}
									className="lightBG"
									aria-label={
										ICON_CODE_MAP[weatherData.iconCode] ||
										`Weather condition ${weatherData.iconCode}`
									}
									iconTheme="lightBG"
									size="sm"
								/>
							) : (
								<NoData
									className="lightBG"
									aria-label="Weather icon not available"
									size="sm"
								/>
							)}
						</span>
					)}
					<span className="font-medium">{temperature}°</span>
				</span>
			)}
		</a>
	);
}
