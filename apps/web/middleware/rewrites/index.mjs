import todayPage from './today.mjs';
import { afterFiles as contentPageAfterFiles } from './content.mjs';
import homePage from './home.mjs';
import collectionPage from './collections.mjs';

export const afterFiles = [
	...todayPage,
	...homePage,

	// pages byId intl
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/pages/:assetId([0-9a-f]{24})',
		destination: '/:code/pages/byId/:locale/:assetId',
	},

	// pages byId en-US
	{
		source: '/:code/pages/:assetId([0-9a-f]{24})',
		destination: '/:code/pages/byId/en-US/:assetId',
	},

	// pages byAssetName intl
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/pages/:assetName*',
		destination: '/:code/pages/byAssetName/:locale/:assetName*',
	},

	// pages byAssetName en-US
	{
		source: '/:code/pages/:assetName*',
		destination: '/:code/pages/byAssetName/en-US/:assetName*',
	},

	...collectionPage,
	...contentPageAfterFiles,
];

/**
 * All Next.js rewrites for the web app.
 * @type {import('next').NextConfig['rewrites']}
 */
export default async function rewrites() {
	return {
		// These rewrites are checked after headers/redirects
		// and before all files including _next/public files which
		// allows overriding page files
		beforeFiles: [],

		// These rewrites are checked after pages/public files
		// are checked but before dynamic routes
		afterFiles: [],

		// These rewrites are checked after both pages/public files
		// and dynamic routes are checked

		// Fallback to dev.weather.com for preview (pull requests) or local environments
		// All other custom environments (dev, staging) should be accessed via split-brain
		// Akamai config, therefore no fallback is set there.
		// Production is split-brain regardless, and handles routing to wxu-web and wx-next.
		fallback: [
			...(process.env.VERCEL_TARGET_ENV === 'preview' ||
			process.env.VERCEL_TARGET_ENV === 'development'
				? [
						{
							source: '/:code/:path*',
							destination: 'https://dev.weather.com/:path*',
						},
					]
				: []),
		],
	};
}
