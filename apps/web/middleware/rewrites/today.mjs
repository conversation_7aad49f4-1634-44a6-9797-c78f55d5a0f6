const destinationByLocId = '/:code/weather/today/byLocId/:locale/:locId';
const destinationByCanonicalLocation =
	'/:code/weather/today/byCanonicalLocation/:lang/:country/:adminDistrictCode/:locationType/:displayName';

/**
 * Next.js route rewrites for the today page.
 */
export default [
	// en-us
	{
		source: '/:code/weather/today/l/:locId',
		destination: '/:code/weather/today/byLocId/en-US/:locId',
	},

	// legacy intl
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/weather/today/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/clima/hoje/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/cuaca/dina-iki/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/cuaca/sekarang/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/haliyahewa/leo/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/howa/sugun/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/idojaras/ma/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/ilm/tana/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/kisisel/bugun/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/laiks/sodien/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/moti/sot/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/ob-havo/bugun/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/orai/siandien/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/pocasi/dnes/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/pocasie/dnes/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/pogoda/dzisiaj/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/tempo/oggi/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/temps/aujour/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/temps/avui/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/tiempo/hoy/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/vedur/idag/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/vejret/idag/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/vreme/astazi/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/vreme/danas/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/vreme/danes/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/vrijeme/danas/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/weer/vandaag/l/:locId',
		destination: destinationByLocId,
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/wetter/heute/l/:locId',
		destination: destinationByLocId,
	},

	// new
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/today',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/hoje',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/dina',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/sekarang',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/leo',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/sugun',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/ma',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/tana',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/bugun',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/sodien',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/sot',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName-havo',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/siandien',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/dnes',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/dnes',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/dzisiaj',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/oggi',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/aujour',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/avui',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/hoy',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/idag',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/idag',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/astazi',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/danas',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/danes',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/danas',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/vandaag',
		destination: destinationByCanonicalLocation,
	},
	{
		source:
			'/:code/:lang/:country/:adminDistrictCode/:locationType/:displayName/heute',
		destination: destinationByCanonicalLocation,
	},
];
