export const afterFiles = [
	// Payload articles with date-based paths
	{
		// Payload en-US articles by assetName
		// eg. /en-US/2025/03/27/news/testing
		source:
			'/:code/:year(\\d{4})/:month(\\d{2})/:day(\\d{2})/:collectionId*/:slug',
		destination:
			'/:code/content/payload/byAssetName/en-US/:year/:month/:day/:collectionId*/:slug',
	},
	{
		// Payload intl articles by assetName
		// eg. /en-US/2025/03/27/news/testing
		source:
			'/:code/:locale([a-z]{2}-[A-Z]{2})/:year(\\d{4})/:month(\\d{2})/:day(\\d{2})/:collectionId*/:slug',
		destination:
			'/:code/content/payload/byAssetName/:locale/:year/:month/:day/:collectionId*/:slug',
	},

	// Payload en-US articles by articleId
	{
		// eg. /en-US/2025/03/27/news/testing
		source: '/:code/content/:articleId([0-9a-fA-F]{24})',
		destination: '/:code/content/payload/byId/en-US/:articleId',
	},

	// Payload intl articles by articleId
	{
		// eg. /en-US/2025/03/27/news/testing
		source:
			'/:code/:locale([a-z]{2}-[A-Z]{2})/content/:articleId([0-9a-fA-F]{24})',
		destination: '/:code/content/payload/byId/:locale/:articleId',
	},

	// Classic intl articles by assetName
	{
		source:
			'/:code/:locale([a-z]{2}-[A-Z]{2})/:collectionId*/news/:articleTitle',
		destination:
			'/:code/content/classic/byAssetName/:locale/:collectionId*/news/:articleTitle',
	},

	// Classic en-US articles by assetName
	{
		source: '/:code/:collectionId*/news/:articleTitle',
		destination:
			'/:code/content/classic/byAssetName/en-US/:collectionId*/news/:articleTitle',
	},

	// Classic intl articles by assetId
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/content/classic/:assetId',
		destination: '/:code/content/classic/byAssetId/:locale/:assetId',
	},

	// Classic en-US articles by assetId
	{
		source: '/:code/content/classic/:assetId',
		destination: '/:code/content/classic/byAssetId/en-US/:assetId',
	},

	// Classic en-US Atmosphere Reviews (explicit route)
	{
		source: '/:code/atmosphere/reviews/:slug*',
		destination:
			'/:code/content/classic/byAssetName/en-US/atmosphere/reviews/:slug*',
	},

	{
		source: '/:code/bios/:authorSlug',
		destination: '/:code/content/classic/byAssetName/en-US/bios/:authorSlug',
	},
];
