import { type NextRequest } from 'next/server';
import type { CompiledRewriteRule, RouteParams } from './types';
import { checkConditions } from './checkConditions';

/**
 * Finds the first rewrite rule that matches the given pathname and request conditions.
 * @param pathname - The pathname to match.
 * @param compiledRewrites - Array of pre-compiled rewrite rules.
 * @param request - The NextRequest object.
 * @returns The matched rule and extracted parameters, or null if no match.
 */
export function findMatch(
	pathname: string,
	compiledRewrites: CompiledRewriteRule[],
	request: NextRequest,
): { rule: CompiledRewriteRule; params: RouteParams } | null {
	for (const rule of compiledRewrites) {
		// Try to match the pathname against the rule's source pattern
		const pathMatch = rule.matcher(pathname);

		if (pathMatch) {
			// Path matched, now check conditions
			const pathParams: RouteParams = (pathMatch.params as RouteParams) || {};

			const conditionResult = checkConditions(rule, request, pathParams);

			if (conditionResult.match) {
				// Both path and conditions matched
				return {
					rule,
					params: conditionResult.conditionParams || pathParams,
				};
			}
		}
	}

	// No matching rule found
	return null;
}
