import { type PathFunction, type match } from 'path-to-regexp';

// Type definitions for rewrite rules and conditions
export interface RewriteCondition {
	type: 'header' | 'cookie' | 'query' | 'host';
	key: string;
	value?: string | undefined; // Can be a regex string
	valueRegex?: RegExp;
}

export interface RewriteRule {
	source: string;
	destination: string;
	has?: RewriteCondition[];
	missing?: RewriteCondition[];
}

export interface CompiledRewriteRule extends RewriteRule {
	matcher: ReturnType<typeof match>;
	destinationCompiler: PathFunction;
	// Store compiled regexes for 'has'/'missing' value checks if they are regex
	compiledConditions?: {
		has?: RewriteCondition[];
		missing?: RewriteCondition[];
	};
}

export type RouteParams = Record<string, string | string[] | undefined>;
