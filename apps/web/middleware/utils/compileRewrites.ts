import { match, compile } from 'path-to-regexp';
import type {
	RewriteRule,
	CompiledRewriteRule,
	RewriteCondition,
} from './types';

/**
 * Compiles an array of rewrite rules into a more efficient format for matching.
 * @param rules - Array of rewrite rules.
 * @returns Array of compiled rewrite rules.
 */
export function compileRewrites(rules: RewriteRule[]): CompiledRewriteRule[] {
	return rules.map((rule) => {
		const compiledRule: CompiledRewriteRule = {
			...rule,
			matcher: match(rule.source, { decode: decodeURIComponent }),
			destinationCompiler: compile(rule.destination.split('?')[0] ?? '', {
				// Added ?? '' for safety, though split()[0] should be string
				encode: encodeURIComponent,
			}),
			// compiledConditions is not initialized here by default
		};

		if (rule.has && rule.has.length > 0) {
			if (!compiledRule.compiledConditions) {
				compiledRule.compiledConditions = {};
			}
			compiledRule.compiledConditions.has = rule.has.map((condition) => {
				const compiledConditionPart: RewriteCondition = { ...condition };
				if (typeof condition.value === 'string') {
					try {
						compiledConditionPart.valueRegex = new RegExp(condition.value);
					} catch (e) {
						console.warn(
							`Failed to compile regex for condition value: "${condition.value}" in rule source: "${rule.source}". Treating as string.`,
							e,
						);
					}
				}
				return compiledConditionPart;
			});
		}

		if (rule.missing && rule.missing.length > 0) {
			if (!compiledRule.compiledConditions) {
				compiledRule.compiledConditions = {};
			}
			compiledRule.compiledConditions.missing = rule.missing.map(
				(condition) => {
					const compiledConditionPart: RewriteCondition = { ...condition };
					if (typeof condition.value === 'string') {
						try {
							compiledConditionPart.valueRegex = new RegExp(condition.value);
						} catch (e) {
							console.warn(
								`Failed to compile regex for condition value: "${condition.value}" in rule source: "${rule.source}". Treating as string.`,
								e,
							);
						}
					}
					return compiledConditionPart;
				},
			);
		}
		return compiledRule;
	});
}
