import { type NextRequest } from 'next/server';
import type { CompiledRewriteRule, RouteParams } from './types';

/**
 * Checks if the 'has' and 'missing' conditions of a rule are met by the request.
 * @param rule - The compiled rewrite rule.
 * @param request - The NextRequest object.
 * @param pathParams - Parameters extracted from the path match.
 * @returns An object indicating if conditions are met and any params captured from conditions.
 */
export function checkConditions(
	rule: CompiledRewriteRule,
	request: NextRequest,
	pathParams: RouteParams,
): { match: boolean; conditionParams?: RouteParams } {
	const conditionParams: RouteParams = { ...pathParams };

	// Check 'has' conditions - all must match
	if (rule.compiledConditions?.has) {
		for (const condition of rule.compiledConditions.has) {
			let actualValue: string | undefined;

			switch (condition.type) {
				case 'header':
					actualValue = request.headers.get(condition.key) || undefined;
					break;
				case 'cookie':
					actualValue = request.cookies.get(condition.key)?.value || undefined;
					break;
				case 'query':
					actualValue =
						request.nextUrl.searchParams.get(condition.key) || undefined;
					break;
				case 'host':
					actualValue = request.headers.get('host') || undefined;
					break;
				default:
					// Unknown condition type, fail the match
					return { match: false };
			}

			// If condition.value is undefined, we just check for existence
			if (condition.value === undefined) {
				if (actualValue === undefined) {
					return { match: false };
				}
				// If the key exists, capture its value for use in the destination
				conditionParams[condition.key] = actualValue;
			} else {
				// condition.value is defined, check for match
				if (actualValue === undefined) {
					return { match: false };
				}

				if (condition.valueRegex) {
					// Use regex matching
					const regexMatch = condition.valueRegex.exec(actualValue);
					if (!regexMatch) {
						return { match: false };
					}
					// Merge named groups from regex into conditionParams
					if (regexMatch.groups) {
						Object.assign(conditionParams, regexMatch.groups);
					}
				} else {
					// Use exact string matching
					if (actualValue !== condition.value) {
						return { match: false };
					}
				}
			}
		}
	}

	// Check 'missing' conditions - all must NOT match
	if (rule.compiledConditions?.missing) {
		for (const condition of rule.compiledConditions.missing) {
			let actualValue: string | undefined;

			switch (condition.type) {
				case 'header':
					actualValue = request.headers.get(condition.key) || undefined;
					break;
				case 'cookie':
					actualValue = request.cookies.get(condition.key)?.value || undefined;
					break;
				case 'query':
					actualValue =
						request.nextUrl.searchParams.get(condition.key) || undefined;
					break;
				case 'host':
					actualValue = request.headers.get('host') || undefined;
					break;
				default:
					// Unknown condition type, fail the match
					return { match: false };
			}

			// If condition.value is undefined, we check that the key doesn't exist
			if (condition.value === undefined) {
				if (actualValue !== undefined) {
					return { match: false };
				}
			} else {
				// condition.value is defined, check that it doesn't match
				if (actualValue !== undefined) {
					if (condition.valueRegex) {
						// Use regex matching
						const regexMatch = condition.valueRegex.exec(actualValue);
						if (regexMatch) {
							return { match: false }; // Should NOT match for 'missing'
						}
					} else {
						// Use exact string matching
						if (actualValue === condition.value) {
							return { match: false }; // Should NOT match for 'missing'
						}
					}
				}
			}
		}
	}

	return { match: true, conditionParams };
}
