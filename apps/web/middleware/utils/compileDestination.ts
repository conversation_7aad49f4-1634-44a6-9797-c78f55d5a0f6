import { compile } from 'path-to-regexp';
import type { RouteParams } from './types';

/**
 * Applies extracted parameters to a destination string (path and query).
 * @param destination - The destination string from a rewrite rule.
 * @param params - Parameters extracted from the path and conditions.
 * @param originalSearch - The original search string from the request.
 * @returns The resolved URL string (path + query).
 */
export function compileDestination(
	destination: string,
	params: RouteParams,
	originalSearch: string,
): string {
	// Compile path part with path-to-regexp
	const compiledPath = compile(destination, {
		encode: encodeURIComponent,
	})(params);

	// Handle query parameters
	const finalQueryParams = new URLSearchParams(originalSearch);
	const finalQueryString = finalQueryParams.toString();

	return finalQueryString
		? `${compiledPath}?${finalQueryString}`
		: compiledPath;
}
