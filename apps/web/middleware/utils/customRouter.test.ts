import { describe, it, expect } from 'vitest';
import {
	compileRewrites,
	findMatch,
	compileDestination,
	type RewriteRule,
	type RouteParams,
} from './index';
import type { NextRequest } from 'next/server';

// Test data - sample rewrite rules
const testRewrites: RewriteRule[] = [
	{
		source:
			'/:code/:locale([a-z]{2}-[A-Z]{2})/:year(\\d{4})/:month(\\d{2})/:day(\\d{2})/:collectionId*/:slug',
		destination:
			'/:code/content/payload/byAssetName/:locale/:year/:month/:day/:collectionId*/:slug',
	},
	{
		source: '/:code/weather/today/l/:locId',
		destination: '/:code/weather/today/l/:locId',
	},
	{
		source: '/:code/pages/:assetId([0-9a-f]{24})',
		destination: '/:code/pages/byId/en-US/:assetId',
	},
	{
		source: '/:code/:locale([a-z]{2}-[A-Z]{2})/pages/:assetName*',
		destination: '/:code/pages/byAssetName/:locale/:assetName*',
		has: [
			{
				type: 'header',
				key: 'x-custom-header',
				value: 'test-value',
			},
		],
	},
	{
		source: `/:code/news/weather`,
		has: [
			{
				type: 'query',
				key: 'pg',
			},
		],
		destination: `/:code/content/classic/byCollectionId/en-US/:pg/news/weather`,
	},
	{
		source: `/:code/news/weather`,
		destination: `/:code/content/classic/byCollectionId/en-US/1/news/weather`,
	},
	{
		source: '/:code/:collectionId*/news/:articleTitle',
		destination:
			'/:code/content/classic/byAssetName/en-US/:collectionId*/news/:articleTitle',
	},
];

// Mock request object
const createMockRequest = (
	headers: Record<string, string> = {},
	query: Record<string, string> = {},
	cookies: Record<string, string> = {},
): Partial<NextRequest> => ({
	headers: {
		get: (key: string) => headers[key] || null,
		entries: () => Object.entries(headers),
		has: (key: string) => key in headers,
		keys: () => Object.keys(headers),
		values: () => Object.values(headers),
	} as any,
	nextUrl: {
		searchParams: {
			get: (key: string) => query[key] || null,
			entries: () => Object.entries(query),
			has: (key: string) => key in query,
			keys: () => Object.keys(query),
			values: () => Object.values(query),
		},
	} as any,
	cookies: {
		get: (key: string) => (cookies[key] ? { value: cookies[key] } : undefined),
		has: (key: string) => key in cookies,
		getAll: () =>
			Object.entries(cookies).map(([name, value]) => ({ name, value })),
	} as any,
});

describe('Custom Router Utilities', () => {
	const compiledRewrites = compileRewrites(testRewrites);

	it('should compile rewrite rules', () => {
		expect(compiledRewrites).toHaveLength(testRewrites.length);
	});

	it('should match a complex path with multiple parameters', () => {
		const path = '/abc123/en-US/2025/03/27/news/testing';
		const request = createMockRequest();
		const match = findMatch(path, compiledRewrites, request as NextRequest);

		expect(match).not.toBeNull();
		if (match) {
			const result = compileDestination(
				match.rule.destination,
				match.params,
				'',
			);
			expect(result).toBe(
				'/abc123/content/payload/byAssetName/en-US/2025/03/27/news/testing',
			);
			expect(match.params).toEqual({
				code: 'abc123',
				locale: 'en-US',
				year: '2025',
				month: '03',
				day: '27',
				collectionId: ['news'],
				slug: 'testing',
			});
		}
	});

	it('should match the weather today path', () => {
		const path = '/abc123/weather/today/l/USGA0028';
		const request = createMockRequest();
		const match = findMatch(path, compiledRewrites, request as NextRequest);

		expect(match).not.toBeNull();
		if (match) {
			const result = compileDestination(
				match.rule.destination,
				match.params,
				'',
			);
			expect(result).toBe('/abc123/weather/today/l/USGA0028');
			expect(match.params).toEqual({ code: 'abc123', locId: 'USGA0028' });
		}
	});

	it('should match an asset ID path', () => {
		const path = '/abc123/pages/507f1f77bcf86cd799439011';
		const request = createMockRequest();
		const match = findMatch(path, compiledRewrites, request as NextRequest);

		expect(match).not.toBeNull();
		if (match) {
			const result = compileDestination(
				match.rule.destination,
				match.params,
				'',
			);
			expect(result).toBe('/abc123/pages/byId/en-US/507f1f77bcf86cd799439011');
			expect(match.params).toEqual({
				code: 'abc123',
				assetId: '507f1f77bcf86cd799439011',
			});
		}
	});

	it('should match a path with a "has" condition when the header is present', () => {
		const path = '/abc123/en-US/pages/some-page';
		const request = createMockRequest({ 'x-custom-header': 'test-value' });
		const match = findMatch(path, compiledRewrites, request as NextRequest);

		expect(match).not.toBeNull();
		if (match) {
			const result = compileDestination(
				match.rule.destination,
				match.params,
				'',
			);
			expect(result).toBe('/abc123/pages/byAssetName/en-US/some-page');
			expect(match.params).toEqual({
				code: 'abc123',
				locale: 'en-US',
				assetName: ['some-page'],
			});
		}
	});

	it('should NOT match a path with a "has" condition when the header is missing', () => {
		const path = '/abc123/en-US/pages/some-page';
		const request = createMockRequest(); // No header
		const match = findMatch(path, compiledRewrites, request as NextRequest);

		expect(match).toBeNull();
	});

	it("should not match a path that doesn't correspond to any rule", () => {
		const path = '/abc123/some/random/path';
		const request = createMockRequest();
		const match = findMatch(path, compiledRewrites, request as NextRequest);

		expect(match).toBeNull();
	});

	it('should match a paginated news weather path when pg query is present', () => {
		const path = '/abc123/news/weather';
		const request = createMockRequest({}, { pg: '2' });
		const match = findMatch(path, compiledRewrites, request as NextRequest);

		expect(match).not.toBeNull();
		if (match) {
			expect(match.params).toEqual({ code: 'abc123', pg: '2' });
			const result = compileDestination(
				match.rule.destination,
				match.params,
				'?pg=2',
			);
			expect(result).toBe(
				'/abc123/content/classic/byCollectionId/en-US/2/news/weather?pg=2',
			);
		}
	});

	it('should match a non-paginated news weather path when pg query is missing', () => {
		const path = '/abc123/news/weather';
		const request = createMockRequest(); // No query params
		const match = findMatch(path, compiledRewrites, request as NextRequest);

		expect(match).not.toBeNull();
		if (match) {
			expect(match.params).toEqual({ code: 'abc123' });
			const result = compileDestination(
				match.rule.destination,
				match.params,
				'',
			);
			expect(result).toBe(
				'/abc123/content/classic/byCollectionId/en-US/1/news/weather',
			);
		}
	});

	it('should match a classic article path', () => {
		const path =
			'/abc123/health/respiratory/news/2025-02-12-common-winter-breathing-asthma-problems-children';
		const request = createMockRequest();
		const match = findMatch(path, compiledRewrites, request as NextRequest);

		expect(match).not.toBeNull();
		if (match) {
			const result = compileDestination(
				match.rule.destination,
				match.params,
				'',
			);
			expect(result).toBe(
				'/abc123/content/classic/byAssetName/en-US/health/respiratory/news/2025-02-12-common-winter-breathing-asthma-problems-children',
			);
			expect(match.params).toEqual({
				code: 'abc123',
				collectionId: ['health', 'respiratory'],
				articleTitle:
					'2025-02-12-common-winter-breathing-asthma-problems-children',
			});
		}
	});
});
