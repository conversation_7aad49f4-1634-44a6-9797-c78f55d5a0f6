import { type NextRequest, NextResponse } from 'next/server';
import { getBloomFilterFromEdgeConfig } from '@/utils/edgeConfig';
import { hasLength } from '@/utils/hasLength';
import { Redirect } from '../../payload-types';

/**
 * Check if the current path has a redirect in the database
 * If a redirect is found, return a redirect response
 *
 * @param request - The NextRequest object
 * @returns Promise<NextResponse | null> - Redirect response or null if no redirect found
 */
export async function checkForRedirect(
	request: NextRequest,
): Promise<NextResponse | null> {
	const { pathname } = request.nextUrl;

	// Skip redirect check for common non-redirected paths
	if (pathname.endsWith('.svg') || pathname.endsWith('.js')) {
		return null;
	}

	try {
		const bloomFilter = await getBloomFilterFromEdgeConfig();

		// If bloom filter exists and path is not in filter, skip DB check
		if (bloomFilter && !bloomFilter.has(pathname)) {
			console.debug(
				'[checkForRedirect]',
				`'${pathname}' is not in bloomFilter, skipping redirect`,
			);
			return null;
		}

		console.debug(
			'[checkForRedirect]',
			`'${pathname}' is in bloomFilter, proceeding to redirect`,
		);

		const payloadApiUrl = new URL(
			'/api/payload/redirects',
			request.nextUrl.origin,
		);
		payloadApiUrl.searchParams.set('where[source][equals]', pathname);
		payloadApiUrl.searchParams.set('limit', '1');

		const redirectResponse = await fetch(payloadApiUrl.toString(), {
			headers: {
				'Content-Type': 'application/json',
			},
		});

		if (!redirectResponse.ok) {
			console.error(
				`Redirect API returned ${redirectResponse.status}: ${redirectResponse.statusText}`,
			);
			return null;
		}

		const redirectData = await redirectResponse.json();

		if (redirectData && hasLength(redirectData.docs)) {
			const redirect = redirectData.docs[0] as Redirect;
			const { destination, permanent } = redirect;
			const statusCode = permanent ? 301 : 302;

			return NextResponse.redirect(
				new URL(destination, request.url),
				statusCode,
			);
		}

		return null;
	} catch (error: unknown) {
		console.error('Error checking for redirect', (error as Error).message);

		return null;
	}
}
