import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { NextRequest, NextResponse } from 'next/server';
import { checkForRedirect } from './checkForRedirect';
import * as edgeConfigModule from '@/utils/edgeConfig';
import * as hasLengthModule from '@/utils/hasLength';

// Mock the dependencies
vi.mock('@/utils/edgeConfig');
vi.mock('@/utils/hasLength');

// Mock global fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock console methods to avoid noise in tests
const mockConsoleDebug = vi.spyOn(console, 'debug');
const mockConsoleError = vi.spyOn(console, 'error');

// Helper function to create mock NextRequest
const createMockRequest = (
	pathname: string,
	origin = 'https://example.com',
): NextRequest => {
	const url = new URL(pathname, origin);
	return {
		nextUrl: url,
		url: url.toString(),
	} as NextRequest;
};

// Mock bloom filter
const mockBloomFilter = {
	has: vi.fn(),
};

describe('checkForRedirect', () => {
	beforeEach(() => {
		vi.clearAllMocks();
		mockFetch.mockClear();
		mockConsoleDebug.mockClear();
		mockConsoleError.mockClear();
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('Early returns for non-redirectable paths', () => {
		it('should return null for SVG files', async () => {
			const request = createMockRequest('/path/to/image.svg');
			const result = await checkForRedirect(request);

			expect(result).toBeNull();
			expect(mockFetch).not.toHaveBeenCalled();
		});

		it('should return null for JS files', async () => {
			const request = createMockRequest('/path/to/script.js');
			const result = await checkForRedirect(request);

			expect(result).toBeNull();
			expect(mockFetch).not.toHaveBeenCalled();
		});
	});

	describe('Bloom filter optimization', () => {
		it('should skip DB check when bloom filter exists and path is not in filter', async () => {
			const request = createMockRequest('/test-path');

			// Mock bloom filter that doesn't contain the path
			mockBloomFilter.has.mockReturnValue(false);
			vi.mocked(
				edgeConfigModule.getBloomFilterFromEdgeConfig,
			).mockResolvedValue(mockBloomFilter as any);

			const result = await checkForRedirect(request);

			expect(result).toBeNull();
			expect(mockBloomFilter.has).toHaveBeenCalledWith('/test-path');
			expect(mockFetch).not.toHaveBeenCalled();
		});

		it('should proceed to DB check when bloom filter exists and path is in filter', async () => {
			const request = createMockRequest('/test-path');

			// Mock bloom filter that contains the path
			mockBloomFilter.has.mockReturnValue(true);
			vi.mocked(
				edgeConfigModule.getBloomFilterFromEdgeConfig,
			).mockResolvedValue(mockBloomFilter as any);

			// Mock API response with no redirects
			mockFetch.mockResolvedValue({
				ok: true,
				json: () => Promise.resolve({ docs: [] }),
			} as Response);

			vi.mocked(hasLengthModule.hasLength).mockReturnValue(false);

			const result = await checkForRedirect(request);

			expect(result).toBeNull();
			expect(mockBloomFilter.has).toHaveBeenCalledWith('/test-path');
			expect(mockFetch).toHaveBeenCalled();
		});

		it('should proceed to DB check when bloom filter is null', async () => {
			const request = createMockRequest('/test-path');

			// Mock no bloom filter
			vi.mocked(
				edgeConfigModule.getBloomFilterFromEdgeConfig,
			).mockResolvedValue(null);

			// Mock API response with no redirects
			mockFetch.mockResolvedValue({
				ok: true,
				json: () => Promise.resolve({ docs: [] }),
			} as Response);

			vi.mocked(hasLengthModule.hasLength).mockReturnValue(false);

			const result = await checkForRedirect(request);

			expect(result).toBeNull();
			expect(mockFetch).toHaveBeenCalled();
		});
	});

	describe('API interaction', () => {
		beforeEach(() => {
			// Mock bloom filter that allows proceeding to API
			mockBloomFilter.has.mockReturnValue(true);
			vi.mocked(
				edgeConfigModule.getBloomFilterFromEdgeConfig,
			).mockResolvedValue(mockBloomFilter as any);
		});

		it('should construct correct API URL with query parameters', async () => {
			const request = createMockRequest('/test-path', 'https://example.com');

			mockFetch.mockResolvedValue({
				ok: true,
				json: () => Promise.resolve({ docs: [] }),
			} as Response);

			vi.mocked(hasLengthModule.hasLength).mockReturnValue(false);

			await checkForRedirect(request);

			expect(mockFetch).toHaveBeenCalledWith(
				'https://example.com/api/payload/redirects?where%5Bsource%5D%5Bequals%5D=%2Ftest-path&limit=1',
				{
					headers: {
						'Content-Type': 'application/json',
					},
				},
			);
		});

		it('should return null when API response is not ok', async () => {
			const request = createMockRequest('/test-path');

			mockFetch.mockResolvedValue({
				ok: false,
				status: 500,
				statusText: 'Internal Server Error',
			} as Response);

			const result = await checkForRedirect(request);

			expect(result).toBeNull();
		});

		it('should return null when no redirects are found', async () => {
			const request = createMockRequest('/test-path');

			mockFetch.mockResolvedValue({
				ok: true,
				json: () => Promise.resolve({ docs: [] }),
			} as Response);

			vi.mocked(hasLengthModule.hasLength).mockReturnValue(false);

			const result = await checkForRedirect(request);

			expect(result).toBeNull();
		});
	});

	describe('Redirect responses', () => {
		beforeEach(() => {
			// Mock bloom filter that allows proceeding to API
			mockBloomFilter.has.mockReturnValue(true);
			vi.mocked(
				edgeConfigModule.getBloomFilterFromEdgeConfig,
			).mockResolvedValue(mockBloomFilter as any);
		});

		it('should return permanent redirect (301) when permanent is true', async () => {
			const request = createMockRequest('/old-path', 'https://example.com');

			const mockRedirect = {
				destination: '/new-path',
				permanent: true,
			};

			mockFetch.mockResolvedValue({
				ok: true,
				json: () => Promise.resolve({ docs: [mockRedirect] }),
			} as Response);

			vi.mocked(hasLengthModule.hasLength).mockReturnValue(true);

			// Mock NextResponse.redirect
			const mockRedirectResponse = { type: 'redirect', status: 301 } as any;
			const redirectSpy = vi
				.spyOn(NextResponse, 'redirect')
				.mockReturnValue(mockRedirectResponse);

			const result = await checkForRedirect(request);

			expect(result).toBe(mockRedirectResponse);
			expect(redirectSpy).toHaveBeenCalledWith(
				new URL('/new-path', 'https://example.com'),
				301,
			);
		});

		it('should return temporary redirect (302) when permanent is false', async () => {
			const request = createMockRequest('/old-path', 'https://example.com');

			const mockRedirect = {
				destination: '/new-path',
				permanent: false,
			};

			mockFetch.mockResolvedValue({
				ok: true,
				json: () => Promise.resolve({ docs: [mockRedirect] }),
			} as Response);

			vi.mocked(hasLengthModule.hasLength).mockReturnValue(true);

			// Mock NextResponse.redirect
			const mockRedirectResponse = { type: 'redirect', status: 302 } as any;
			const redirectSpy = vi
				.spyOn(NextResponse, 'redirect')
				.mockReturnValue(mockRedirectResponse);

			const result = await checkForRedirect(request);

			expect(result).toBe(mockRedirectResponse);
			expect(redirectSpy).toHaveBeenCalledWith(
				new URL('/new-path', 'https://example.com'),
				302,
			);
		});

		it('should handle absolute destination URLs', async () => {
			const request = createMockRequest('/old-path', 'https://example.com');

			const mockRedirect = {
				destination: 'https://external.com/new-path',
				permanent: true,
			};

			mockFetch.mockResolvedValue({
				ok: true,
				json: () => Promise.resolve({ docs: [mockRedirect] }),
			} as Response);

			vi.mocked(hasLengthModule.hasLength).mockReturnValue(true);

			// Mock NextResponse.redirect
			const mockRedirectResponse = { type: 'redirect', status: 301 } as any;
			const redirectSpy = vi
				.spyOn(NextResponse, 'redirect')
				.mockReturnValue(mockRedirectResponse);

			const result = await checkForRedirect(request);

			expect(result).toBe(mockRedirectResponse);
			expect(redirectSpy).toHaveBeenCalledWith(
				new URL('https://external.com/new-path', 'https://example.com'),
				301,
			);
		});
	});

	describe('Error handling', () => {
		beforeEach(() => {
			// Mock bloom filter that allows proceeding to API
			mockBloomFilter.has.mockReturnValue(true);
			vi.mocked(
				edgeConfigModule.getBloomFilterFromEdgeConfig,
			).mockResolvedValue(mockBloomFilter as any);
		});

		it('should handle fetch errors gracefully', async () => {
			const request = createMockRequest('/test-path');

			const fetchError = new Error('Network error');
			mockFetch.mockRejectedValue(fetchError);

			const result = await checkForRedirect(request);

			expect(result).toBeNull();
		});

		it('should handle JSON parsing errors gracefully', async () => {
			const request = createMockRequest('/test-path');

			mockFetch.mockResolvedValue({
				ok: true,
				json: () => Promise.reject(new Error('Invalid JSON')),
			} as Response);

			const result = await checkForRedirect(request);

			expect(result).toBeNull();
		});

		it('should handle bloom filter errors gracefully', async () => {
			const request = createMockRequest('/test-path');

			vi.mocked(
				edgeConfigModule.getBloomFilterFromEdgeConfig,
			).mockRejectedValue(new Error('Bloom filter error'));

			const result = await checkForRedirect(request);

			expect(result).toBeNull();
		});

		it('should handle malformed redirect data gracefully', async () => {
			const request = createMockRequest('/test-path');

			mockFetch.mockResolvedValue({
				ok: true,
				json: () =>
					Promise.resolve({
						docs: [
							{
								/* missing required fields */
							},
						],
					}),
			} as Response);

			vi.mocked(hasLengthModule.hasLength).mockReturnValue(true);

			// This should not throw, but may result in undefined behavior
			// The function should handle this gracefully
			const result = await checkForRedirect(request);

			// The function should either return null or handle the malformed data
			expect(result).toBeDefined();
		});
	});

	describe('Edge cases', () => {
		it('should handle empty pathname', async () => {
			const request = createMockRequest('');

			vi.mocked(
				edgeConfigModule.getBloomFilterFromEdgeConfig,
			).mockResolvedValue(null);
			mockFetch.mockResolvedValue({
				ok: true,
				json: () => Promise.resolve({ docs: [] }),
			} as Response);

			vi.mocked(hasLengthModule.hasLength).mockReturnValue(false);

			const result = await checkForRedirect(request);

			expect(result).toBeNull();
		});

		it('should handle root path', async () => {
			const request = createMockRequest('/');

			vi.mocked(
				edgeConfigModule.getBloomFilterFromEdgeConfig,
			).mockResolvedValue(null);
			mockFetch.mockResolvedValue({
				ok: true,
				json: () => Promise.resolve({ docs: [] }),
			} as Response);

			vi.mocked(hasLengthModule.hasLength).mockReturnValue(false);

			const result = await checkForRedirect(request);

			expect(result).toBeNull();
			expect(mockFetch).toHaveBeenCalledWith(
				expect.stringContaining('where%5Bsource%5D%5Bequals%5D=%2F'),
				expect.any(Object),
			);
		});

		it('should handle paths with special characters', async () => {
			const request = createMockRequest('/path with spaces & symbols!');

			vi.mocked(
				edgeConfigModule.getBloomFilterFromEdgeConfig,
			).mockResolvedValue(null);
			mockFetch.mockResolvedValue({
				ok: true,
				json: () => Promise.resolve({ docs: [] }),
			} as Response);

			vi.mocked(hasLengthModule.hasLength).mockReturnValue(false);

			const result = await checkForRedirect(request);

			expect(result).toBeNull();
			expect(mockFetch).toHaveBeenCalledWith(
				expect.stringContaining(
					'where%5Bsource%5D%5Bequals%5D=%2Fpath%2520with%2520spaces%2520%26%2520symbols%21',
				),
				expect.any(Object),
			);
		});
	});
});
