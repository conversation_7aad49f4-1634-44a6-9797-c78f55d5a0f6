import pkg from 'bloom-filters';
const { ScalableBloomFilter } = pkg;
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { parse } from 'path-to-regexp';
import { afterFiles } from '../rewrites/index.mjs';

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const BLOOM_FILTER_EXPORT_PATH = path.resolve(
	__dirname,
	'../dist/bloom-filter.json',
);

async function generateBloomFilter() {
	console.log('Generating Bloom filter from rewrite rules...');

	const estimatedItemCount = afterFiles.length;
	const falsePositiveRate = 0.001;

	const filter = ScalableBloomFilter.create(
		estimatedItemCount,
		falsePositiveRate,
	);

	for (const rule of afterFiles) {
		if (typeof rule === 'object' && rule.source) {
			const tokens = parse(rule.source);
			for (const token of tokens) {
				if (typeof token === 'string') {
					const staticSegment = token.replace(/\//g, '');
					if (staticSegment) {
						filter.add(staticSegment);
					}
				}
			}
		}
	}

	const exportedFilter = filter.saveAsJSON();

	try {
		await fs.writeFile(
			BLOOM_FILTER_EXPORT_PATH,
			JSON.stringify(exportedFilter, null, 2),
			'utf-8',
		);
		console.log(
			`Bloom filter successfully generated and saved to ${BLOOM_FILTER_EXPORT_PATH}`,
		);
	} catch (error) {
		console.error('Failed to write Bloom filter to file:', error);
		process.exit(1);
	}
}

generateBloomFilter();
