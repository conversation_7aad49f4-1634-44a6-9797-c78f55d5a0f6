import { flag } from 'flags/next';

/**
 * Gets the partner value from the "par" search parameter.
 */
export const partnerParamFlag = flag<string>({
	key: 'partner-param-flag',
	decide: ({ headers }) => {
		const searchParamsHeader = headers.get('x-search-params') ?? '';
		const searchParams = new URLSearchParams(searchParamsHeader);
		return searchParams.get('par') ?? '';
	},
});

/**
 * Gets the device class from the request.
 * Device class is sourced from a previously-set x-device-class header, or from
 * the `deviceClass` query param (for development/debugging).
 */
export const deviceClassFlag = flag<string>({
	key: 'device-class-flag',
	decide: ({ headers }) => {
		// get device class from query params
		const searchParamsHeader = headers.get('x-search-params') ?? '';
		const searchParams = new URLSearchParams(searchParamsHeader);
		const deviceClassParam = searchParams.get('deviceClass');

		// get deviceClass from header set by middleware
		const deviceClass = headers.get('x-device-class');

		// prefer query param over header, for dev/debugging
		return deviceClassParam || deviceClass || '';
	},
});

/**
 * Gets the privacy regime code, set by the middleware as a request header.
 */
export const privacyRegimeCodeFlag = flag<string>({
	key: 'privacy-regime-flag',
	decide: ({ headers }) => {
		const regimeCode = headers.get('x-twc-privacy');
		return regimeCode || '';
	},
});
