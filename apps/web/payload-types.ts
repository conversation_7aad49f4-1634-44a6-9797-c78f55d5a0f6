/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    locations: Location;
    'content-queries': ContentQuery;
    tenants: Tenant;
    tags: Tag;
    users: User;
    images: Image;
    pages: Page;
    articles: Article;
    videos: Video;
    liveblogs: Liveblog;
    'context-parameters': ContextParameter;
    'ad-slots': AdSlot;
    'third-party-config': ThirdPartyConfig;
    redirects: Redirect;
    'payload-jobs': PayloadJob;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {
    users: {
      'authorData.authoredContent': 'articles';
    };
  };
  collectionsSelect: {
    locations: LocationsSelect<false> | LocationsSelect<true>;
    'content-queries': ContentQueriesSelect<false> | ContentQueriesSelect<true>;
    tenants: TenantsSelect<false> | TenantsSelect<true>;
    tags: TagsSelect<false> | TagsSelect<true>;
    users: UsersSelect<false> | UsersSelect<true>;
    images: ImagesSelect<false> | ImagesSelect<true>;
    pages: PagesSelect<false> | PagesSelect<true>;
    articles: ArticlesSelect<false> | ArticlesSelect<true>;
    videos: VideosSelect<false> | VideosSelect<true>;
    liveblogs: LiveblogsSelect<false> | LiveblogsSelect<true>;
    'context-parameters': ContextParametersSelect<false> | ContextParametersSelect<true>;
    'ad-slots': AdSlotsSelect<false> | AdSlotsSelect<true>;
    'third-party-config': ThirdPartyConfigSelect<false> | ThirdPartyConfigSelect<true>;
    redirects: RedirectsSelect<false> | RedirectsSelect<true>;
    'payload-jobs': PayloadJobsSelect<false> | PayloadJobsSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: string;
  };
  globals: {
    'site-mode': SiteMode;
  };
  globalsSelect: {
    'site-mode': SiteModeSelect<false> | SiteModeSelect<true>;
  };
  locale: 'de-DE' | 'en-US' | 'es-US' | 'fr-FR' | 'gls';
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: {
      getCMAssetByID: TaskGetCMAssetByID;
      getContentByTags: TaskGetContentByTags;
      getContentByIds: TaskGetContentByIds;
      refreshContentQueries: TaskRefreshContentQueries;
      updateAdMetrics: TaskUpdateAdMetrics;
      schedulePublish: TaskSchedulePublish;
      inline: {
        input: unknown;
        output: unknown;
      };
    };
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * Location data store, for canonical and search
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "locations".
 */
export interface Location {
  id: string;
  /**
   * The common display name for this location
   */
  displayName: string;
  /**
   * Type of location - currently supports POI and Airport
   */
  locationType: 'poi' | 'airport';
  locationData?: LocationData;
  /**
   * Canonical ID stored as a partial path for this location on weather.com
   */
  canonicalID?: string | null;
  /**
   * Hash of geocode and location type from SUN
   */
  placeID?: string | null;
  /**
   * Legacy TWC location identifier corresponding to a unique location record.  This location type is provided to ensure compatibility with legacy queries.
   */
  locID?: string | null;
  city?: string | null;
  /**
   * State/province/region code (e.g., 'AL', 'CA')
   */
  adminDistrictCode?: string | null;
  /**
   * Full state/province/region name
   */
  adminDistrict?: string | null;
  country?: string | null;
  /**
   * ISO 3166-1 alpha-2 country code
   */
  countryCode?: string | null;
  /**
   * Geographic coordinates (latitude, longitude)
   *
   * @minItems 2
   * @maxItems 2
   */
  locationPoint: [number, number];
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "locationData".
 */
export interface LocationData {
  poi?: Poi;
  airport?: Airport;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "poi".
 */
export interface Poi {
  poiType?:
    | (
        | 'amusement park'
        | 'beach'
        | 'campground'
        | 'fair ground'
        | 'hiking trail'
        | 'motorsport racing track'
        | 'national park'
        | 'ski'
        | 'sports venue'
        | 'stadium'
        | 'state park'
        | 'theme park'
        | 'urban park'
      )
    | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "airport".
 */
export interface Airport {
  /**
   * 4-digit airport code
   */
  icaoCode?: string | null;
  /**
   * 3-digit airport code
   */
  iataCode?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "content-queries".
 */
export interface ContentQuery {
  id: string;
  name: string;
  queries?:
    | {
        queryType: 'data' | 'id' | 'kalliope';
        tags?: (string | Tag)[] | null;
        /**
         * How to combine multiple tags: AND requires all tags, OR requires any tag
         */
        tagsOperator?: ('AND' | 'OR') | null;
        category?: (string | Tag)[] | null;
        /**
         * How to combine multiple categories: OR requires any category (Note: Articles can only have one category)
         */
        categoryOperator?: 'OR' | null;
        topic?: (string | Tag)[] | null;
        /**
         * How to combine multiple topics: OR requires any topic (Note: Articles can only have one topic)
         */
        topicOperator?: 'OR' | null;
        /**
         * How to combine different field groups (tags, categories, topics): AND requires all conditions, OR requires any condition
         */
        groupsOperator?: ('AND' | 'OR') | null;
        syncStatus?: ('pending' | 'in-progress' | 'completed' | 'failed') | null;
        lastSynced?: string | null;
        contentId?: (string | Article)[] | null;
        kalliopeCMQSID?: string | null;
        _locked: boolean;
        _lockedByTaskID?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Content merged from various sources
   */
  mergedContent?:
    | {
        id: string;
        source: 'drupal' | 'payload';
        /**
         * Type of content
         */
        contentType: 'article' | 'image' | 'video';
        title: string;
        overrideTitle?: string | null;
        description?: string | null;
        overrideDescription?: string | null;
        thumbnail: string;
        overrideThumbnail?: string | null;
        url: string;
        pinned?: boolean | null;
        /**
         * ID of the query that sourced this content
         */
        sourceQueryID?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tags".
 */
export interface Tag {
  id: string;
  name: string;
  slug?: string | null;
  slugLock?: boolean | null;
  type:
    | 'adMetrics'
    | 'adZones'
    | 'analyticsTag'
    | 'categoryTags'
    | 'channel'
    | 'contentTags'
    | 'entitlement'
    | 'iabTags'
    | 'locationTags'
    | 'region'
    | 'state';
  /**
   * Full hierarchical path (auto-generated)
   */
  fullPath?: string | null;
  parent?: (string | null) | Tag;
  /**
   * Ad metric associated with this category
   */
  adMetric?: (string | null) | Tag;
  /**
   * Select one or more versions that apply to this IAB Tag
   */
  versions?: ('v1' | 'v2' | 'v3')[] | null;
  /**
   * Select IAB Tags associated with this Analytics Tag
   */
  iabTags?: (string | Tag)[] | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "articles".
 */
export interface Article {
  id: string;
  tenant?: (string | null) | Tenant;
  title: string;
  subHeadline?: string | null;
  category: {
    relationTo: 'tags';
    value: string | Tag;
  };
  authors?: (string | User)[] | null;
  externalAuthors?: string | null;
  /**
   * Can be either rich text or plain text
   */
  partnerByline: {
    type: 'richText' | 'text';
    richText?: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    } | null;
    text?: string | null;
  };
  headerLayout: 'default' | 'noAuthorImages';
  featuredImage: string | Image;
  topic?: {
    relationTo: 'tags';
    value: string | Tag;
  } | null;
  content: {
    body: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    };
  };
  seo?: {
    /**
     * Used by partners to point to their external URL (e.g. https://example.com)
     */
    canonicalUrl?: string | null;
    title?: string | null;
    description?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (string | null) | Image;
  };
  coreMetadata?: {
    /**
     * Select entitlements for the content.
     */
    entitlements?:
      | ('platform-all-free' | 'platform-all-premium' | 'platform-all-standard' | 'platform-all-freemium')[]
      | null;
    /**
     * This field is automatically populated based on the category, but can be overridden if unlocked
     */
    adMetric?: (string | null) | Tag;
    adMetricLock?: boolean | null;
    createdBy?: string | null;
    updatedBy?: string | null;
  };
  channelOverrides?: {
    /**
     * Channel headlines override the standard article headline
     */
    channelHeadlines?:
      | {
          channel: string | Tag;
          headline: string;
          id?: string | null;
        }[]
      | null;
  };
  tags?: (string | Tag)[] | null;
  assetName?: string | null;
  assetNameLock?: boolean | null;
  publishDate?: string | null;
  publishDateLock?: boolean | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tenants".
 */
export interface Tenant {
  id: string;
  name: string;
  /**
   * Used for domain-based tenant handling
   */
  domain?: string | null;
  /**
   * Used for url paths, example: /tenant-slug/page-slug
   */
  slug: string;
  /**
   * If checked, logging in is not required to read. Useful for building public pages.
   */
  allowPublicRead?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: string;
  role: ('admin' | 'web-developer' | 'editor' | 'authenticated')[];
  username?: string | null;
  tenants?:
    | {
        tenant: string | Tenant;
        roles: ('tenant-admin' | 'tenant-viewer')[];
        id?: string | null;
      }[]
    | null;
  /**
   * Okta authentication metadata (automatically managed)
   */
  _okta?: {
    /**
     * Unique Okta user identifier
     */
    uid?: string | null;
    /**
     * Token expiration timestamp
     */
    exp?: number | null;
    /**
     * Okta instance that issued the token
     */
    iss?: string | null;
    /**
     * Token creation timestamp
     */
    iat?: number | null;
    /**
     * OAuth client identifier
     */
    cid?: string | null;
  };
  isAuthor?: boolean | null;
  authorData?: {
    firstName?: string | null;
    lastName?: string | null;
    bio?: string | null;
    /**
     * Please enter in a valid bio url. https://weather.com/bios/my-bio-url
     */
    bioUrl?: string | null;
    profilePicture?: (string | null) | Image;
    socialMediaProfiles?:
      | {
          platform: string;
          url: string;
          id?: string | null;
        }[]
      | null;
    authoredContent?: {
      docs?: (string | Article)[];
      hasNextPage?: boolean;
      totalDocs?: number;
    };
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "images".
 */
export interface Image {
  id: string;
  tenant?: (string | null) | Tenant;
  seo: Seo;
  tags?: (string | Tag)[] | null;
  prefix?: string | null;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    small?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    large?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "seo".
 */
export interface Seo {
  altText: string;
  caption: string;
  /**
   * Attribution for the image (e.g., photographer, source)
   */
  credit?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages".
 */
export interface Page {
  id: string;
  tenant?: (string | null) | Tenant;
  title: string;
  assetName: string;
  assetNameLock?: boolean | null;
  variantName?: string | null;
  createdBy?: string | null;
  updatedBy?: string | null;
  content: {
    layout: {
      region: 'main' | 'sidebar';
      blocks?:
        | (
            | MorningBriefBlockConfig
            | DailyForecastBlockConfig
            | CurrentConditionsBlockConfig
            | AdBlockConfig
            | ImageBlockConfig
            | VideoBlockConfig
            | ContentMediaBlockConfig
            | LiveblogEntriesBlockConfig
            | CTABlockConfig
            | SlideshowBlockConfig
            | PromoDriverBlock
          )[]
        | null;
      /**
       * Override or extend page context parameters for this layout
       */
      contextParameterOverrides?: {
        /**
         * Enable context parameter overrides for this layout
         */
        enabled?: boolean | null;
        /**
         * Configure context parameters for this layout. Multiple values for the same parameter create an OR relationship.
         */
        parameters?: {
          /**
           * Value(s) for deviceClass parameter. Multiple selections create an OR relationship.
           */
          deviceClass?: ('desktop' | 'mobile')[] | null;
          /**
           * Value(s) for partner parameter. Multiple selections create an OR relationship.
           */
          partner?: ('samsung' | 'oppo' | 'apple')[] | null;
          /**
           * Value(s) for weatherMode parameter. Multiple selections create an OR relationship.
           */
          weatherMode?: ('active' | 'normal')[] | null;
          /**
           * Value(s) for siteMode parameter. Multiple selections create an OR relationship.
           */
          siteMode?: ('normal' | 'severe' | 'hybrid')[] | null;
          /**
           * Value(s) for privacyRegime parameter. Multiple selections create an OR relationship.
           */
          privacyRegime?:
            | (
                | 'ca'
                | 'usa'
                | 'usa-ccpa'
                | 'usa-co'
                | 'usa-ct'
                | 'usa-va'
                | 'ca-qc'
                | 'gdpr'
                | 'latam-co'
                | 'latam-do'
                | 'latam-pe'
                | 'lgpd'
                | 'pipl'
                | 'tr-kvkk'
              )[]
            | null;
        };
        /**
         * Determine when this layout should be visible
         */
        visibilityRule?: ('show_always' | 'show_matching' | 'hide_matching') | null;
      };
      id?: string | null;
    }[];
  };
  seo?: {
    title?: string | null;
    description?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (string | null) | Image;
  };
  adConfig?: {
    /**
     * Configure ad settings for this page
     */
    adsData?: {
      /**
       * The ad network code for this page
       */
      networkCode?: string | null;
      /**
       * The ad zone for this page
       */
      adZone?: string | null;
      /**
       * The ad engine for this page
       */
      engine?: string | null;
      /**
       * JSON configuration for video preroll bidders
       */
      videoPrerollBidders?:
        | {
            [k: string]: unknown;
          }
        | unknown[]
        | string
        | number
        | boolean
        | null;
      /**
       * Override or extend page context parameters for ads data
       */
      contextParameterOverrides?: {
        /**
         * Enable context parameter overrides for ads data
         */
        enabled?: boolean | null;
        /**
         * Configure context parameters for ads data. Multiple values for the same parameter create an OR relationship.
         */
        parameters?: {
          /**
           * Value(s) for deviceClass parameter. Multiple selections create an OR relationship.
           */
          deviceClass?: ('desktop' | 'mobile')[] | null;
          /**
           * Value(s) for partner parameter. Multiple selections create an OR relationship.
           */
          partner?: ('samsung' | 'oppo' | 'apple')[] | null;
          /**
           * Value(s) for weatherMode parameter. Multiple selections create an OR relationship.
           */
          weatherMode?: ('active' | 'normal')[] | null;
          /**
           * Value(s) for siteMode parameter. Multiple selections create an OR relationship.
           */
          siteMode?: ('normal' | 'severe' | 'hybrid')[] | null;
          /**
           * Value(s) for privacyRegime parameter. Multiple selections create an OR relationship.
           */
          privacyRegime?:
            | (
                | 'ca'
                | 'usa'
                | 'usa-ccpa'
                | 'usa-co'
                | 'usa-ct'
                | 'usa-va'
                | 'ca-qc'
                | 'gdpr'
                | 'latam-co'
                | 'latam-do'
                | 'latam-pe'
                | 'lgpd'
                | 'pipl'
                | 'tr-kvkk'
              )[]
            | null;
        };
        /**
         * Determine when these ads should be visible
         */
        visibilityRule?: ('show_always' | 'show_matching' | 'hide_matching') | null;
      };
    };
    /**
     * Add multiple third-party ad configurations for this page
     */
    thirdpartyConfigs?:
      | {
          /**
           * Select a third-party ad configuration
           */
          thirdPartyConfig: string | ThirdPartyConfig;
          /**
           * Override or extend page context parameters for this third-party config
           */
          contextParameterOverrides?: {
            /**
             * Enable context parameter overrides for this third-party config
             */
            enabled?: boolean | null;
            /**
             * Configure context parameters for this third-party config. Multiple values for the same parameter create an OR relationship.
             */
            parameters?: {
              /**
               * Value(s) for deviceClass parameter. Multiple selections create an OR relationship.
               */
              deviceClass?: ('desktop' | 'mobile')[] | null;
              /**
               * Value(s) for partner parameter. Multiple selections create an OR relationship.
               */
              partner?: ('samsung' | 'oppo' | 'apple')[] | null;
              /**
               * Value(s) for weatherMode parameter. Multiple selections create an OR relationship.
               */
              weatherMode?: ('active' | 'normal')[] | null;
              /**
               * Value(s) for siteMode parameter. Multiple selections create an OR relationship.
               */
              siteMode?: ('normal' | 'severe' | 'hybrid')[] | null;
              /**
               * Value(s) for privacyRegime parameter. Multiple selections create an OR relationship.
               */
              privacyRegime?:
                | (
                    | 'ca'
                    | 'usa'
                    | 'usa-ccpa'
                    | 'usa-co'
                    | 'usa-ct'
                    | 'usa-va'
                    | 'ca-qc'
                    | 'gdpr'
                    | 'latam-co'
                    | 'latam-do'
                    | 'latam-pe'
                    | 'lgpd'
                    | 'pipl'
                    | 'tr-kvkk'
                  )[]
                | null;
            };
            /**
             * Determine when this third-party config should be visible
             */
            visibilityRule?: ('show_always' | 'show_matching' | 'hide_matching') | null;
          };
          id?: string | null;
        }[]
      | null;
  };
  /**
   * Configure context parameters for this page. Multiple values for the same parameter create an OR relationship.
   */
  contextParameters?: {
    /**
     * Value(s) for deviceClass parameter. Multiple selections create an OR relationship.
     */
    deviceClass?: ('desktop' | 'mobile')[] | null;
    /**
     * Value(s) for partner parameter. Multiple selections create an OR relationship.
     */
    partner?: ('samsung' | 'oppo' | 'apple')[] | null;
    /**
     * Value(s) for weatherMode parameter. Multiple selections create an OR relationship.
     */
    weatherMode?: ('active' | 'normal')[] | null;
    /**
     * Value(s) for siteMode parameter. Multiple selections create an OR relationship.
     */
    siteMode?: ('normal' | 'severe' | 'hybrid')[] | null;
    /**
     * Value(s) for privacyRegime parameter. Multiple selections create an OR relationship.
     */
    privacyRegime?:
      | (
          | 'ca'
          | 'usa'
          | 'usa-ccpa'
          | 'usa-co'
          | 'usa-ct'
          | 'usa-va'
          | 'ca-qc'
          | 'gdpr'
          | 'latam-co'
          | 'latam-do'
          | 'latam-pe'
          | 'lgpd'
          | 'pipl'
          | 'tr-kvkk'
        )[]
      | null;
  };
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "MorningBriefBlockConfig".
 */
export interface MorningBriefBlockConfig {
  id?: string | null;
  blockName?: string | null;
  blockType: 'MorningBrief';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "DailyForecastBlockConfig".
 */
export interface DailyForecastBlockConfig {
  locationProvider: 'geolocation' | 'specified';
  /**
   * Configure context parameter overrides for this block
   */
  contextParameterOverrides?: {
    /**
     * Enable context parameter overrides for this block
     */
    enabled?: boolean | null;
    /**
     * Configure context parameters for this block. Multiple values for the same parameter create an OR relationship.
     */
    parameters?: {
      /**
       * Value(s) for deviceClass parameter. Multiple selections create an OR relationship.
       */
      deviceClass?: ('desktop' | 'mobile')[] | null;
      /**
       * Value(s) for partner parameter. Multiple selections create an OR relationship.
       */
      partner?: ('samsung' | 'oppo' | 'apple')[] | null;
      /**
       * Value(s) for weatherMode parameter. Multiple selections create an OR relationship.
       */
      weatherMode?: ('active' | 'normal')[] | null;
      /**
       * Value(s) for siteMode parameter. Multiple selections create an OR relationship.
       */
      siteMode?: ('normal' | 'severe' | 'hybrid')[] | null;
      /**
       * Value(s) for privacyRegime parameter. Multiple selections create an OR relationship.
       */
      privacyRegime?:
        | (
            | 'ca'
            | 'usa'
            | 'usa-ccpa'
            | 'usa-co'
            | 'usa-ct'
            | 'usa-va'
            | 'ca-qc'
            | 'gdpr'
            | 'latam-co'
            | 'latam-do'
            | 'latam-pe'
            | 'lgpd'
            | 'pipl'
            | 'tr-kvkk'
          )[]
        | null;
    };
    /**
     * Determine when this block should be visible
     */
    visibilityRule?: ('show_always' | 'show_matching' | 'hide_matching') | null;
  };
  id?: string | null;
  blockName?: string | null;
  blockType: 'DailyForecast';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CurrentConditionsBlockConfig".
 */
export interface CurrentConditionsBlockConfig {
  locationProvider: 'geolocation' | 'specified';
  /**
   * Enter a location to display weather information for
   */
  locationEntry?: string | null;
  /**
   * Configure context parameter overrides for this block
   */
  contextParameterOverrides?: {
    /**
     * Enable context parameter overrides for this block
     */
    enabled?: boolean | null;
    /**
     * Configure context parameters for this block. Multiple values for the same parameter create an OR relationship.
     */
    parameters?: {
      /**
       * Value(s) for deviceClass parameter. Multiple selections create an OR relationship.
       */
      deviceClass?: ('desktop' | 'mobile')[] | null;
      /**
       * Value(s) for partner parameter. Multiple selections create an OR relationship.
       */
      partner?: ('samsung' | 'oppo' | 'apple')[] | null;
      /**
       * Value(s) for weatherMode parameter. Multiple selections create an OR relationship.
       */
      weatherMode?: ('active' | 'normal')[] | null;
      /**
       * Value(s) for siteMode parameter. Multiple selections create an OR relationship.
       */
      siteMode?: ('normal' | 'severe' | 'hybrid')[] | null;
      /**
       * Value(s) for privacyRegime parameter. Multiple selections create an OR relationship.
       */
      privacyRegime?:
        | (
            | 'ca'
            | 'usa'
            | 'usa-ccpa'
            | 'usa-co'
            | 'usa-ct'
            | 'usa-va'
            | 'ca-qc'
            | 'gdpr'
            | 'latam-co'
            | 'latam-do'
            | 'latam-pe'
            | 'lgpd'
            | 'pipl'
            | 'tr-kvkk'
          )[]
        | null;
    };
    /**
     * Determine when this block should be visible
     */
    visibilityRule?: ('show_always' | 'show_matching' | 'hide_matching') | null;
  };
  id?: string | null;
  blockName?: string | null;
  blockType: 'CurrentConditions';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "AdBlockConfig".
 */
export interface AdBlockConfig {
  adId: string;
  /**
   * Select an ad slot to use for this ad block
   */
  adSlot?: (string | null) | AdSlot;
  /**
   * Configure context parameter overrides for this block
   */
  contextParameterOverrides?: {
    /**
     * Enable context parameter overrides for this block
     */
    enabled?: boolean | null;
    /**
     * Configure context parameters for this block. Multiple values for the same parameter create an OR relationship.
     */
    parameters?: {
      /**
       * Value(s) for deviceClass parameter. Multiple selections create an OR relationship.
       */
      deviceClass?: ('desktop' | 'mobile')[] | null;
      /**
       * Value(s) for partner parameter. Multiple selections create an OR relationship.
       */
      partner?: ('samsung' | 'oppo' | 'apple')[] | null;
      /**
       * Value(s) for weatherMode parameter. Multiple selections create an OR relationship.
       */
      weatherMode?: ('active' | 'normal')[] | null;
      /**
       * Value(s) for siteMode parameter. Multiple selections create an OR relationship.
       */
      siteMode?: ('normal' | 'severe' | 'hybrid')[] | null;
      /**
       * Value(s) for privacyRegime parameter. Multiple selections create an OR relationship.
       */
      privacyRegime?:
        | (
            | 'ca'
            | 'usa'
            | 'usa-ccpa'
            | 'usa-co'
            | 'usa-ct'
            | 'usa-va'
            | 'ca-qc'
            | 'gdpr'
            | 'latam-co'
            | 'latam-do'
            | 'latam-pe'
            | 'lgpd'
            | 'pipl'
            | 'tr-kvkk'
          )[]
        | null;
    };
    /**
     * Determine when this block should be visible
     */
    visibilityRule?: ('show_always' | 'show_matching' | 'hide_matching') | null;
  };
  id?: string | null;
  blockName?: string | null;
  blockType: 'Ad';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ad-slots".
 */
export interface AdSlot {
  id: string;
  /**
   * The name of the ad_config entity
   */
  name: string;
  /**
   * The target slot for this ad configuration
   */
  slotTarget: string;
  /**
   * The position of the ad
   */
  adPosition: string;
  /**
   * The devices this ad configuration applies to
   */
  adDevices: ('mobile' | 'tablet' | 'desktop')[];
  /**
   * The sizes for this ad configuration
   */
  adSizes: {
    width: number;
    height: number;
    id?: string | null;
  }[];
  /**
   * The sequential group for this ad configuration
   */
  sequentialGroup: 'ATF' | 'BTF';
  /**
   * Keyword for this ad configuration
   */
  adKeyword?: string | null;
  /**
   * Custom JSON configuration for this ad slot
   */
  customJson?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * Display this Ad Configuration if Lite Ads are enabled
   */
  liteAdPosition?: boolean | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ImageBlockConfig".
 */
export interface ImageBlockConfig {
  imageType: 'internal' | 'external';
  internalImageId?: (string | null) | Image;
  externalImageUrl?: string | null;
  altText?: string | null;
  controls?: Controls;
  /**
   * Set this image as a priority image if it is above the fold so that it loads immediately
   */
  isPriorityImage?: boolean | null;
  /**
   * Optional URL to link the image to (e.g., affiliate link)
   */
  linkUrl?: string | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'Image';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "controls".
 */
export interface Controls {
  showCaption?: boolean | null;
  /**
   * Use the caption from the selected image
   */
  useImageCaption?: boolean | null;
  /**
   * Custom caption (only used if "Use Image Caption" is unchecked)
   */
  caption?: string | null;
  /**
   * Custom credit (only used if "Use Image Caption" is unchecked)
   */
  credit?: string | null;
  alignment?: ('left' | 'center' | 'right') | null;
  width?: ('full' | 'half') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "VideoBlockConfig".
 */
export interface VideoBlockConfig {
  /**
   * Select a video from the video collection. If selected, this will override the direct video fields.
   */
  videoReference?: (string | null) | Video;
  file?: string | null;
  playlist?: string | null;
  image?: string | null;
  title?: string | null;
  description?: string | null;
  tracks?: {
    file?: string | null;
    label?: string | null;
    kind?: ('captions' | 'chapters' | 'thumbnails') | null;
    default?: boolean | null;
  };
  playerSettings?: {
    aspectRatio?: ('16:9' | '4:3' | '1:1') | null;
    width?: string | null;
    showDescriptions?: boolean | null;
  };
  /**
   * Select content queries to use as playlists
   */
  playlistsReference?:
    | {
        contentQuery: string | ContentQuery;
        id?: string | null;
      }[]
    | null;
  custom?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'Video';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "videos".
 */
export interface Video {
  id: string;
  tenant?: (string | null) | Tenant;
  title: string;
  description?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  category: {
    relationTo: 'tags';
    value: string | Tag;
  };
  topic?: {
    relationTo: 'tags';
    value: string | Tag;
  } | null;
  authors?: (string | User)[] | null;
  externalAuthors?: string | null;
  content: {
    /**
     * Add URLs for different video formats (at least one required)
     */
    videoFormatUrls: {
      format: 'm3u8' | 'mp4';
      url: string;
      id?: string | null;
    }[];
    /**
     * Select the default video format to use
     */
    defaultFormat?: {
      format: 'm3u8' | 'mp4';
      /**
       * This field is populated automatically based on the selected format
       */
      url?: string | null;
    };
    /**
     * Enter the duration in the format xx:xx:xx (hours:minutes:seconds)
     */
    duration?: string | null;
    aspectRatio?: ('none' | 'horizontal' | 'vertical') | null;
    /**
     * Channel-specific video format URLs
     */
    channelVideos?:
      | {
          channel: string | Tag;
          format: 'm3u8' | 'mp4';
          /**
           * This field is populated automatically based on the selected format
           */
          url?: string | null;
          id?: string | null;
        }[]
      | null;
    /**
     * URL for the key frame image
     */
    keyFrameImage?: string | null;
    transcript?: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    } | null;
    /**
     * Add content queries as playlists
     */
    playlists?:
      | {
          contentQuery: string | ContentQuery;
          id?: string | null;
        }[]
      | null;
  };
  seo?: {
    /**
     * Used by partners to point to their external URL (e.g. https://example.com)
     */
    canonicalUrl?: string | null;
    title?: string | null;
    description?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (string | null) | Image;
  };
  coreMetadata?: {
    /**
     * Select entitlements for the content.
     */
    entitlements?:
      | ('platform-all-free' | 'platform-all-premium' | 'platform-all-standard' | 'platform-all-freemium')[]
      | null;
    /**
     * This field is automatically populated based on the category, but can be overridden if unlocked
     */
    adMetric?: (string | null) | Tag;
    adMetricLock?: boolean | null;
    createdBy?: string | null;
    updatedBy?: string | null;
  };
  channelOverrides?: {
    /**
     * Channel titles override the standard video title
     */
    channelTitles?:
      | {
          channel: string | Tag;
          title: string;
          id?: string | null;
        }[]
      | null;
  };
  tags?: (string | Tag)[] | null;
  publishDate?: string | null;
  publishDateLock?: boolean | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ContentMediaBlockConfig".
 */
export interface ContentMediaBlockConfig {
  title: string;
  contentQuery: string | ContentQuery;
  limit?: ('1' | '4') | null;
  ctaText?: string | null;
  ctaLink?: string | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'ContentMedia';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "LiveblogEntriesBlockConfig".
 */
export interface LiveblogEntriesBlockConfig {
  id?: string | null;
  blockName?: string | null;
  blockType: 'LiveblogEntries';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CTABlockConfig".
 */
export interface CTABlockConfig {
  /**
   * Main heading text for the CTA
   */
  title: string;
  subTitle?: string | null;
  subText?: string | null;
  /**
   * Standard contains a button to click such as 'see more' where as featured only contains the image and is clickable
   */
  ctaStyle: 'standard' | 'featured';
  /**
   * Select an image, SVG, or animated image
   */
  media?: (string | null) | Image;
  buttonText?: string | null;
  /**
   * Enter an external URL (e.g., https://example.com) or an internal URL starting with a slash (e.g., /about).
   */
  linkUrl: string;
  openInNewTab?: boolean | null;
  backgroundColor?: ('white' | 'light-gray' | 'brand-blue' | 'accent') | null;
  /**
   * Add tracking tags for analytics
   */
  analyticsTags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'CTABlock';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "SlideshowBlockConfig".
 */
export interface SlideshowBlockConfig {
  /**
   * Add images to your slideshow. Each image can have its own title, caption, and metadata.
   */
  slides: {
    imageAsset: string | Image;
    /**
     * Title to display for this image. Leave empty if you don't want to show a title.
     */
    imageTitle?: string | null;
    imageCaption?: string | null;
    id?: string | null;
  }[];
  settings?: Settings;
  id?: string | null;
  blockName?: string | null;
  blockType: 'Slideshow';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "settings".
 */
export interface Settings {
  rounded?: boolean | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "PromoDriverBlock".
 */
export interface PromoDriverBlock {
  /**
   * Main heading text for the promo (recommended maximum 25 characters, 1 line)
   */
  title: string;
  /**
   * Select an image for the promo
   */
  image: string | Image;
  /**
   * Secondary heading text for the promo (recommended maximum 30 characters, 1 line)
   */
  subtitle?: string | null;
  /**
   * Main text content for the promo (recommended maximum 65 characters, 2 lines)
   */
  bodyText?: string | null;
  /**
   * Call-to-action button configuration
   */
  ctaButton?: {
    /**
     * Maximum 20 characters (1 line). This limit is enforced.
     */
    text?: string | null;
    /**
     * Enter an external URL (e.g., https://example.com) or an internal URL starting with a slash (e.g., /about).
     */
    url?: string | null;
    openInNewTab?: boolean | null;
  };
  id?: string | null;
  blockName?: string | null;
  blockType: 'promoDriver';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "third-party-config".
 */
export interface ThirdPartyConfig {
  id: string;
  /**
   * The name of the third party configuration
   */
  name: string;
  /**
   * Authentication token for the third party service
   */
  token: string;
  /**
   * The type of third party service
   */
  type: string;
  /**
   * JSON configuration for the third party service
   */
  thirdPartyConfigJson:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "liveblogs".
 */
export interface Liveblog {
  id: string;
  tenant?: (string | null) | Tenant;
  title: string;
  content: {
    body: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    };
  };
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * Context parameter combinations for page routing
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "context-parameters".
 */
export interface ContextParameter {
  id: string;
  /**
   * Name to identify this parameter combination
   */
  name: string;
  /**
   * Automatically generated unique hash representing this parameter combination
   */
  hash?: string | null;
  /**
   * The parameter key-value pairs. Must include "pageKey".
   */
  parameters?: {
    /**
     * Value for pageKey parameter
     */
    pageKey?: string | null;
    /**
     * Value for deviceClass parameter.
     */
    deviceClass?: ('desktop' | 'mobile') | null;
    /**
     * Value for partner parameter.
     */
    partner?: ('samsung' | 'oppo' | 'apple') | null;
    /**
     * Value for weatherMode parameter.
     */
    weatherMode?: ('active' | 'normal') | null;
    /**
     * Value for siteMode parameter.
     */
    siteMode?: ('normal' | 'severe' | 'hybrid') | null;
    /**
     * Value for privacyRegime parameter.
     */
    privacyRegime?:
      | (
          | 'ca'
          | 'usa'
          | 'usa-ccpa'
          | 'usa-co'
          | 'usa-ct'
          | 'usa-va'
          | 'ca-qc'
          | 'gdpr'
          | 'latam-co'
          | 'latam-do'
          | 'latam-pe'
          | 'lgpd'
          | 'pipl'
          | 'tr-kvkk'
        )
      | null;
  };
  /**
   * The page this parameter combination is used at the top level
   */
  page?: (string | null) | Page;
  /**
   * Pages where this parameter combination is used in layouts
   */
  layouts?: (string | Page)[] | null;
  /**
   * Pages where this parameter combination is used in blocks
   */
  blocks?: (string | Page)[] | null;
  /**
   * Priority weight (0-1000) for resolving conflicts when multiple partial matches have the same parameter count. Example: {"pageKey": "home", "deviceClass": "desktop", "weatherMode": "active"} and {"pageKey": "home", "deviceClass": "desktop", "siteMode": "severe"} have the same number of parameters - whichever has the higher weight will be used.
   */
  weight?: number | null;
  /**
   * Optional description to help identify this parameter combination
   */
  description?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "redirects".
 */
export interface Redirect {
  id: string;
  /**
   * The original URL path that should be redirected (e.g., /old-path)
   */
  source: string;
  /**
   * The new URL path to redirect to (e.g., /new-path)
   */
  destination: string;
  /**
   * If checked, a 301 (permanent) redirect will be used. Otherwise, a 302 (temporary) redirect will be used.
   */
  permanent?: boolean | null;
  /**
   * Optional notes about this redirect (e.g., why it was created, when it can be removed)
   */
  notes?: string | null;
  /**
   * Whether this redirect was automatically created due to an assetName change
   */
  createdByAssetNameChange?: boolean | null;
  /**
   * The collection that contained the document whose assetName changed
   */
  originalCollection?: string | null;
  /**
   * The ID of the document whose assetName changed
   */
  originalDocumentId?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs".
 */
export interface PayloadJob {
  id: string;
  /**
   * Input data provided to the job
   */
  input?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  taskStatus?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  completedAt?: string | null;
  totalTried?: number | null;
  /**
   * If hasError is true this job will not be retried
   */
  hasError?: boolean | null;
  /**
   * If hasError is true, this is the error that caused it
   */
  error?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * Task execution log
   */
  log?:
    | {
        executedAt: string;
        completedAt: string;
        taskSlug:
          | 'inline'
          | 'getCMAssetByID'
          | 'getContentByTags'
          | 'getContentByIds'
          | 'refreshContentQueries'
          | 'updateAdMetrics'
          | 'schedulePublish';
        taskID: string;
        input?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        output?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        state: 'failed' | 'succeeded';
        error?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        id?: string | null;
      }[]
    | null;
  taskSlug?:
    | (
        | 'inline'
        | 'getCMAssetByID'
        | 'getContentByTags'
        | 'getContentByIds'
        | 'refreshContentQueries'
        | 'updateAdMetrics'
        | 'schedulePublish'
      )
    | null;
  queue?: string | null;
  waitUntil?: string | null;
  processing?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: string;
  document?:
    | ({
        relationTo: 'locations';
        value: string | Location;
      } | null)
    | ({
        relationTo: 'content-queries';
        value: string | ContentQuery;
      } | null)
    | ({
        relationTo: 'tenants';
        value: string | Tenant;
      } | null)
    | ({
        relationTo: 'tags';
        value: string | Tag;
      } | null)
    | ({
        relationTo: 'users';
        value: string | User;
      } | null)
    | ({
        relationTo: 'images';
        value: string | Image;
      } | null)
    | ({
        relationTo: 'pages';
        value: string | Page;
      } | null)
    | ({
        relationTo: 'articles';
        value: string | Article;
      } | null)
    | ({
        relationTo: 'videos';
        value: string | Video;
      } | null)
    | ({
        relationTo: 'liveblogs';
        value: string | Liveblog;
      } | null)
    | ({
        relationTo: 'context-parameters';
        value: string | ContextParameter;
      } | null)
    | ({
        relationTo: 'ad-slots';
        value: string | AdSlot;
      } | null)
    | ({
        relationTo: 'third-party-config';
        value: string | ThirdPartyConfig;
      } | null)
    | ({
        relationTo: 'redirects';
        value: string | Redirect;
      } | null)
    | ({
        relationTo: 'payload-jobs';
        value: string | PayloadJob;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: string;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: string;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "locations_select".
 */
export interface LocationsSelect<T extends boolean = true> {
  displayName?: T;
  locationType?: T;
  locationData?: T | LocationDataSelect<T>;
  canonicalID?: T;
  placeID?: T;
  locID?: T;
  city?: T;
  adminDistrictCode?: T;
  adminDistrict?: T;
  country?: T;
  countryCode?: T;
  locationPoint?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "locationData_select".
 */
export interface LocationDataSelect<T extends boolean = true> {
  poi?: T | PoiSelect<T>;
  airport?: T | AirportSelect<T>;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "poi_select".
 */
export interface PoiSelect<T extends boolean = true> {
  poiType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "airport_select".
 */
export interface AirportSelect<T extends boolean = true> {
  icaoCode?: T;
  iataCode?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "content-queries_select".
 */
export interface ContentQueriesSelect<T extends boolean = true> {
  name?: T;
  queries?:
    | T
    | {
        queryType?: T;
        tags?: T;
        tagsOperator?: T;
        category?: T;
        categoryOperator?: T;
        topic?: T;
        topicOperator?: T;
        groupsOperator?: T;
        syncStatus?: T;
        lastSynced?: T;
        contentId?: T;
        kalliopeCMQSID?: T;
        _locked?: T;
        _lockedByTaskID?: T;
        id?: T;
      };
  mergedContent?:
    | T
    | {
        id?: T;
        source?: T;
        contentType?: T;
        title?: T;
        overrideTitle?: T;
        description?: T;
        overrideDescription?: T;
        thumbnail?: T;
        overrideThumbnail?: T;
        url?: T;
        pinned?: T;
        sourceQueryID?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tenants_select".
 */
export interface TenantsSelect<T extends boolean = true> {
  name?: T;
  domain?: T;
  slug?: T;
  allowPublicRead?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tags_select".
 */
export interface TagsSelect<T extends boolean = true> {
  name?: T;
  slug?: T;
  slugLock?: T;
  type?: T;
  fullPath?: T;
  parent?: T;
  adMetric?: T;
  versions?: T;
  iabTags?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  role?: T;
  username?: T;
  tenants?:
    | T
    | {
        tenant?: T;
        roles?: T;
        id?: T;
      };
  _okta?:
    | T
    | {
        uid?: T;
        exp?: T;
        iss?: T;
        iat?: T;
        cid?: T;
      };
  isAuthor?: T;
  authorData?:
    | T
    | {
        firstName?: T;
        lastName?: T;
        bio?: T;
        bioUrl?: T;
        profilePicture?: T;
        socialMediaProfiles?:
          | T
          | {
              platform?: T;
              url?: T;
              id?: T;
            };
        authoredContent?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "images_select".
 */
export interface ImagesSelect<T extends boolean = true> {
  tenant?: T;
  seo?: T | SeoSelect<T>;
  tags?: T;
  prefix?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
  sizes?:
    | T
    | {
        small?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        large?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "seo_select".
 */
export interface SeoSelect<T extends boolean = true> {
  altText?: T;
  caption?: T;
  credit?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages_select".
 */
export interface PagesSelect<T extends boolean = true> {
  tenant?: T;
  title?: T;
  assetName?: T;
  assetNameLock?: T;
  variantName?: T;
  createdBy?: T;
  updatedBy?: T;
  content?:
    | T
    | {
        layout?:
          | T
          | {
              region?: T;
              blocks?:
                | T
                | {
                    MorningBrief?: T | MorningBriefBlockConfigSelect<T>;
                    DailyForecast?: T | DailyForecastBlockConfigSelect<T>;
                    CurrentConditions?: T | CurrentConditionsBlockConfigSelect<T>;
                    Ad?: T | AdBlockConfigSelect<T>;
                    Image?: T | ImageBlockConfigSelect<T>;
                    Video?: T | VideoBlockConfigSelect<T>;
                    ContentMedia?: T | ContentMediaBlockConfigSelect<T>;
                    LiveblogEntries?: T | LiveblogEntriesBlockConfigSelect<T>;
                    CTABlock?: T | CTABlockConfigSelect<T>;
                    Slideshow?: T | SlideshowBlockConfigSelect<T>;
                    promoDriver?: T | PromoDriverBlockSelect<T>;
                  };
              contextParameterOverrides?:
                | T
                | {
                    enabled?: T;
                    parameters?:
                      | T
                      | {
                          deviceClass?: T;
                          partner?: T;
                          weatherMode?: T;
                          siteMode?: T;
                          privacyRegime?: T;
                        };
                    visibilityRule?: T;
                  };
              id?: T;
            };
      };
  seo?:
    | T
    | {
        title?: T;
        description?: T;
        image?: T;
      };
  adConfig?:
    | T
    | {
        adsData?:
          | T
          | {
              networkCode?: T;
              adZone?: T;
              engine?: T;
              videoPrerollBidders?: T;
              contextParameterOverrides?:
                | T
                | {
                    enabled?: T;
                    parameters?:
                      | T
                      | {
                          deviceClass?: T;
                          partner?: T;
                          weatherMode?: T;
                          siteMode?: T;
                          privacyRegime?: T;
                        };
                    visibilityRule?: T;
                  };
            };
        thirdpartyConfigs?:
          | T
          | {
              thirdPartyConfig?: T;
              contextParameterOverrides?:
                | T
                | {
                    enabled?: T;
                    parameters?:
                      | T
                      | {
                          deviceClass?: T;
                          partner?: T;
                          weatherMode?: T;
                          siteMode?: T;
                          privacyRegime?: T;
                        };
                    visibilityRule?: T;
                  };
              id?: T;
            };
      };
  contextParameters?:
    | T
    | {
        deviceClass?: T;
        partner?: T;
        weatherMode?: T;
        siteMode?: T;
        privacyRegime?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "MorningBriefBlockConfig_select".
 */
export interface MorningBriefBlockConfigSelect<T extends boolean = true> {
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "DailyForecastBlockConfig_select".
 */
export interface DailyForecastBlockConfigSelect<T extends boolean = true> {
  locationProvider?: T;
  contextParameterOverrides?:
    | T
    | {
        enabled?: T;
        parameters?:
          | T
          | {
              deviceClass?: T;
              partner?: T;
              weatherMode?: T;
              siteMode?: T;
              privacyRegime?: T;
            };
        visibilityRule?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CurrentConditionsBlockConfig_select".
 */
export interface CurrentConditionsBlockConfigSelect<T extends boolean = true> {
  locationProvider?: T;
  locationEntry?: T;
  contextParameterOverrides?:
    | T
    | {
        enabled?: T;
        parameters?:
          | T
          | {
              deviceClass?: T;
              partner?: T;
              weatherMode?: T;
              siteMode?: T;
              privacyRegime?: T;
            };
        visibilityRule?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "AdBlockConfig_select".
 */
export interface AdBlockConfigSelect<T extends boolean = true> {
  adId?: T;
  adSlot?: T;
  contextParameterOverrides?:
    | T
    | {
        enabled?: T;
        parameters?:
          | T
          | {
              deviceClass?: T;
              partner?: T;
              weatherMode?: T;
              siteMode?: T;
              privacyRegime?: T;
            };
        visibilityRule?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ImageBlockConfig_select".
 */
export interface ImageBlockConfigSelect<T extends boolean = true> {
  imageType?: T;
  internalImageId?: T;
  externalImageUrl?: T;
  altText?: T;
  controls?: T | ControlsSelect<T>;
  isPriorityImage?: T;
  linkUrl?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "controls_select".
 */
export interface ControlsSelect<T extends boolean = true> {
  showCaption?: T;
  useImageCaption?: T;
  caption?: T;
  credit?: T;
  alignment?: T;
  width?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "VideoBlockConfig_select".
 */
export interface VideoBlockConfigSelect<T extends boolean = true> {
  videoReference?: T;
  file?: T;
  playlist?: T;
  image?: T;
  title?: T;
  description?: T;
  tracks?:
    | T
    | {
        file?: T;
        label?: T;
        kind?: T;
        default?: T;
      };
  playerSettings?:
    | T
    | {
        aspectRatio?: T;
        width?: T;
        showDescriptions?: T;
      };
  playlistsReference?:
    | T
    | {
        contentQuery?: T;
        id?: T;
      };
  custom?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ContentMediaBlockConfig_select".
 */
export interface ContentMediaBlockConfigSelect<T extends boolean = true> {
  title?: T;
  contentQuery?: T;
  limit?: T;
  ctaText?: T;
  ctaLink?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "LiveblogEntriesBlockConfig_select".
 */
export interface LiveblogEntriesBlockConfigSelect<T extends boolean = true> {
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CTABlockConfig_select".
 */
export interface CTABlockConfigSelect<T extends boolean = true> {
  title?: T;
  subTitle?: T;
  subText?: T;
  ctaStyle?: T;
  media?: T;
  buttonText?: T;
  linkUrl?: T;
  openInNewTab?: T;
  backgroundColor?: T;
  analyticsTags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "SlideshowBlockConfig_select".
 */
export interface SlideshowBlockConfigSelect<T extends boolean = true> {
  slides?:
    | T
    | {
        imageAsset?: T;
        imageTitle?: T;
        imageCaption?: T;
        id?: T;
      };
  settings?: T | SettingsSelect<T>;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "settings_select".
 */
export interface SettingsSelect<T extends boolean = true> {
  rounded?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "PromoDriverBlock_select".
 */
export interface PromoDriverBlockSelect<T extends boolean = true> {
  title?: T;
  image?: T;
  subtitle?: T;
  bodyText?: T;
  ctaButton?:
    | T
    | {
        text?: T;
        url?: T;
        openInNewTab?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "articles_select".
 */
export interface ArticlesSelect<T extends boolean = true> {
  tenant?: T;
  title?: T;
  subHeadline?: T;
  category?: T;
  authors?: T;
  externalAuthors?: T;
  partnerByline?:
    | T
    | {
        type?: T;
        richText?: T;
        text?: T;
      };
  headerLayout?: T;
  featuredImage?: T;
  topic?: T;
  content?:
    | T
    | {
        body?: T;
      };
  seo?:
    | T
    | {
        canonicalUrl?: T;
        title?: T;
        description?: T;
        image?: T;
      };
  coreMetadata?:
    | T
    | {
        entitlements?: T;
        adMetric?: T;
        adMetricLock?: T;
        createdBy?: T;
        updatedBy?: T;
      };
  channelOverrides?:
    | T
    | {
        channelHeadlines?:
          | T
          | {
              channel?: T;
              headline?: T;
              id?: T;
            };
      };
  tags?: T;
  assetName?: T;
  assetNameLock?: T;
  publishDate?: T;
  publishDateLock?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "videos_select".
 */
export interface VideosSelect<T extends boolean = true> {
  tenant?: T;
  title?: T;
  description?: T;
  category?: T;
  topic?: T;
  authors?: T;
  externalAuthors?: T;
  content?:
    | T
    | {
        videoFormatUrls?:
          | T
          | {
              format?: T;
              url?: T;
              id?: T;
            };
        defaultFormat?:
          | T
          | {
              format?: T;
              url?: T;
            };
        duration?: T;
        aspectRatio?: T;
        channelVideos?:
          | T
          | {
              channel?: T;
              format?: T;
              url?: T;
              id?: T;
            };
        keyFrameImage?: T;
        transcript?: T;
        playlists?:
          | T
          | {
              contentQuery?: T;
              id?: T;
            };
      };
  seo?:
    | T
    | {
        canonicalUrl?: T;
        title?: T;
        description?: T;
        image?: T;
      };
  coreMetadata?:
    | T
    | {
        entitlements?: T;
        adMetric?: T;
        adMetricLock?: T;
        createdBy?: T;
        updatedBy?: T;
      };
  channelOverrides?:
    | T
    | {
        channelTitles?:
          | T
          | {
              channel?: T;
              title?: T;
              id?: T;
            };
      };
  tags?: T;
  publishDate?: T;
  publishDateLock?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "liveblogs_select".
 */
export interface LiveblogsSelect<T extends boolean = true> {
  tenant?: T;
  title?: T;
  content?:
    | T
    | {
        body?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "context-parameters_select".
 */
export interface ContextParametersSelect<T extends boolean = true> {
  name?: T;
  hash?: T;
  parameters?:
    | T
    | {
        pageKey?: T;
        deviceClass?: T;
        partner?: T;
        weatherMode?: T;
        siteMode?: T;
        privacyRegime?: T;
      };
  page?: T;
  layouts?: T;
  blocks?: T;
  weight?: T;
  description?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ad-slots_select".
 */
export interface AdSlotsSelect<T extends boolean = true> {
  name?: T;
  slotTarget?: T;
  adPosition?: T;
  adDevices?: T;
  adSizes?:
    | T
    | {
        width?: T;
        height?: T;
        id?: T;
      };
  sequentialGroup?: T;
  adKeyword?: T;
  customJson?: T;
  liteAdPosition?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "third-party-config_select".
 */
export interface ThirdPartyConfigSelect<T extends boolean = true> {
  name?: T;
  token?: T;
  type?: T;
  thirdPartyConfigJson?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "redirects_select".
 */
export interface RedirectsSelect<T extends boolean = true> {
  source?: T;
  destination?: T;
  permanent?: T;
  notes?: T;
  createdByAssetNameChange?: T;
  originalCollection?: T;
  originalDocumentId?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs_select".
 */
export interface PayloadJobsSelect<T extends boolean = true> {
  input?: T;
  taskStatus?: T;
  completedAt?: T;
  totalTried?: T;
  hasError?: T;
  error?: T;
  log?:
    | T
    | {
        executedAt?: T;
        completedAt?: T;
        taskSlug?: T;
        taskID?: T;
        input?: T;
        output?: T;
        state?: T;
        error?: T;
        id?: T;
      };
  taskSlug?: T;
  queue?: T;
  waitUntil?: T;
  processing?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * Sitemode
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "site-mode".
 */
export interface SiteMode {
  id: string;
  siteModeConfigs?:
    | {
        attributes: Attributes;
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "attributes".
 */
export interface Attributes {
  configName: string;
  configMode?: ('severe' | 'hybrid' | 'normal') | null;
  configLocale?: ('en-US' | 'es-US') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "site-mode_select".
 */
export interface SiteModeSelect<T extends boolean = true> {
  siteModeConfigs?:
    | T
    | {
        attributes?: T | AttributesSelect<T>;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "attributes_select".
 */
export interface AttributesSelect<T extends boolean = true> {
  configName?: T;
  configMode?: T;
  configLocale?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskGetCMAssetByID".
 */
export interface TaskGetCMAssetByID {
  input: {
    kalliopeCMQID: string;
    queryDocID: string;
    lastSynced: string;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskGetContentByTags".
 */
export interface TaskGetContentByTags {
  input: {
    queryId: string;
    queryDocID: string;
    tags?:
      | {
          id: string;
        }[]
      | null;
    tagsOperator?: string | null;
    category?:
      | {
          id: string;
        }[]
      | null;
    categoryOperator?: string | null;
    topic?:
      | {
          id: string;
        }[]
      | null;
    topicOperator?: string | null;
    groupsOperator?: string | null;
    lastSynced?: string | null;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskGetContentByIds".
 */
export interface TaskGetContentByIds {
  input: {
    queryId: string;
    queryDocID: string;
    contentIds: {
      value: string;
    }[];
    lastSynced?: string | null;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskRefreshContentQueries".
 */
export interface TaskRefreshContentQueries {
  input: {
    lastSyncedBefore?: string | null;
    limit?: number | null;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskUpdateAdMetrics".
 */
export interface TaskUpdateAdMetrics {
  input: {
    categoryId: string;
    adMetricId?: string | null;
    batchSize?: number | null;
    skipIds?:
      | {
          value: string;
        }[]
      | null;
    locale?: string | null;
  };
  output: {
    updatedCount?: number | null;
    hasMore?: boolean | null;
    message?: string | null;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskSchedulePublish".
 */
export interface TaskSchedulePublish {
  input: {
    type?: ('publish' | 'unpublish') | null;
    locale?: string | null;
    doc?:
      | ({
          relationTo: 'pages';
          value: string | Page;
        } | null)
      | ({
          relationTo: 'articles';
          value: string | Article;
        } | null)
      | ({
          relationTo: 'videos';
          value: string | Video;
        } | null)
      | ({
          relationTo: 'liveblogs';
          value: string | Liveblog;
        } | null);
    global?: string | null;
    user?: (string | null) | User;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "YouTubeBlockConfig".
 */
export interface YouTubeBlockConfig {
  /**
   * Enter the full YouTube URL
   */
  url: string;
  /**
   * Choose how the video embed should be displayed.
   */
  size?: ('hero' | 'article') | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'YouTube';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "BuyButtonBlockConfig".
 */
export interface BuyButtonBlockConfig {
  url: string;
  label: string;
  variant?:
    | ('bg-brand-200' | 'bg-brand-300' | 'bg-yellow-400' | 'bg-green-600' | 'bg-violet-500' | 'bg-gray-900')
    | null;
  inline?: boolean | null;
  /**
   * Adds proper rel attributes for affiliate links (nofollow, noopener, noreferrer)
   */
  isAffiliate?: boolean | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'BuyButton';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TwitterBlockConfig".
 */
export interface TwitterBlockConfig {
  entryMethod: 'url' | 'id';
  /**
   * Paste the full URL of the tweet
   */
  tweetURL?: string | null;
  /**
   * Enter only the numeric ID of the tweet
   */
  tweetId?: string | null;
  /**
   * Show replies in the tweet thread
   */
  showThread?: boolean | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'Twitter';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "InstagramBlockConfig".
 */
export interface InstagramBlockConfig {
  /**
   * Enter the full Instagram URL
   */
  url: string;
  /**
   * Choose how the video embed should be displayed.
   */
  size?: ('hero' | 'article') | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'Instagram';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}