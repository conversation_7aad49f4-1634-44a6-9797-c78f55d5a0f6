import { withPayload } from '@payloadcms/next/withPayload';
import rewrites from './middleware/rewrites/index.mjs';
import bundleAnalyzer from '@next/bundle-analyzer';
import withVercelToolbarFunc from '@vercel/toolbar/plugins/next';

const withVercelToolbar = withVercelToolbarFunc();

/** @type {import('next').NextConfig} */
const nextConfig = {
	productionBrowserSourceMaps: process.env.PRODUCTION_SOURCEMAPS === 'true',
	expireTime: 60 * 5,
	// use for testing purposes locally
	// reactStrictMode: false,
	images: {
		loader: 'custom',
		loaderFile: './utils/imageLoader.ts',
		remotePatterns: [
			{
				protocol: 'https',
				hostname: 's.w-x.co',
				port: '',
				pathname: '/**',
				search: '',
			},
			{
				protocol: 'https',
				hostname: 's-dev.w-x.co',
				port: '',
				pathname: '/**',
				search: '',
			},
		],
	},
	logging: {
		fetches: {
			fullUrl: true,
		},
	},
	experimental: {
		useCache: true,
	},
	rewrites,
};

export default bundleAnalyzer({
	enabled: process.env.ANALYZE === 'true',
	openAnalyzer: true,
})(withPayload(withVercelToolbar(nextConfig)));
