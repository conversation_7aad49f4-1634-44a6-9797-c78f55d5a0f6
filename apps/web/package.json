{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"predev": "payload generate:importmap", "dev": "NODE_OPTIONS='--inspect' next dev --turbopack --port 3001 --experimental-https", "prebuild": "payload generate:importmap", "build": "next build --debug", "build:analyze": "ANALYZE=true pnpm build", "start": "next start", "lint": "eslint --max-warnings=0 .", "check-types": "tsc --noEmit", "clean": "rimraf .turbo .next", "generate:importmap": "payload generate:importmap", "generate:types": "payload generate:types", "test": "vitest run", "test:watch": "vitest watch", "test:e2e": "playwright test --reporter=blob", "test:e2e:ui": "playwright test --ui", "test:e2e:setup": "./scripts/setup-playwright.sh", "test:e2e:report": "playwright show-report --port=9324", "payload:migrate:fresh": "pnpx payload migrate:fresh", "vercel:link": "pnpx vercel link", "vercel:env:pull": "pnpx vercel env pull --environment=development"}, "dependencies": {"@amplitude/experiment-js-client": "^1.10.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@mparticle/web-sdk": "^2.35.0", "@payloadcms/db-mongodb": "3.44.0", "@payloadcms/email-nodemailer": "3.44.0", "@payloadcms/live-preview-react": "3.44.0", "@payloadcms/next": "3.44.0", "@payloadcms/payload-cloud": "3.44.0", "@payloadcms/plugin-cloud-storage": "3.44.0", "@payloadcms/plugin-multi-tenant": "3.44.0", "@payloadcms/plugin-oauth2": "^4.13.0", "@payloadcms/plugin-seo": "3.44.0", "@payloadcms/richtext-lexical": "3.44.0", "@payloadcms/storage-s3": "3.44.0", "@payloadcms/ui": "3.44.0", "@repo/dal": "workspace:*", "@repo/icons": "workspace:*", "@repo/mocks": "workspace:*", "@repo/ui": "workspace:*", "@twc/upsx-sdk": "workspace:*", "@vercel/speed-insights": "^1.2.0", "bloom-filters": "3.0.1", "class-variance-authority": "^0.7.1", "cookies-next": "^6.0.0", "date-fns": "^4.1.0", "es-toolkit": "^1.37.2", "flags": "^4.0.0", "graphql": "^16.10.0", "jose": "^6.0.10", "jotai": "^2.12.2", "jotai-effect": "^2.0.2", "jotai-optics": "^0.4.0", "jsdom": "^26.0.0", "lucide-react": "^0.525.0", "next": "^15.2.0", "next-themes": "^0.4.6", "nodemailer": "^7.0.0", "optics-ts": "^2.4.1", "path-to-regexp": "6.3.0", "payload": "3.44.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-responsive": "^10.0.1", "schema-dts": "^1.1.5", "server-only": "^0.0.1", "sharp": "^0.34.0", "swr": "^2.3.3", "use-memo-one": "^1.1.3", "uuid": "^11.1.0"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.1", "@playwright/test": "^1.42.1", "@repo/eslint-config": "workspace:*", "@repo/mocks": "workspace:*", "@repo/playwright-utils": "workspace:*", "@repo/typescript-config": "workspace:*", "@storybook/react": "^8.6.11", "@storybook/types": "^8.6.11", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@turbo/gen": "^2.4.4", "@types/jsdom": "^21.1.7", "@types/mparticle__web-sdk": "^2.20.3", "@types/node": "^22.13.8", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.21.0", "ignore-loader": "^0.1.2", "jsdom": "^26.0.0", "rimraf": "^6.0.1", "typescript": "~5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.7"}}