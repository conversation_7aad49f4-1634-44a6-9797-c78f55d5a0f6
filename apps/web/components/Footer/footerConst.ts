import {
	ACCESSIBILITY_STATEMENT_LINK,
	ADCHOICES_LINK,
	COOKIE_NOTICE_LINK,
	DATA_VENDORS_LINK,
	PRIVACY_POLICY_LINKS,
	TERMS_OF_USE_LINKS,
} from '@/components/Footer/legalLinks';
import { Facebook } from '@repo/icons/Social';
import { Twitter } from '@repo/icons/Social';
import { Instagram } from '@repo/icons/Social';
import { YouTube } from '@repo/icons/Social';
import TWCoIcon from '@/assets/footer/the-weather-company.svg';
import TWCIcon from '@/assets/footer/the-weather-channel.svg';
import WUIcon from '@/assets/footer/weather-underground.svg';

export const allCorporateLinks = [
	{
		title: 'Feedback',
		titleTranslationKey: 'feedback',
		url: '//support.weather.com/s/',
		queryString: 'language=${lang}&type=TWC',
		wuQueryString: 'language=${lang}&type=WU',
		target: '_blank',
		rel: 'noopener nofollow',
	},
	{
		title: 'Weather API',
		titleTranslationKey: 'weatherAPI',
		url: 'https://www.wunderground.com/weather/api/?ref=twc-DE',
		includeLocales: ['de-DE'],
		excludePartners: ['samsungVietnam'],
		target: '_self',
		rel: 'nofollow',
	},
	{
		title: 'Weather API',
		titleTranslationKey: 'weatherAPI',
		url: 'https://www.wunderground.com/weather/api',
		excludeLocales: ['de-DE', 'en-US'],
		excludePartners: ['samsungVietnam'],
		target: '_self',
		rel: 'nofollow',
	},
	{
		title: 'Mission',
		titleTranslationKey: 'mission',
		url: '/mission',
		includeLocales: ['en-US'],
		target: '_blank',
		rel: 'noopener nofollow',
	},
	{
		title: 'Careers',
		titleTranslationKey: 'careers',
		url: 'https://www.weathercompany.com/careers',
		includeLocales: ['en-US'],
		target: '_blank',
		rel: 'noopener nofollow',
	},
	{
		title: 'News Room',
		titleTranslationKey: 'pressRoom',
		url: 'https://www.weathercompany.com/newsroom',
		target: '_blank',
		rel: 'noopener nofollow',
	},
	{
		title: 'Advertise With Us',
		titleTranslationKey: 'advertiseWithUs',
		url: 'https://www.weathercompany.com/advertising/',
		includeLocales: ['en-US', 'de-DE'],
		target: '_blank',
		rel: 'noopener nofollow',
	},
	{
		title: 'TV',
		titleTranslationKey: 'tv',
		url: 'https://www.weathergroup.com/brands/the-weather-channel/',
		includeLocales: ['en-US'],
		target: '_blank',
		rel: 'nofollow',
	},
	{
		title: 'Newsletter Sign Up',
		titleTranslationKey: 'newsletterSignUp',
		url: 'https://weather.com/newsletter?cm_ven=dnt_newsletter_footer',
		includeLocales: ['en-US'],
		target: '_self',
		rel: 'nofollow',
	},
	{
		title: 'Impressum',
		titleTranslationKey: 'impressum',
		url: '/weather.com/de-DE/impressum',
		includeLocales: ['de-DE'],
		target: '_blank',
		rel: 'noopener nofollow',
	},
	{
		title: 'Weather Data APIs',
		titleTranslationKey: 'weatherDataAPIs',
		url: 'https://www.weathercompany.com/weather-data-apis/',
		target: '_blank',
		rel: 'noopener nofollow',
	},
];

export const allLegalLinks = [
	...TERMS_OF_USE_LINKS,
	...PRIVACY_POLICY_LINKS,
	COOKIE_NOTICE_LINK,
	ADCHOICES_LINK,
	ACCESSIBILITY_STATEMENT_LINK,
	DATA_VENDORS_LINK,
];

export const allPrivacyLinks = [
	{
		title: 'Privacy Settings',
		titleTranslationKey: 'privacySettings',
		url: '/${locale}/privacy-settings',
		target: '_self',
		rel: 'noopener noreferrer nofollow',
		wuUrl: '//www.wunderground.com/${locale}/privacy-settings',
	},
	{
		title: 'Data Purposes',
		titleTranslationKey: 'dataPurposes',
		url: '/${locale}/data-purposes?regime=jp',
		wuUrl: '//www.wunderground.com/${locale}/data-purposes?regime=jp',
		target: '_self',
		rel: 'noopener noreferrer nofollow',
	},
	{
		title: 'Do Not Sell or Share My Personal Information',
		titleTranslationKey: 'doNotSell',
		url: '/${locale}/privacy-settings#do-not-sell',
		target: '_self',
		rel: 'noopener noreferrer nofollow',
		wuUrl: '//www.wunderground.com/${locale}/privacy-settings#do-not-sell',
	},
	{
		title: 'Limit Use of My Sensitive Personal Information',
		titleTranslationKey: 'sensitiveData',
		url: '/${locale}/privacy-settings#sensitive-data',
		target: '_self',
		rel: 'noopener noreferrer nofollow',
	},
	{
		title: 'Data Rights',
		titleTranslationKey: 'dataRights',
		url: '/${locale}/data-rights',
		wuUrl: '//www.wunderground.com/${locale}/data-rights',
		target: '_self',
		rel: 'noopener noreferrer nofollow',
	},
];

export const socialLinks = [
	{
		url: 'https://www.facebook.com/TheWeatherChannel',
		title: 'Facebook',
		IconComponent: Facebook,
		iconOnly: true,
	},
	{
		url: 'http://www.twitter.com/weatherchannel',
		title: 'X',
		IconComponent: Twitter,
		iconOnly: true,
		iconClassName: 'p-0.5',
	},
	{
		url: 'http://instagram.com/weatherchannel',
		title: 'Instagram',
		IconComponent: Instagram,
		iconOnly: true,
	},
	{
		url: 'http://www.youtube.com/user/TheWeatherChannel/',
		title: 'Youtube',
		IconComponent: YouTube,
		iconOnly: true,
	},
];

export const brandLinks = [
	{
		url: 'https://www.weathercompany.com/',
		title: 'The Weather Company',
		iconUrl: TWCoIcon.src,
		iconOnly: true,
		iconClassName: 'w-[50px]',
	},
	{
		url: 'https://weather.com/',
		title: 'The Weather Channel',
		iconUrl: TWCIcon.src,
		iconOnly: true,
		iconClassName: 'w-[35px]',
	},
	{
		url: 'https://www.wunderground.com/',
		title: 'Weather Underground',
		iconUrl: WUIcon.src,
		iconOnly: true,
		iconClassName: 'w-[50px]',
	},
	{
		url: 'https://weather.com/storm-radar',
		title: 'Storm Radar',
		iconUrl: 'https://s.w-x.co/Storm-Radar-App-Icon.png',
		iconOnly: true,
		iconClassName: 'w-[36px]',
	},
];
