import Link from '@repo/ui/components/Link/Link';
import {
	CorporateFooterLinks,
	LegalFooterLinks,
	PrivacyFooterLinks,
	SocialFooterLinks,
} from '@/components/Footer/FooterLinkSegments';
import Image from '@repo/ui/components/Image/Image';
import Essential from '@/assets/footer/eSSENTIAL.svg';
import Georgia from '@/assets/footer/georgia.svg';

function Footer() {
	return (
		<footer className="w-full border-t border-gray-200">
			<div className="mx-auto max-w-6xl p-7">
				<SocialFooterLinks />

				<CorporateFooterLinks />

				<LegalFooterLinks />

				<div className="mt-2.5 sm:text-center">
					<Link
						href="http://www.exploregeorgia.org/weather-and-travel-tips"
						className="inline-block px-2.5 align-bottom"
					>
						<Image
							unoptimized
							src={Georgia.src}
							alt="Explore Georgia"
							className="h-[34px] w-auto"
							aria-label="Explore Georgia"
							rounded={false}
						/>
					</Link>

					<Link
						href="https://www.essentialaccessibility.com/the-weather-channel?utm_source=theweatherchannelhomepage&utm_medium=iconlarge&utm_term=eachannelpage&utm_content=header&utm_campaign=theweatherchannel"
						className="inline-block align-bottom"
					>
						<Image
							unoptimized
							src={Essential.src}
							alt="Essential Accessibility"
							className="h-[23px] w-auto"
							aria-label="Essential Accessibility"
							rounded={false}
						/>
					</Link>
				</div>

				{/* Data Responsibility Text */}
				<div className="my-4.5 mx-auto max-w-xl text-sm text-gray-600 sm:text-center">
					<p>
						We recognize our responsibility to use data and technology for good.
						We may use or share your data with our data vendors. Take control of
						your data.
					</p>
				</div>

				{/* Privacy Controls */}
				<div className="my-3.5">
					<PrivacyFooterLinks />
				</div>

				{/* Forecast Accuracy */}
				<div className="mx-auto max-w-xl text-sm text-gray-600 sm:text-center">
					<p>
						The Weather Channel is the world&lsquo;s most accurate forecaster
						according to ForecastWatch,{' '}
						<Link
							href="https://forecastwatch.com/AccuracyOverview2017-2022"
							className="text-[#1b4de4] underline hover:no-underline"
						>
							Global and Regional Weather Forecast Accuracy Overview
						</Link>
						, 2017-2022, commissioned by The Weather Company.
					</p>
				</div>

				{/* Weather Channel Link */}
				<p className="mt-5 text-sm text-gray-600 sm:text-center">
					Weather Channel
				</p>

				{/* Copyright */}
				<div className="mb-3 mt-4 text-xs text-gray-600 sm:text-center">
					<p>© The Weather Company, LLC 2025</p>
				</div>
			</div>
			{/* Spacing for mobile nav */}
			<div className="h-15 md:hidden" />
		</footer>
	);
}

export default Footer;
