import Link from '@repo/ui/components/Link/Link';
import { FooterLink } from '@/components/Footer/footerLinksUtil';
import Image from '@repo/ui/components/Image/Image';

type FooterNavLinksProps = {
	links: FooterLink[];
	hasSeparators?: boolean | 'row' | 'column';
	separatorDirChanges?: boolean;
	separatorColorClass?:
		| `border-${string}`
		| [`border-${string}`, `sm:border-${string}`];
	className?: string;
	itemClassName?: string;
	linkClassName?: string;
};

const getSeparatorClasses = (
	separators?: boolean | 'row' | 'column',
	separatorDirChanges?: boolean,
	separatorColorClass?: string | string[],
) => {
	if (!separators) return '';

	const sepColor =
		typeof separatorColorClass === 'string'
			? [separatorColorClass]
			: separatorColorClass || [];

	return (
		`${sepColor[0] || 'border-gray-600'} ${sepColor[1] || ''} last:border-0 ` +
		(separators === 'column'
			? // Column separators (vertical)
				separatorDirChanges
				? 'sm:border-b max-sm:border-r'
				: 'border-b'
			: // Row separators (horizontal)
				separatorDirChanges
				? 'sm:border-r max-sm:border-b'
				: 'border-r')
	);
};

export function FooterNavLinks({
	links,
	hasSeparators = false,
	className,
	itemClassName,
	linkClassName,
	separatorDirChanges,
	separatorColorClass,
}: FooterNavLinksProps) {
	return (
		<ul className={`flex flex-wrap items-center ${className || ''}`}>
			{links.map((link) => (
				<li
					key={link.title}
					className={`flex items-center ${getSeparatorClasses(hasSeparators, separatorDirChanges, separatorColorClass)} ${itemClassName || ''}`}
				>
					<Link
						href={link.url}
						className={`flex items-center justify-center text-center text-sm text-gray-600 hover:underline ${linkClassName || ''}`}
						target={link.target}
						rel={link.rel}
					>
						{!link.iconOnly && <span>{link.title}</span>}
						{link.IconComponent ? (
							<link.IconComponent
								className={link.iconClassName || 'h-6'}
								aria-label={link.iconOnly ? link.title : ''}
							/>
						) : (
							link.iconUrl && (
								<Image
									unoptimized
									src={link.iconUrl}
									alt={link.iconOnly ? link.title : ''}
									className={link.iconClassName || 'h-6'}
								/>
							)
						)}
					</Link>
				</li>
			))}
		</ul>
	);
}

export default FooterNavLinks;
