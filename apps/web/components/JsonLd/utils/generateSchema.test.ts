import { describe, it, expect, vi } from 'vitest';
import {
	generateArticleSchema,
	generateLiveBlogSchema,
	generateOrganizationSchema,
	generateVideoSchema,
	generateWebPageSchema,
	twcSchema,
} from './generateSchema';
import { LiveBlogArticle } from '@/app/(web)/[code]/content/classic/utils/transformDsxArticle';

// Mock the articleData module
vi.mock('@/collections/Articles/utils/articleData', () => ({
	getArticleDetails: vi.fn(),
}));

describe('generateSchema', () => {
	describe('twcSchema', () => {
		it('should have the correct structure for The Weather Channel organization', () => {
			// Use snapshot for the full structure
			expect(twcSchema).toMatchSnapshot();

			// Keep a few key assertions for critical properties
			expect(twcSchema.name).toBe('The Weather Channel');
			expect(twcSchema.url).toBe('https://weather.com');
		});
	});

	describe('generateArticleSchema', () => {
		const mockArticleDetails = {
			title: 'Test Article',
			seoTitle: 'Test SEO Title',
			seoDescription: 'Test description',
			canonicalUrl: 'https://weather.com/test-article',
			url: 'https://weather.com/test-article',
			images: [
				{ url: 'https://example.com/image1.jpg' },
				{ url: 'https://example.com/image2.jpg', alt: 'Test image' },
			],
			createdAt: '2025-04-25T10:00:00Z',
			datePublished: '2025-04-25T12:00:00Z',
			dateModified: '2025-04-25T14:00:00Z',
			authors: ['John Doe', 'Jane Smith'],
			tagNames: ['weather', 'test'],
			topicName: 'Weather News',
			keywords: ['weather', 'test', 'article'],
			video: undefined,
		};

		it('should generate a valid article schema', () => {
			const schema = generateArticleSchema(mockArticleDetails);

			// Use snapshot for the full structure
			expect(schema).toMatchSnapshot();

			// Keep a few key assertions for critical properties
			expect(schema['@type']).toBe('NewsArticle');
			expect(schema.headline).toBe('Test Article');
			expect(schema.url).toBe('https://weather.com/test-article');
			expect(schema.dateCreated).toBe('2025-04-25T10:00:00Z');
		});

		it('should handle articles with no authors', () => {
			const schema = generateArticleSchema({
				...mockArticleDetails,
				authors: [],
			});

			// Use snapshot for the full structure
			expect(schema).toMatchSnapshot();

			// Verify author is still set to twcSchema even with no authors
			expect(schema.author).toBe(twcSchema);
		});
	});

	describe('generateOrganizationSchema', () => {
		it('should generate a valid organization schema with all fields', () => {
			const schema = generateOrganizationSchema(
				'Test Organization',
				'https://example.com',
				'https://example.com/logo.png',
				['https://twitter.com/testorg', 'https://facebook.com/testorg'],
			);

			// Use snapshot for the full structure
			expect(schema).toMatchSnapshot();

			// Keep a few key assertions for critical properties

			const typedSchema = schema as any;
			expect(typedSchema['@type']).toBe('Organization');
			expect(typedSchema.name).toBe('Test Organization');
		});

		it('should generate a valid organization schema without optional fields', () => {
			const schema = generateOrganizationSchema(
				'Test Organization',
				'https://example.com',
			);

			// Use snapshot for the full structure
			expect(schema).toMatchSnapshot();

			const typedSchema = schema as any;
			// Verify optional fields are undefined
			expect(typedSchema.logo).toBeUndefined();
			expect(typedSchema.sameAs).toBeUndefined();
		});
	});

	describe('generateWebPageSchema', () => {
		it('should generate a valid webpage schema', () => {
			const schema = generateWebPageSchema(
				'Test Page',
				'https://example.com/test-page',
			);

			// Use snapshot for the full structure
			expect(schema).toMatchSnapshot();

			// Keep a few key assertions for critical properties

			const typedSchema = schema as any;
			expect(typedSchema['@type']).toBe('WebPage');
			expect(typedSchema.name).toBe('Test Page');
			expect(typedSchema.url).toBe('https://example.com/test-page');
			expect(typedSchema.author).toBe(twcSchema);
		});
	});

	// Add basic tests for new schemas
	describe('generateLiveBlogSchema', () => {
		it('should generate a valid live blog schema', () => {
			const mockArticle = {
				createdAt: '2025-04-25T10:00:00Z',
				liveBlog: {
					isLiveBlog: true,
					eventData: {
						eventStartTime: '2023-10-01T00:00:00Z',
						eventEndTime: '2023-10-01T01:00:00Z',
						liveBlogEntries: [
							{
								title: 'Test Entry',
								body: 'Test Body',
								wxnodes: [
									{
										id: 'entry-1',
										type: 'wxnode_internal_image',
										__wxnext: {
											imageUrl: 'https://example.com/image.jpg',
										},
									},
								],
								lastmodifieddate: '2023-10-01T00:00:00Z',
								publishedDate: '2023-10-01T00:00:00Z',
								timestamp: '01/Oct/2023 00:00 AM GMT',
								includeTimestamp: true,
							},
						],
					},
				},
			} as unknown as LiveBlogArticle;

			const mockArticleDetails = {
				title: 'Test Live Blog',
				seoTitle: '',
				seoDescription: 'Test live blog description',
				canonicalUrl: 'https://weather.com/test-live-blog',
				url: 'https://weather.com/test-live-blog',
				images: [{ url: 'https://example.com/image.jpg' }],
				createdAt: '2025-04-25T12:00:00Z',
				datePublished: '2025-04-25T12:00:00Z',
				dateModified: '2025-04-25T14:00:00Z',
				authors: [],
				tagNames: [],
				topicName: 'Weather News',
				keywords: ['weather', 'live', 'blog'],
				video: undefined,
			};

			const schema = generateLiveBlogSchema(mockArticle, mockArticleDetails);

			// Use snapshot for the full structure
			expect(schema).toMatchSnapshot();

			// Keep a few key assertions for critical properties

			const typedSchema = schema as any;
			expect(typedSchema['@type']).toBe('LiveBlogPosting');
			expect(typedSchema.headline).toBe('Test Live Blog');
		});
	});

	describe('generateVideoSchema', () => {
		it('should generate a valid video schema', () => {
			const mockVideo = {
				title: 'Test Video',
				description: 'Test video description',
				thumbnailUrl: 'https://example.com/thumbnail.jpg',
				headline: 'Test Video Headline',
				duration: 'PT2M30S',
				contentUrl: 'https://example.com/video.mp4',
				transcript: 'Test transcript',
				url: 'https://weather.com/test-video',
				publishedAt: '2025-04-25T12:00:00Z',
				updatedAt: '2025-04-25T14:00:00Z',
			};

			const schema = generateVideoSchema(mockVideo);

			// Use snapshot for the full structure
			expect(schema).toMatchSnapshot();

			// Keep a few key assertions for critical properties

			const typedSchema = schema as any;
			expect(typedSchema['@type']).toBe('VideoObject');
			expect(typedSchema.name).toBe('Test Video');
			expect(typedSchema.contentUrl).toBe('https://example.com/video.mp4');
		});
	});
});
