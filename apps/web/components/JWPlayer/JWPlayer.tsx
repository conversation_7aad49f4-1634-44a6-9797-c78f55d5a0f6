'use client';

import React, { FC, useEffect, useRef, useMemo, useId, useState } from 'react';
import Script from 'next/script';
import type { JWPlayerInstance, PlayerProps } from './types/player';
import { getDefaultSetupOptions } from './utils/setupUtils';
import { SetupOptions } from './types/setup';
import { createLogger } from '@repo/logger';
import { CallbackProps } from './types/events';

const logger = createLogger('JWPlayer');

/**
 * JWPlayer component for displaying videos and playlists
 *
 * This component provides a wrapper around the JW Player JavaScript API,
 * handling script loading, player initialization, and event handling.
 * It supports both direct video URLs and playlists, with configurable
 * options for autoplay, aspect ratio, and advertising.
 */
export const JWPlayer: FC<PlayerProps> = ({
	playlist,
	options = {},
	setupParams = {},
	events = [],
}) => {
	const playerDomId = `player-${useId()}`;

	const { playerId = 'IdNDrRWQ' } = setupParams;

	const configProps = useMemo(
		(): SetupOptions => ({
			playlist,
			...getDefaultSetupOptions(setupParams),
			...options,
		}),
		[playlist, setupParams, options],
	);

	const playerInstanceRef = useRef<JWPlayerInstance | null>(null);
	const isInitializedRef = useRef(false);
	const hasFile = playlist?.[0]?.file;

	// State for script loading status
	const [scriptStatus, setScriptStatus] = useState<
		'loading' | 'ready' | 'error'
	>(hasFile ? 'loading' : 'error');

	// Initializing player
	useEffect(() => {
		// Note: The player is initialized only once. Subsequent changes to `configProps` or `events`
		// will not trigger a re-setup intentionally. This is to avoid unnecessary reinitializations.
		// Check if player is ready for initialization
		if (
			isInitializedRef.current ||
			typeof window === 'undefined' ||
			!('jwplayer' in window) ||
			scriptStatus !== 'ready' ||
			!hasFile
		) {
			return;
		}

		const playerInstance = window.jwplayer(playerDomId);
		playerInstanceRef.current = playerInstance;
		isInitializedRef.current = true;

		logger.info('initialized player');

		// Setup player
		playerInstance.setup(configProps);

		// Register event handlers
		events.forEach(({ eventName, callback }) => {
			playerInstance.on(eventName, (event) => {
				callback({
					event,
					player: playerInstance,
				} as CallbackProps);
			});
			logger.lifecycle(`Registered event handler for ${eventName}`);
		});
	}, [configProps, events, hasFile, playerDomId, scriptStatus]);

	useEffect(() => {
		// remove player reference on unmount
		return () => {
			if (playerInstanceRef.current) {
				try {
					playerInstanceRef.current.remove();
					playerInstanceRef.current = null;
					isInitializedRef.current = false;
				} catch (e) {
					logger.error('Error removing JW Player', e);
				}
			}
		};
	}, []);

	// Script onLoad required for AdminBlock, onReady required for Block
	return (
		<div>
			{hasFile && (
				<Script
					src={`https://cdn.jwplayer.com/libraries/${playerId}.js`}
					strategy="afterInteractive"
					onLoad={() => setScriptStatus('ready')}
					onReady={() => setScriptStatus('ready')}
					onError={() => setScriptStatus('error')}
				/>
			)}
			<div className="overflow-hidden rounded-lg">
				{scriptStatus === 'loading' && hasFile ? ( // Loading state
					<div className="flex aspect-video w-full items-center justify-center bg-black">
						<div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-white" />
					</div>
				) : scriptStatus === 'error' ? ( // Error state
					<div className="flex aspect-video w-full items-center justify-center bg-black text-white">
						Failed to initialize video player
					</div>
				) : (
					// Default state
					<div id={playerDomId} className="aspect-video w-full bg-black" />
				)}
			</div>
		</div>
	);
};

export default JWPlayer;
