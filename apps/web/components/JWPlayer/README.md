# JWPlayer Component

The JWPlayer component provides a wrapper around the JW Player JavaScript API, handling script loading, player initialization, and event handling. It supports both direct video URLs and playlists, with configurable options for autoplay, aspect ratio, and advertising.

## Features

- Dynamic script loading with Next.js Script component
- Playlist support
- Event handling
- Responsive design
- Explicit loading and error states with UI feedback
- Efficient cleanup on unmount

## Usage

```tsx
import { JWPlayer } from "@/components/JWPlayer";
import type { PlaylistItem } from "@/components/JWPlayer/types/playlist";

// Create a playlist
const playlist: PlaylistItem[] = [
	{
		file: "https://cdn.jwplayer.com/videos/example-video.mp4",
		image: "https://cdn.jwplayer.com/thumbs/example-video.jpg",
		title: "Example Video",
		description: "This is an example video",
	},
];

// Render the player
import { createLogger } from "@repo/logger";

const VideoPlayer = () => (
	<JWPlayer
		playlist={playlist}
		setupParams={{
			playerId: "IdNDrRWQ", // Your JW Player library ID
		}}
		options={{
			aspectratio: "16:9",
			autostart: false,
		}}
		events={[
			{
				eventName: "ready",
				callback: (event, player) => {
					const logger = createLogger('VideoPlayer');
					logger.info("Player is ready");
				},
			},
			{
				eventName: "play",
				callback: (event, player) => {
					const logger = createLogger('VideoPlayer');
					logger.info("Video started playing");
				},
			},
		]}
	/>
);
```

## Props

| Prop          | Type                   | Description                      |
| ------------- | ---------------------- | -------------------------------- |
| `playlist`    | `PlaylistItem[]`       | Array of videos to play          |
| `options`     | `OptionalSetupOptions` | Optional JW Player setup options |
| `setupParams` | `SetupParams`          | Parameters for player setup      |
| `events`      | `PlayerEvents`         | Event handlers for player events |

### PlaylistItem

```typescript
interface PlaylistItem {
	file: string; // URL to the video file
	image?: string; // URL to the thumbnail image
	title?: string; // Video title
	description?: string; // Video description
	tracks?: Track[]; // Captions, chapters, or thumbnails
	// Additional properties supported by JW Player
}
```

### SetupParams

```typescript
interface SetupParams {
	playerId?: string; // JW Player library ID (default: 'IdNDrRWQ')
	// Additional setup parameters
}
```

## Logging and Debugging

The JWPlayer component uses the `@repo/logger` package for consistent logging across the application:

```typescript
import { createLogger } from "@repo/logger";

const logger = createLogger('JWPlayer');

// Log information
logger.info("Player initialized", { playerId });

// Log warnings
logger.warn("Potential issue detected", { details });

// Log errors
logger.error("Error initializing player", error);

// Log lifecycle events
logger.lifecycle("Registered event handler for play");
```

The logging utility provides environment-aware output control through the DEBUG environment variable:

```bash
# Enable all JWPlayer logs
DEBUG=wx-next:JWPlayer:* pnpm dev

# Enable only error logs
DEBUG=wx-next:*:error pnpm dev

# Enable specific log levels
DEBUG=wx-next:JWPlayer:lifecycle pnpm dev
```

## Architecture

The JWPlayer component uses several key patterns:

### 1. Next.js Script Component for Loading

The component uses Next.js Script component to load the JW Player script, providing better control over loading states:

```typescript
<Script
  src={`https://cdn.jwplayer.com/libraries/${playerId}.js`}
  strategy="afterInteractive"
  onLoad={() => setScriptStatus('ready')}
  onError={() => setScriptStatus('error')}
/>
```

### 2. Script Status Management

The component explicitly tracks script loading status with state:

```typescript
const [scriptStatus, setScriptStatus] = useState<"loading" | "ready" | "error">(
	hasFile ? "loading" : "error",
);
```

### 3. Player Instance Management

The component manages the player instance using refs to prevent memory leaks:

```typescript
const playerInstanceRef = useRef<JWPlayerInstance | null>(null);
const isInitializedRef = useRef(false);
```

### 4. Explicit UI States

The component provides explicit UI feedback for different states:

```typescript
{scriptStatus === 'loading' && hasFile ? ( // Loading state
  <div className="w-full aspect-video bg-black flex items-center justify-center">
    <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-white" />
  </div>
) : (scriptStatus === 'error') ? ( // Error state
  <div className="w-full aspect-video bg-black flex items-center justify-center text-white">
    Failed to initialize video player
  </div>
) : ( // Default state
  <div
    id={playerDomId}
    className="w-full aspect-video bg-black"
  />
)}
```

## Integration with Video Block

The JWPlayer component is used by the Video block (`apps/web/blocks/Video/VideoBlock.tsx`) to render videos in the PayloadCMS system. The Video block:

1. Processes video data from PayloadCMS
2. Generates thumbnails using the `getVideoThumbnail` utility
3. Builds a playlist using the `buildJwPlaylist` utility
4. Renders the JWPlayer component with the processed data

## Testing

The JWPlayer component has unit tests in `JWPlayer.test.tsx` that verify:

1. Player container rendering
2. Script loading and initialization
3. Player instance cleanup and reinitialization
4. Event handler registration
5. Loading and error state rendering

To run the tests:

```bash
pnpm --filter web test -- -t "JWPlayer"
```

## Implementation Details

### Initialization Logic

The component uses a callback function to initialize the player, which is triggered when the script is loaded:

```typescript
const initializePlayer = useCallback(() => {
	if (
		typeof window === "undefined" ||
		!("jwplayer" in window) ||
		scriptStatus !== "ready" ||
		!hasFile
	)
		return;

	// Remove existing instance if it exists
	if (playerInstanceRef.current) {
		try {
			playerInstanceRef.current.remove();
		} catch (e) {
			const logger = createLogger('JWPlayer');
			logger.error("Error removing existing player", e);
		}
	}

	const playerInstance = window.jwplayer(playerDomId);
	playerInstanceRef.current = playerInstance;
	isInitializedRef.current = true;

	// Setup player
	playerInstance.setup(configProps);

	// Register event handlers
	events.forEach(({ eventName, callback }) => {
		playerInstance.on(eventName as EventName, (event: unknown) => {
			callback(event, playerInstance);
		});
		const logger = createLogger('JWPlayer');
		logger.lifecycle(`Registered event handler for ${eventName}`);
	});
}, [scriptStatus, hasFile, playerDomId, configProps, events]);
```

### Script Loading Detection

The component checks if the script is already loaded when mounting:

```typescript
useEffect(() => {
	if (typeof window !== "undefined" && "jwplayer" in window && hasFile) {
		setScriptStatus("ready");
	}
}, [hasFile]);
```
