// https://docs.jwplayer.com/players/reference/javascript-player-api-introduction

import { User } from '@/user/hooks/useUser';
import type { JWPlayerInstance } from './player';
import { useTrackVideoEvent } from '@/analytics/mparticle/hooks/useTrackVideoEvent';

// TODO: verify that all these events actually take an event as their first argument
export interface PlayerEvents {
	// Setup
	ready?: (event: unknown, player: JWPlayerInstance) => void;

	// Advertising
	adComplete?: (event: unknown, player: JWPlayerInstance) => void;
	adError?: (error: Error, player: JWPlayerInstance) => void;
	adImpression?: (event: unknown, player: JWPlayerInstance) => void;
	adPlay?: (event: unknown, player: JWPlayerInstance) => void;
	adStarted?: (event: unknown, player: JWPlayerInstance) => void;
	adRequest?: (event: unknown, player: JWPlayerInstance) => void;
	beforePlay?: (event: unknown, player: JWPlayerInstance) => void; // preroll processing, currently handled in setup options in the VideoBlock
	adSkipped?: (event: unknown, player: JWPlayerInstance) => void;

	// Playback
	play?: (event: unknown, player: JWPlayerInstance) => void;
	playAttempt?: (event: unknown, player: JWPlayerInstance) => void;
	pause?: (event: unknown, player: JWPlayerInstance) => void;
	complete?: (event: unknown, player: JWPlayerInstance) => void;
	error?: (error: Error, player: JWPlayerInstance) => void;
	firstFrame?: (event: unknown, player: JWPlayerInstance) => void;
	setupError?: (error: Error, player: JWPlayerInstance) => void;

	// Seek
	time: (event: unknown, player: JWPlayerInstance) => void;
	seek: (event: unknown, player: JWPlayerInstance) => void;

	// Playlist
	playlistItem?: (event: unknown, player: JWPlayerInstance) => void;

	// Resize
	fullscreen?: (event: unknown, player: JWPlayerInstance) => void;

	// Viewability
	viewable?: (event: unknown, player: JWPlayerInstance) => void;
}

export type EventName = keyof PlayerEvents;

export interface VideoEvent {
	eventName: EventName;
	callback: (props: CallbackProps) => void;
}

export interface CallbackProps {
	event: Record<string, unknown>;
	player: JWPlayerInstance;

	// passed in from VideoBlock
	isFirstPlayRef: { current: boolean };
	user: User;
	trackVideoEvent: ReturnType<typeof useTrackVideoEvent>;
}
