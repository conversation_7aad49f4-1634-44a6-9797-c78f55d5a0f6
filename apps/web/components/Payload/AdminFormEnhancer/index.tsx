'use client';

import React, { useEffect } from 'react';

interface AdminFormEnhancerProps {
	collection?: string;
	id?: string;
}

/**
 * AdminFormEnhancer - Component to enhance PayloadCMS admin forms
 * 
 * This component automatically detects and fixes common form state issues:
 * 1. Empty forms when draft data exists
 * 2. Stale form data after navigation
 * 3. Draft state inconsistencies
 * 
 * It integrates seamlessly with PayloadCMS admin interface.
 */
export const AdminFormEnhancer: React.FC<AdminFormEnhancerProps> = ({
	collection,
	id,
}) => {
	useEffect(() => {
		// Only run in PayloadCMS admin context
		if (!window.location.pathname.includes('/payload/admin')) {
			return;
		}

		let retryCount = 0;
		const maxRetries = 3;

		const enhanceForm = () => {
			try {
				// Check if we're on an edit page
				const isEditPage = window.location.pathname.includes('/edit/') || 
								   window.location.pathname.includes('/create');
				
				if (!isEditPage) return;

				// Look for PayloadCMS form
				const form = document.querySelector('form[data-payload-form]') || 
							 document.querySelector('form');
				
				if (!form) {
					// Retry if form not found yet
					if (retryCount < maxRetries) {
						retryCount++;
						setTimeout(enhanceForm, 1000);
					}
					return;
				}

				// Check if form has the draft reload functionality already
				if (form.hasAttribute('data-enhanced')) {
					return;
				}

				// Mark form as enhanced
				form.setAttribute('data-enhanced', 'true');

				// Add form state monitoring
				addFormStateMonitoring(form);

				// Add draft data recovery
				addDraftRecovery(form);

			} catch (error) {
				console.warn('AdminFormEnhancer: Error enhancing form:', error);
			}
		};

		// Initial enhancement
		setTimeout(enhanceForm, 500);

		// Re-enhance on navigation (for SPA behavior)
		const handleNavigation = () => {
			setTimeout(enhanceForm, 500);
		};

		// Listen for URL changes
		window.addEventListener('popstate', handleNavigation);

		// Listen for PayloadCMS navigation events
		const observer = new MutationObserver((mutations) => {
			mutations.forEach((mutation) => {
				if (mutation.type === 'childList') {
					// Check if new form elements were added
					const addedNodes = Array.from(mutation.addedNodes);
					const hasFormElements = addedNodes.some(node => 
						node.nodeType === Node.ELEMENT_NODE &&
						(node as Element).querySelector('form, input, textarea')
					);
					
					if (hasFormElements) {
						setTimeout(enhanceForm, 200);
					}
				}
			});
		});

		observer.observe(document.body, {
			childList: true,
			subtree: true
		});

		return () => {
			window.removeEventListener('popstate', handleNavigation);
			observer.disconnect();
		};
	}, [collection, id]);

	return null; // This component doesn't render anything visible
};

// Helper function to add form state monitoring
function addFormStateMonitoring(form: Element) {
	const checkFormEmpty = () => {
		const inputs = form.querySelectorAll('input, textarea, select');
		let hasContent = false;

		inputs.forEach((input) => {
			const element = input as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
			if (element.name && !element.name.startsWith('_') && element.value.trim()) {
				hasContent = true;
			}
		});

		// Check for rich text content
		const richTextElements = form.querySelectorAll('[data-lexical-editor]');
		richTextElements.forEach((element) => {
			if (element.textContent && element.textContent.trim()) {
				hasContent = true;
			}
		});

		return !hasContent;
	};

	// Store original form state
	let initialFormEmpty = checkFormEmpty();
	
	// Check periodically for form state changes
	const intervalId = setInterval(() => {
		const currentlyEmpty = checkFormEmpty();
		
		// If form became empty unexpectedly, show recovery option
		if (!initialFormEmpty && currentlyEmpty) {
			showRecoveryNotification();
			clearInterval(intervalId);
		}
	}, 2000);

	// Clean up interval after 30 seconds
	setTimeout(() => clearInterval(intervalId), 30000);
}

// Helper function to add draft recovery functionality
function addDraftRecovery(form: Element) {
	// Add a subtle recovery button to the form
	const recoveryButton = document.createElement('button');
	recoveryButton.type = 'button';
	recoveryButton.textContent = '🔄 Reload Draft Data';
	recoveryButton.style.cssText = `
		position: fixed;
		bottom: 20px;
		right: 20px;
		background: #007bff;
		color: white;
		border: none;
		border-radius: 4px;
		padding: 8px 12px;
		font-size: 12px;
		cursor: pointer;
		z-index: 1000;
		display: none;
		box-shadow: 0 2px 8px rgba(0,0,0,0.2);
	`;

	recoveryButton.onclick = () => {
		// Force reload with draft parameter
		const url = new URL(window.location.href);
		url.searchParams.set('draft', 'true');
		url.searchParams.set('_t', Date.now().toString());
		window.location.href = url.toString();
	};

	document.body.appendChild(recoveryButton);

	// Show button when needed
	(window as any).showDraftRecovery = () => {
		recoveryButton.style.display = 'block';
		setTimeout(() => {
			recoveryButton.style.display = 'none';
		}, 10000); // Hide after 10 seconds
	};
}

// Helper function to show recovery notification
function showRecoveryNotification() {
	// Check if notification already exists
	if (document.querySelector('.form-recovery-notification')) {
		return;
	}

	const notification = document.createElement('div');
	notification.className = 'form-recovery-notification';
	notification.style.cssText = `
		position: fixed;
		top: 20px;
		right: 20px;
		background: #fff3cd;
		border: 1px solid #ffeaa7;
		border-radius: 4px;
		padding: 12px 16px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
		z-index: 1000;
		max-width: 300px;
		font-size: 14px;
	`;

	notification.innerHTML = `
		<div style="font-weight: bold; margin-bottom: 8px; color: #856404;">
			Form Data Lost
		</div>
		<div style="margin-bottom: 12px; color: #856404;">
			The form appears to have lost its data. This can happen after navigation.
		</div>
		<div style="display: flex; gap: 8px;">
			<button onclick="window.location.reload()" style="
				background: #007bff;
				color: white;
				border: none;
				border-radius: 4px;
				padding: 6px 12px;
				font-size: 12px;
				cursor: pointer;
			">Reload Page</button>
			<button onclick="this.closest('.form-recovery-notification').remove()" style="
				background: #6c757d;
				color: white;
				border: none;
				border-radius: 4px;
				padding: 6px 12px;
				font-size: 12px;
				cursor: pointer;
			">Dismiss</button>
		</div>
	`;

	document.body.appendChild(notification);

	// Auto-remove after 15 seconds
	setTimeout(() => {
		if (notification.parentNode) {
			notification.remove();
		}
	}, 15000);
}

export default AdminFormEnhancer;
