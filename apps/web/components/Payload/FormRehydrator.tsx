'use client';

import { useFormRehydration } from '@/hooks/useFormRehydration';

/**
 * FormRehydrator - Simple component to fix PayloadCMS form population issues
 * 
 * This component automatically detects when form data isn't properly loaded
 * and forces a rehydration. It's designed to be lightweight and non-intrusive.
 * 
 * Usage: Add this component to any PayloadCMS admin page where form
 * population issues occur.
 */
export const FormRehydrator: React.FC = () => {
  useFormRehydration();
  
  // This component doesn't render anything visible
  return null;
};
