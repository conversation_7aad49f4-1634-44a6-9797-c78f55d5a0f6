'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';

interface FormStateManagerProps {
	collection: string;
	id: string;
}

/**
 * FormStateManager - Enhanced component to handle PayloadCMS form state issues
 *
 * This component addresses multiple draft-related problems:
 * 1. Form values not populating when returning to edit page after navigation
 * 2. Draft data not loading properly after creating a draft
 * 3. Old draft versions appearing instead of current state
 * 4. Form state loss when navigating between admin pages
 *
 * Solutions implemented:
 * 1. Monitors page visibility and form state
 * 2. Detects when form is empty but draft data exists
 * 3. Provides automatic and manual form reload options
 * 4. Handles draft vs published state conflicts
 */
export const FormStateManager: React.FC<FormStateManagerProps> = ({
	collection,
	id,
}) => {
	const router = useRouter();
	const [showFormReloadWarning, setShowFormReloadWarning] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [formState, setFormState] = useState<'empty' | 'populated' | 'checking'>('checking');

	// Check if form fields are populated
	const checkFormState = useCallback(() => {
		try {
			// Look for PayloadCMS form fields in the DOM
			const formFields = document.querySelectorAll(
				'input[name], textarea[name], select[name]'
			);
			
			let populatedFields = 0;
			let totalFields = 0;

			formFields.forEach((field) => {
				const input = field as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
				const name = input.name;
				
				// Skip system fields and empty names
				if (!name || name.startsWith('_') || name === 'id') return;
				
				totalFields++;
				
				// Check if field has a value
				if (input.value && input.value.trim() !== '') {
					populatedFields++;
				}
			});

			// Consider form populated if more than 20% of fields have values
			// or if there are key content fields populated
			const hasTitle = document.querySelector('input[name="title"]') as HTMLInputElement;
			const hasContent = document.querySelector('textarea[name*="body"], div[data-lexical-editor]');
			
			const isPopulated = populatedFields > 0 && (
				populatedFields / Math.max(totalFields, 1) > 0.2 ||
				(hasTitle && hasTitle.value.trim() !== '') ||
				hasContent
			);

			setFormState(isPopulated ? 'populated' : 'empty');
			return isPopulated;
		} catch (error) {
			console.warn('Error checking form state:', error);
			setFormState('populated'); // Assume populated to avoid false positives
			return true;
		}
	}, []);

	// Force reload the current page to refresh form data
	const reloadFormData = useCallback(async () => {
		setIsLoading(true);
		try {
			// Add a timestamp to force cache bust
			const currentUrl = new URL(window.location.href);
			currentUrl.searchParams.set('_reload', Date.now().toString());
			
			// Use router.replace to maintain history
			router.replace(currentUrl.toString());
		} catch (error) {
			console.error('Failed to reload form data:', error);
			// Fallback to hard refresh
			window.location.reload();
		}
	}, [router]);

	// Load draft data specifically
	const loadDraftData = useCallback(async () => {
		setIsLoading(true);
		try {
			const currentUrl = new URL(window.location.href);
			
			// Ensure we're loading draft data
			currentUrl.searchParams.set('draft', 'true');
			currentUrl.searchParams.set('_reload', Date.now().toString());
			
			router.replace(currentUrl.toString());
		} catch (error) {
			console.error('Failed to load draft data:', error);
			setIsLoading(false);
		}
	}, [router]);

	// Check for draft data availability
	const checkDraftAvailability = useCallback(async () => {
		try {
			const response = await fetch(
				`/api/payload/${collection}/${id}?draft=true&depth=0`,
				{
					credentials: 'include',
					headers: {
						'Accept': 'application/json',
					},
				}
			);

			if (response.ok) {
				const draftDoc = await response.json();
				return draftDoc && Object.keys(draftDoc).length > 0;
			}
		} catch (error) {
			console.warn('Failed to check draft availability:', error);
		}
		return false;
	}, [collection, id]);

	// Main effect to monitor form state and page visibility
	useEffect(() => {
		let timeoutId: NodeJS.Timeout;

		const handleVisibilityChange = async () => {
			if (document.visibilityState === 'visible') {
				// Wait a bit for the page to fully load
				timeoutId = setTimeout(async () => {
					const isFormPopulated = checkFormState();
					
					if (!isFormPopulated) {
						// Check if draft data exists
						const hasDraftData = await checkDraftAvailability();
						
						if (hasDraftData) {
							setShowFormReloadWarning(true);
						}
					}
				}, 1000);
			}
		};

		// Initial check after component mounts
		timeoutId = setTimeout(async () => {
			const isFormPopulated = checkFormState();
			
			if (!isFormPopulated) {
				const hasDraftData = await checkDraftAvailability();
				if (hasDraftData) {
					setShowFormReloadWarning(true);
				}
			}
		}, 2000);

		// Listen for visibility changes
		document.addEventListener('visibilitychange', handleVisibilityChange);

		// Also check when URL changes (for SPA navigation)
		const handlePopState = () => {
			setTimeout(checkFormState, 500);
		};
		window.addEventListener('popstate', handlePopState);

		return () => {
			if (timeoutId) clearTimeout(timeoutId);
			document.removeEventListener('visibilitychange', handleVisibilityChange);
			window.removeEventListener('popstate', handlePopState);
		};
	}, [collection, id, checkFormState, checkDraftAvailability]);

	const dismissWarning = () => {
		setShowFormReloadWarning(false);
	};

	if (!showFormReloadWarning) {
		return null;
	}

	return (
		<div
			style={{
				position: 'fixed',
				top: '20px',
				right: '20px',
				backgroundColor: '#e3f2fd',
				border: '1px solid #2196f3',
				borderRadius: '4px',
				padding: '12px 16px',
				boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
				zIndex: 1000,
				maxWidth: '400px',
			}}
		>
			<div
				style={{ marginBottom: '8px', fontWeight: 'bold', color: '#1976d2' }}
			>
				Form Data Missing
			</div>
			<div style={{ marginBottom: '12px', fontSize: '14px', color: '#1976d2' }}>
				The form appears empty but draft data exists. This may happen after
				navigating away and returning to the edit page.
			</div>
			<div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
				<button
					onClick={loadDraftData}
					disabled={isLoading}
					style={{
						backgroundColor: '#2196f3',
						color: 'white',
						border: 'none',
						borderRadius: '4px',
						padding: '6px 12px',
						fontSize: '12px',
						cursor: isLoading ? 'not-allowed' : 'pointer',
						opacity: isLoading ? 0.6 : 1,
					}}
				>
					{isLoading ? 'Loading...' : 'Load Draft Data'}
				</button>
				<button
					onClick={reloadFormData}
					disabled={isLoading}
					style={{
						backgroundColor: '#ff9800',
						color: 'white',
						border: 'none',
						borderRadius: '4px',
						padding: '6px 12px',
						fontSize: '12px',
						cursor: isLoading ? 'not-allowed' : 'pointer',
						opacity: isLoading ? 0.6 : 1,
					}}
				>
					{isLoading ? 'Reloading...' : 'Reload Page'}
				</button>
				<button
					onClick={dismissWarning}
					style={{
						backgroundColor: '#6c757d',
						color: 'white',
						border: 'none',
						borderRadius: '4px',
						padding: '6px 12px',
						fontSize: '12px',
						cursor: 'pointer',
					}}
				>
					Dismiss
				</button>
			</div>
		</div>
	);
};
