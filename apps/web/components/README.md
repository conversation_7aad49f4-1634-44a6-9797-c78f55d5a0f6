# Component Patterns

This document outlines the various component patterns used throughout the wx-next project. Understanding these patterns will help you create and use components effectively within the application.

## Table of Contents

- [Component Types](#component-types)
  - [Payload Blocks](#payload-blocks)
  - [Payload Admin UI Components](#payload-admin-ui-components)
  - [Frontend UI Components](#frontend-ui-components)
  - [Higher-Order Components](#higher-order-components)
  - [Shared UI Components](#shared-ui-components)
- [Best Practices](#best-practices)
- [Directory Structure](#directory-structure)

## Component Types

### Payload Blocks

Payload blocks are content components used within the PayloadCMS system. They allow content editors to add structured content to pages and articles.

**Location**: `apps/web/blocks/`

**Structure**:

- Each block is in its own directory (e.g., `Image/`, `YouTube/`)
- Each block typically consists of:
  - `config.ts`: Defines the block configuration schema, fields, and admin component
  - `[BlockName]Block.tsx`: React component for frontend rendering
  - `[BlockName]AdminBlock.tsx`: React component for the admin interface
  - `field.ts`: Field definitions for the block

**Example**:

```typescript
// config.ts
import { imageFields } from "@/blocks/Image/field";
import type { Block } from "payload";

const ImageBlockConfig: Block = {
	slug: "Image",
	interfaceName: "ImageBlockConfig",
	labels: {
		singular: "Image Block",
		plural: "Image Blocks",
	},
	fields: imageFields,
	admin: {
		components: {
			Block: {
				path: "@/blocks/Image/ImageAdminBlock#default",
			},
		},
	},
};

export default ImageBlockConfig;
```

**Registration**:
Blocks are registered in `apps/web/blocks/index.ts` and used in the `Blocks.tsx` component.

### Payload Admin UI Components

These components are specifically designed for the PayloadCMS admin interface. They provide custom UI elements for content editors.

**Location**: `apps/web/components/Payload/AdminBlock/` and other admin-specific components

**Characteristics**:

- Use Payload's UI components and hooks
- Focus on providing a good editing experience
- Often use the `useAllFormFields` hook to access form data

**Example**:

```typescript
// AdminBlock/index.tsx
'use client';

import React, { FC } from 'react';
import {
  BlockEditButton,
  BlockRemoveButton,
} from '@payloadcms/richtext-lexical/client';

interface AdminBlockProps {
  name: string;
  children?: React.ReactNode;
}

const AdminBlock: FC<AdminBlockProps> = ({ name, children }) => {
  return (
    <div className="p-5 rounded border border-gray-300 flex w-full min-w-xl flex-col">
      <h4 className="flex justify-between items-center">
        <span className="flex-grow text-left">{name}</span>
        <BlockEditButton />
        <BlockRemoveButton />
      </h4>
      <div className="font-bold mb-2">
        <div className="pt-3">{children}</div>
      </div>
    </div>
  );
};

export default AdminBlock;
```

### Frontend UI Components

These components are used in the frontend application for end users. They render content and provide interactive features.

**Location**: Various directories in `apps/web/components/`

**Characteristics**:

- Marked with `'use client'` directive for Next.js client components
- Use SWR for data fetching from the DAL (Data Access Layer)
- Handle loading, error, and success states
- Use TailwindCSS for styling
- Use custom hooks for shared logic

**Data Fetching with SWR**:

Client components use SWR (stale-while-revalidate) for data fetching, which provides automatic caching, revalidation, and state management. This replaces the direct use of tryCatch in client components.

```typescript
// DailyForecast/index.tsx (simplified)
"use client";

import React from "react";
import { getDailyForecast } from "@repo/dal/weather/forecast/daily";
import { Units, ForecastDuration } from "@repo/dal/weather/types";
import useSWR from "swr";
import { useLocationSource } from "@/hooks/useLocationSource";

export const DailyForecast: React.FC<DailyForecastProps> = ({ location }) => {
 // Use the location hook to handle location logic
 const { effectiveLocation, isLocationLoading } = useLocationSource({
  location,
 });

 // SWR fetcher function for forecast data
 const forecastFetcher = () => {
  if (!effectiveLocation) return null;
  return getDailyForecast({
   geocode: effectiveLocation.geocode,
   units: Units.IMPERIAL,
   language: "en-US",
   duration: ForecastDuration.THREE_DAY,
  });
 };

 // Use SWR for data fetching
 const {
  data: dailyForecast,
  error,
  isLoading: isForecastLoading,
 } = useSWR(
  effectiveLocation ? ["daily-forecast", effectiveLocation.geocode] : null,
  forecastFetcher,
  {
   revalidateOnFocus: false,
   dedupingInterval: 60000, // 1 minute
  },
 );

 // Combine loading states
 const isLoading = isForecastLoading || isLocationLoading;

 if (isLoading) {
  return <LoadingState />;
 }

 if (error || !dailyForecast) {
  return <ErrorState error={error} />;
 }

 return <SuccessState data={dailyForecast} location={effectiveLocation} />;
};
```

**Using DAL Transformation Utilities**:

The DAL provides utilities for transforming API responses, particularly for compact data formats:

```typescript
// Example of using transformation utilities in a component
"use client";

import React from "react";
import { fromCompactResponse, createTransformer } from "@repo/dal/utils/http/transformers";
import useSWR from "swr";

// Example 1: Using the generic transformer
const SimpleTransformExample = () => {
  const { data } = useSWR("api/compact-data", async () => {
    const response = await fetch("api/compact-data");
    const compactData = await response.json();
    // Transform compact data to array of objects
    return fromCompactResponse(compactData);
  });

  return <div>{/* Render transformed data */}</div>;
};

// Example 2: Using the createTransformer utility for more complex transformations
const ComplexTransformExample = () => {
  // Create a custom transformer with post-processing
  const transformWeatherData = createTransformer<WeatherResponse, WeatherItem>(
    (item, index, response) => ({
      ...item,
      // Add default values
      temperature: item.temperature ?? "N/A",
      condition: item.condition ?? "Unknown",
      // Handle nested data
      alerts: response.alertsArray?.[index] ?? [],
      // Format data
      formattedTime: formatTime(item.time),
    }),
  );

  const { data } = useSWR("api/weather-data", async () => {
    const response = await fetch("api/weather-data");
    const rawData = await response.json();
    // Apply the custom transformer
    return transformWeatherData(rawData);
  });

  return <div>{/* Render transformed weather data */}</div>;
};
```

**Domain-Specific Import Patterns**:

For better tree-shaking and more explicit dependencies, use domain-specific imports:

```typescript
// Weather domain imports
import { getDailyForecast } from "@repo/dal/weather/forecast/daily";
import { getCurrentObservations } from "@repo/dal/weather/current/observations";
import { getWeatherAlerts } from "@repo/dal/weather/alerts/headlines";

// Location domain imports
import { getLocationPointByGeocode } from "@repo/dal/locations/point";
import { getLocationsByQuery } from "@repo/dal/locations/search";

// Content domain imports
import { getArticlesByCollectionName } from "@repo/dal/content/articles";
import { getVideosByCollectionName } from "@repo/dal/content/videos";

// Types imports
import type { LocationPointResponse } from "@repo/dal/locations/types";
import type { DailyForecastItem } from "@repo/dal/weather/forecast/types";
```

**SWR Configuration Options**:

- `revalidateOnFocus`: Whether to revalidate when window gets focused
- `revalidateOnReconnect`: Whether to revalidate when browser regains network connection
- `refreshInterval`: Polling interval (in milliseconds)
- `dedupingInterval`: Dedupe requests with the same key in this time span
- `fallbackData`: Initial data to use before loading
- `onSuccess`: Callback function when a fetch is successful
- `onError`: Callback function when a fetch fails

**Benefits of SWR**:

- Automatic revalidation on specific events
- Built-in loading and error states
- Caching to prevent redundant API calls
- Deduplication of requests
- Consistent data fetching pattern across components

**Icon System**:

The project uses a specific system for handling icons, differentiating between dynamic weather icons and general UI icons.

- **Weather-Specific Icons (`WxIcon`)**:

  - For dynamic weather conditions (e.g., sunny, cloudy, alerts), use the `<WxIcon />` component from `@repo/icons/WxIcon`.
  - It takes an `iconCode` (and optional `iconCodeExtend` for alerts) to render the correct symbol.
  - Props include `size`, `iconTheme`, `className`, `title`, `desc`.
  - Use `<NoData />` from `@repo/icons/Data';
  - Example:

    ```tsx
    import { WxIcon } from '@repo/icons/WxIcon';
    import { NoData } from '@repo/icons/Data';

    // General weather condition
    <WxIcon iconCode={32} size="lg" /> {/* Example: Sunny */}

    // Fallback example
    {weatherData?.iconCode !== undefined && (ICON_CODE_MAP[weatherData.iconCode] || 'na') !== 'na' ? (
      <WxIcon iconCode={weatherData.iconCode} size="sm" />
    ) : (
      <NoData size="sm" aria-label="Weather data not available" />
    )}
    ```

- **General UI Icons (Direct Import)**:

  - For static UI icons (e.g., avatars, navigation arrows, settings), import the specific React component directly from its category within `@repo/icons`.
  - These are standard SVGR components and accept common SVG/React props.
  - Example:

    ```tsx
    import { UserAvatar } from '@repo/icons/Avatar';
    import { ChevronDown } from '@repo/icons/Navigation';

    <UserAvatar className="h-8 w-8" />
    <ChevronDown className="inline h-4 w-4 ml-1" />
    ```

Refer to `docs/svg-loading-options.md` for more detailed documentation on the project's icon system and `apps/web/app/(web)/test/icons/page.tsx` for usage examples.

### Custom Hooks for Shared Logic

Custom hooks are used to extract and share logic between components. They follow the React hooks pattern and allow for better separation of concerns and code reuse.

**Location**: `apps/web/hooks/`

#### Location Source Hook Pattern

The `useLocationSource` hook is a key example of this pattern, abstracting the complex location handling logic that was previously duplicated across weather components.

```typescript
// apps/web/hooks/useLocationSource.ts (simplified)
"use client";

import { useState, useEffect } from "react";
import { useAtomValue } from "jotai";
import useSWR from "swr";
import { userLocationsAtom } from "@/user/atoms/preferences/location";
import { geoipGeocode } from "@/atoms/geolocation";
import { getLocationPointByGeocode } from "@repo/dal/locations/point";
import type { LocationPoint } from "@repo/dal/locations/types";

export function useLocationSource({
	location,
}: UseLocationSourceProps): UseLocationSourceResult {
	// State for the effective location
	const [effectiveLocation, setEffectiveLocation] = useState(location);

	// Get favorite locations from atom
	const favoriteLocations = useAtomValue(userLocationsAtom);

	// Get geoip geocode from atom
	const geoipGeocodeValue = useAtomValue(geoipGeocode);

	// Fetch location data for favorites and geoip
	// ...

	// Determine which location to use based on priority
	useEffect(
		() => {
			// 1. Location from props (highest priority)
			if (location) {
				setEffectiveLocation(location);
				return;
			}

			// 2. First favorite location (if available)
			if (favoriteLocationData && favoriteGeocode) {
				setEffectiveLocation({
					displayName: favoriteLocationData.location.displayName,
					adminDistrict: favoriteLocationData.location.adminDistrict,
					geocode: favoriteGeocode,
				});
				return;
			}

			// 3. GeoIP location from geoipGeocode atom (lowest priority)
			if (geoipLocationData && geoipGeocodeValue) {
				setEffectiveLocation({
					displayName: geoipLocationData.location.displayName,
					adminDistrict: geoipLocationData.location.adminDistrict,
					geocode: geoipGeocodeValue,
				});
				return;
			}

			// Fallback if we have geocode but fetch failed
			if (geoipLocationError && geoipGeocodeValue) {
				setEffectiveLocation({
					displayName: "Current Location",
					adminDistrict: "",
					geocode: geoipGeocodeValue,
				});
				return;
			}
		},
		[
			/* dependencies */
		],
	);

	return {
		effectiveLocation,
		isLocationLoading,
		locationError,
	};
}
```

**Usage in Components**:

```typescript
// In a weather component
const { effectiveLocation, isLocationLoading } = useLocationSource({
	location, // From props (highest priority)
});

// Now use effectiveLocation for data fetching
const { data, error, isLoading } = useSWR(
	effectiveLocation ? ["weather-data", effectiveLocation.geocode] : null,
	() => fetchWeatherData(effectiveLocation.geocode),
);
```

**Benefits of the Location Source Hook**:

1. **Reduced Duplication**: Eliminates duplicated location handling logic across components
2. **Consistent Behavior**: Ensures all components handle location sources with the same priority
3. **Separation of Concerns**: Components focus on rendering weather data, not location resolution
4. **Maintainability**: Location logic changes only need to be made in one place
5. **Type Safety**: Shared location data structure ensures consistency

**Location Priority Order**:

The hook implements a clear priority order for location sources:

1. Direct location prop (highest priority) - typically from URL parameters
2. Favorite location from favorites atom - from user preferences
3. GeoIP location (lowest priority) - automatic location detection

### Higher-Order Components

Higher-order components (HOCs) wrap other components to provide additional functionality or data.

**Note**: While HOCs are still used in some parts of the codebase, the preferred approach for location-based components is to use the `useLocationSource` hook directly in client components. This provides better type safety, simpler component structure, and avoids the complexity of server-to-client data passing.

**Characteristics**:

- Take a component as input and return an enhanced component
- Inject props or behavior into the wrapped component
- Often handle cross-cutting concerns like data fetching

**Example of a Generic HOC**:

```typescript
// withErrorBoundary.tsx (example)
import React from 'react';
import ErrorBoundary from '@/components/ErrorBoundary';

export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
) {
  return function WithErrorBoundaryComponent(props: P) {
    return (
      <ErrorBoundary fallback={<div>Something went wrong</div>}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
}
```

**Usage**:

```typescript
// In a page component
const SafeComponent = withErrorBoundary(MyComponent);

export default function Home() {
  return (
    <div>
      <SafeComponent />
    </div>
  );
}
```

### Shared UI Components

These are reusable UI components that can be used across the application. They provide consistent UI elements and behaviors.

**Location**: `packages/ui/src/components/`

**Characteristics**:

- Highly reusable and generic
- Minimal business logic
- Focus on presentation and interaction
- Often use TailwindCSS for styling

**Examples**:

- Button
- Input
- Card
- Form elements

### Media Components

#### JWPlayer Component

The JWPlayer component provides a wrapper around the JW Player JavaScript API, handling script loading, player initialization, and event handling.

**Location**: `apps/web/components/JWPlayer/`

**Features**:

- Dynamic script loading with SWR caching
- Playlist support
- Event handling
- Responsive design
- Loading and error states
- Cleanup on unmount

**Implementation**:

```typescript
// JWPlayer component using SWR for script loading
const {
	data: isScriptLoaded,
	error: scriptError,
	isLoading: isScriptLoading,
} = useSWR(
	hasFile ? ["jwplayer-script", playerId] : null,
	jwPlayerScriptFetcher,
	{
		revalidateOnFocus: false,
		revalidateOnReconnect: false,
		dedupingInterval: Infinity,
		shouldRetryOnError: true,
		errorRetryCount: 2,
		errorRetryInterval: 2000,
	},
);
```

**Integration with Video Block**:

The JWPlayer component is used by the Video block (`apps/web/blocks/Video/VideoBlock.tsx`) to render videos in the PayloadCMS system. See the [Video Block documentation](../blocks/Video/README.md) for more details.

For detailed documentation, see the [JWPlayer README](../components/JWPlayer/README.md).

## State Management with Atoms

This project uses [Jotai](https://jotai.org/) atoms for state management, providing an alternative to React Context and prop drilling. Atoms allow for efficient state sharing between components without the overhead of context providers.

### Server-to-Client Hydration

One of the key advantages of using atoms in this project is the ability to hydrate state from server components to client components:

```typescript
// Server component (e.g., in a page.tsx file)
import { AtomHydrationBoundaries } from '@/components/AtomHydrationBoundaries';
import { getLocationData } from '@/utils/getLocationData';

export default async function Page() {
  // Fetch data on the server
  const locationData = await getLocationData();

  return (
    <>
      {/* Hydrate atoms with server data */}
      <AtomHydrationBoundaries locationData={locationData} />

      {/* Client components can now access the hydrated atoms */}
      <ClientComponent />
    </>
  );
}
```

### Benefits Over Context/Providers

Using atoms instead of context providers offers several advantages:

1. **No Provider Nesting**: Eliminates the "provider hell" problem where multiple contexts lead to deeply nested providers
2. **Selective Re-rendering**: Components only re-render when the specific atoms they use change
3. **Simpler API**: More straightforward API compared to Context/useReducer patterns
4. **Server-Side Hydration**: Easy hydration of server-fetched data to client components
5. **Code Splitting**: Better support for code splitting and lazy loading

### Integration with Components

Components can access atom state in two ways:

1. **Direct atom access**: Client components can read from and write to atoms directly
2. **Server component + atom hydration**: Server components can fetch data, which is then hydrated into atoms for client components to use

#### Example: Using Atoms in Client Components

```typescript
'use client';

import { useAtomValue } from 'jotai';
import { geoIPLocationPointAtom } from '@/atoms/geolocation';
import { useLocationSource } from '@/hooks/useLocationSource';

export const WeatherDisplay = () => {
  // Use the location hook to get location data
  const { effectiveLocation, isLocationLoading } = useLocationSource({});

  if (isLocationLoading || !effectiveLocation) {
    return <div>Loading location data...</div>;
  }

  return (
    <div>
      <h2>Weather for {effectiveLocation.displayName}</h2>
      {/* Component content */}
    </div>
  );
};
```

### Atom Hydration Pattern

The `AtomHydrationBoundaries` component serves as a bridge between server and client state:

1. Server components fetch data and pass it to `AtomHydrationBoundaries`
2. `AtomHydrationBoundaries` uses `useRehydrateAtoms` to initialize and update atoms with this data
3. Client components can then access this data through atoms
4. When props change (e.g., during navigation), atoms are automatically updated with new values

This pattern eliminates the need for complex context providers while maintaining the benefits of server-side data fetching and reactive updates.

#### The useRehydrateAtoms Hook

Unlike standard Jotai hydration which only sets initial values once, the custom `useRehydrateAtoms` hook checks if atom values have changed and updates them accordingly:

```typescript
// AtomHydrationBoundaries/index.tsx (simplified)
const useRehydrateAtoms = (
	atoms: [WritableAtom<unknown, any, unknown>, unknown][],
) => {
	const store = useStore();

	// Create a write-only atom that updates values when they change
	const rehydrateAtoms = useSetAtom(
		useMemo(
			() =>
				atom(null, (_get, set) => {
					for (const [currentAtom, currentValue] of atoms) {
						if (store.get(currentAtom) !== currentValue) {
							// Only update if the value has changed
							set(currentAtom, currentValue);
						}
					}
				}),
			[atoms, store],
		),
	);

	// Run the effect when dependencies change
	useEffect(() => {
		rehydrateAtoms();
	}, [rehydrateAtoms]);
};
```

This approach ensures that atoms stay in sync with changing props, making it ideal for components that receive updated data during client-side navigation or other prop changes.

## Styling Utilities

### Class Name Management

The project uses utilities from `packages/ui/src/lib/utils.ts` for managing Tailwind CSS class names:

#### `twMerge` and `cn`

```typescript
import { cn, twMerge } from "@repo/ui/lib/utils";

// Using cn for conditional classes
const className = cn(
	"base-class",
	isActive && "active-class",
	isFocused ? "focused-class" : "unfocused-class",
);

// Using twMerge directly
const mergedClasses = twMerge("text-blue-500", "text-red-500"); // 'text-red-500' wins
```

- **`twMerge`**: An extended version of `tailwind-merge` that handles Tailwind CSS class conflicts based on specificity
- **`cn`**: A convenience function that combines `clsx` and `twMerge` for conditional class application and merging

For detailed documentation on these utilities, including how to update the configuration for custom Tailwind theme values, see the [UI Utilities documentation](../../packages/ui/src/lib/README.md).

### Class Variance Authority (CVA)

For components with multiple variants, use CVA in combination with `cn`:

```typescript
import { cva } from "class-variance-authority";
import { cn } from "@repo/ui/lib/utils";

const buttonVariants = cva(
  "base-button-classes",
  {
    variants: {
      variant: {
        default: "bg-primary text-white",
        outline: "border border-primary text-primary",
      },
      // More variants...
    }
  }
);

// In your component
<button className={cn(buttonVariants({ variant, size }), className)} />
```

## Best Practices

### Component Creation

1. **Single Responsibility**: Each component should have a single responsibility.
2. **Type Safety**: Use TypeScript interfaces for props and state.
3. **Error Handling**: Handle loading, error, and success states.
4. **Accessibility**: Ensure components are accessible (proper ARIA attributes, keyboard navigation).
5. **Responsive Design**: Use TailwindCSS utilities for responsive design.
6. **Class Name Management**: Use `cn` and `twMerge` from `@repo/ui/lib/utils` for managing Tailwind classes.

### Payload Blocks Structure

1. **Consistent Structure**: Follow the established pattern of config.ts, Block component, and AdminBlock component.
2. **Field Validation**: Add validation to fields to ensure data integrity.
3. **Preview**: Provide a meaningful preview in the admin interface.

### Frontend Components

1. **Data Fetching**: Use SWR with the DAL for client-side data fetching. Use the tryCatch utility for server-side data fetching.
2. **Loading States**: Show loading indicators during data fetching, leveraging SWR's `isLoading` state.
3. **Error States**: Handle and display errors gracefully, using SWR's `error` state.
4. **Client Components**: Mark components that use hooks or browser APIs with 'use client'.
5. **State Management**:
   - For local state, use React's `useState` and `useReducer`
   - For shared state, prefer atoms over context providers
   - For server-hydrated state, use the atom hydration pattern

### Best Practices for Data Fetching

#### ✅ DO: Use SWR for all client-side data fetching

```typescript
// Define a fetcher function
const locationFetcher = (geocode: string) => {
	return getLocationPointByGeocode(geocode);
};

// Use SWR hook with appropriate cache settings
const { data, error, isLoading } = useSWR(
	geocode ? ["location-data", geocode] : null,
	() => (geocode ? locationFetcher(geocode) : null),
	{
		revalidateOnFocus: false,
		dedupingInterval: 300000, // 5 minutes
	},
);
```

#### ❌ DON'T: Make direct API calls in useEffect hooks

```typescript
// Avoid this pattern
useEffect(() => {
	const fetchData = async () => {
		try {
			setIsLoading(true);
			const data = await getLocationPointByGeocode(geocode);
			setData(data);
		} catch (error) {
			setError(error);
		} finally {
			setIsLoading(false);
		}
	};

	fetchData();
}, [geocode]);
```

#### Why use SWR?

- **Consistent Pattern**: Maintains a consistent data fetching approach across components
- **Automatic Cache Management**: Prevents redundant API calls
- **Built-in State Handling**: Provides isLoading and error states automatically
- **Revalidation**: Configurable data revalidation on various events
- **Deduplication**: Prevents duplicate requests during rendering

### Higher-Order Components Best Practices

1. **Prop Forwarding**: Forward all props to the wrapped component.
2. **Display Name**: Set a displayName for debugging purposes.
3. **Composition**: Prefer composition over inheritance.

### Utility Functions

1. **Shared Logic**: Extract repeated logic into utility functions
2. **Single Responsibility**: Each utility function should have a single responsibility
3. **Type Safety**: Use TypeScript for type-safe utility functions
4. **Documentation**: Add JSDoc comments to document parameters and return values
5. **Location**: Place utility functions in the appropriate directory:
   - General utilities: `apps/web/utils/`
   - Domain-specific utilities: In relevant domain directories

## Directory Structure

```text
wx-next/
├── apps/
│   └── web/
│       ├── blocks/                # Payload CMS blocks
│       │   ├── Ad/
│       │   ├── AuthoredBy/
│       │   ├── BuyButton/
│       │   ├── ByLine/
│       │   ├── ContentMedia/
│       │   ├── Image/
│       │   ├── LiveblogEntries/
│       │   ├── Twitter/
│       │   ├── Video/
│       │   ├── YouTube/
│       │   └── index.ts           # Block registration
│       │
│       ├── components/            # Frontend components
│       │   ├── AdminBlock/        # Admin UI components
│       │   ├── Blocks.tsx         # Block renderer
│       │   ├── CurrentConditions/ # Weather components
│       │   ├── DailyForecast/
│       │   ├── FrontendAdminHeader/
│       │   ├── Header/
│       │   ├── Payload/
│       │   └── RichText/          # Rich text renderer
│       │
│       └── ...
│
└── packages/
    └── ui/                       # Shared UI components
        └── src/
            └── components/       # Reusable UI components
                ├── Button.tsx
                └── Input.tsx
```

This structure organizes components by their purpose and usage, making it easier to find and maintain them.
