'use client';

import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@repo/ui/lib/utils';
import { UserSubscriptionTiers } from '@/user/utils/consts';
import {
	AvatarRegisteredPremium,
	AvatarRegisteredPremiumFilled,
	UserRegisteredAdfree,
	UserRegisteredAdfreeFilled,
	UserRegisteredStroke,
	UserRegisteredBackground,
	UserAvatarFilledAlt,
	UserAnonymous,
} from '@repo/icons/Deprecated';
import { BaseIconProps } from '@repo/icons/BaseIcon';

const userAvatarVariants = cva('w-auto', {
	variants: {
		variant: {
			default: 'fill-white text-white',
			brand: 'fill-[#005986] text-[#005986]',
		},
		size: {
			default: 'h-[27px]',
			sm: 'h-[18px]',
			lg: 'h-[56px]',
		},
	},
	defaultVariants: {
		variant: 'default',
		size: 'default',
	},
});

const userAvatarTextVariants = cva('absolute font-medium text-white', {
	variants: {
		variant: {
			default: '',
			premium: '',
			standard: '',
		},
		size: {
			default: '',
			sm: '',
			lg: '',
		},
	},
	defaultVariants: {
		size: 'default',
	},
	compoundVariants: [
		{
			variant: 'premium',
			size: 'sm',
			className: 'top-[2px] text-[8px]',
		},
		{
			variant: 'premium',
			size: 'default',
			className: 'top-[5px] text-xs',
		},
		{
			variant: 'premium',
			size: 'lg',
			className: 'top-3 text-xl',
		},
		{
			variant: 'standard',
			size: 'sm',
			className: 'top-[3px] text-[8px]',
		},
		{
			variant: 'standard',
			size: 'default',
			className: 'top-[7px] text-xs',
		},
		{
			variant: 'standard',
			size: 'lg',
			className: 'top-[17px] text-xl',
		},
	],
});

interface UserAvatarProps extends VariantProps<typeof userAvatarVariants> {
	firstName?: string | null;
	isLoggedIn: boolean;
	isPremium?: boolean;
	className?: string;
	subscriptionTier?: number;
	stroke?: boolean;
}

export function UserAvatar({
	firstName,
	isLoggedIn,
	isPremium,
	subscriptionTier,
	stroke = true,
	variant,
	size,
	className,
}: UserAvatarProps) {
	const initial = firstName?.charAt(0)?.toUpperCase();
	const { type, Icon } = getIcon({
		isPremium,
		isLoggedIn,
		initial,
		subscriptionTier,
		stroke,
	});

	return (
		<div className="relative flex flex-col items-center">
			<Icon className={cn(userAvatarVariants({ variant, size }), className)} />
			{initial ? (
				<span className={cn(userAvatarTextVariants({ variant: type, size }))}>
					{initial}
				</span>
			) : null}
		</div>
	);
}

interface getIcon {
	type: 'premium' | 'standard';
	Icon: React.FC<Omit<BaseIconProps, 'icon'>>;
}

function getIcon({
	isPremium,
	isLoggedIn,
	initial,
	subscriptionTier,
	stroke,
}: {
	isPremium?: boolean;
	isLoggedIn?: boolean;
	initial?: string;
	subscriptionTier?: number;
	stroke?: boolean;
}): getIcon {
	if (isPremium) {
		const res: getIcon = {
			type: 'premium',
			Icon: AvatarRegisteredPremiumFilled,
		};
		switch (subscriptionTier) {
			case UserSubscriptionTiers.premium: {
				if (stroke) res.Icon = AvatarRegisteredPremium;
				else res.Icon = AvatarRegisteredPremiumFilled;
				break;
			}
			case UserSubscriptionTiers.adFree: {
				if (stroke) res.Icon = UserRegisteredAdfree;
				else res.Icon = UserRegisteredAdfreeFilled;
				break;
			}
			default:
		}
		return res;
	}

	const res: getIcon = {
		type: 'standard',
		Icon: UserAnonymous,
	};
	switch (true) {
		case initial && isLoggedIn: {
			if (stroke) res.Icon = UserRegisteredStroke;
			else res.Icon = UserRegisteredBackground;
			break;
		}
		default: {
			if (stroke) res.Icon = UserAvatarFilledAlt;
			else res.Icon = UserAnonymous;
		}
	}
	return res;
}
