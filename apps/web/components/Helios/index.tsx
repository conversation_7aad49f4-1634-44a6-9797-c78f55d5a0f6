import 'server-only';

import { Article, Page } from '@/payload-types';
import { deviceClassFlag, userTierFlag, precomputeFlags } from '@/flags';
import {
	getHeliosConfig,
	getSubscriptionTier,
} from './utils/heliosTransformer';
import HeliosConfigScript from './components/HeliosConfigScript';
import HeliosSrcScript from './components/HeliosSrcScript';
import { UserSubscriptionTiers } from '@/user/utils/consts';

interface HeliosProps {
	article?: Article | null;
	page?: Page | null;
	code?: string;
}

export default async function Helios({ article, page, code }: HeliosProps) {
	const deviceClass = code
		? await deviceClassFlag(code, precomputeFlags)
		: 'mobile';
	const subscriptionTierFlag = code
		? await userTierFlag(code, precomputeFlags)
		: UserSubscriptionTiers.none.toString();
	const subscriptionTier = getSubscriptionTier(subscriptionTierFlag);

	const isUserPremium =
		subscriptionTierFlag === UserSubscriptionTiers.premium.toString() ||
		subscriptionTierFlag === UserSubscriptionTiers.adFree.toString() ||
		subscriptionTierFlag === UserSubscriptionTiers.premiumWithAd.toString();

	const heliosConfig = getHeliosConfig({
		subscriptionTier,
		isUserPremium,
		article,
		deviceClass,
		page,
	});

	// Safely stringify the config with error handling
	let configJson;
	try {
		configJson = JSON.stringify(heliosConfig);
	} catch (jsonError) {
		console.error('HeliosConfig JSON Parse Error', jsonError);

		// Fall back to config without article data
		const fallbackConfig = getHeliosConfig({
			subscriptionTier,
			isUserPremium,
			article: null, // Exclude article data in fallback
			deviceClass,
		});

		configJson = JSON.stringify(fallbackConfig);
	}

	return (
		<>
			<HeliosConfigScript configJson={configJson} />
			<HeliosSrcScript isUserPremium={isUserPremium} article={article} />
		</>
	);
}
