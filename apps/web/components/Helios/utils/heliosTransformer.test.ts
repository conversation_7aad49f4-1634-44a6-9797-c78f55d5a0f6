import { describe, it, expect } from 'vitest';
import {
	extractAdsDataFromPageResponse,
	extractThirdPartyConfigsFromPageResponse,
	extractAdSlotsFromPageResponse,
	getAdUnitPath,
	getCfValue,
	getSubscriptionTier,
	getSlotsNamesForPage,
} from './heliosTransformer';
import type { Page, AdSlot } from '@/payload-types';
import { UserSubscriptionTiers } from '@/user/utils/consts';

const defaultPage: Page = {
	id: 'test-page-id',
	title: 'Test Page Title',
	assetName: 'Test Page Name',
	content: { layout: [] },
	createdAt: '2025-01-01T12:00:00.000Z',
	updatedAt: '2025-01-01T12:00:00.000Z',
};

const defaultAdConfigData: Page['adConfig'] = {
	adsData: { networkCode: 'test-network-code', adZone: 'test-ad-zone' },
	thirdpartyConfigs: null,
};

const defaultAdSlot: AdSlot = {
	id: 'test-ad-slot-id',
	name: 'Test Ad Slot Name',
	slotTarget: 'test-slot-target',
	adPosition: 'test-ad-position',
	adDevices: [],
	adSizes: [],
	sequentialGroup: 'ATF',
	updatedAt: '2025-01-01T12:00:00.000Z',
	createdAt: '2025-01-01T12:00:00.000Z',
};

describe('heliosTransformer', () => {
	describe('extractAdsDataFromPageResponse', () => {
		it('should extract adsData when it exists and is a non-empty object', () => {
			const adsDataToExtract = { key: 'value' };
			const pageResponse = {
				...defaultPage,
				adConfig: { ...defaultAdConfigData, adsData: adsDataToExtract as any },
			};
			expect(extractAdsDataFromPageResponse(pageResponse)).toEqual(
				adsDataToExtract,
			);
		});

		it.each([
			{
				caseName: 'adConfig is undefined',
				pageResponse: { ...defaultPage, adConfig: undefined },
			},
			{
				caseName: 'adConfig is null',
				pageResponse: { ...defaultPage, adConfig: null as any },
			},
			{
				caseName: 'adsData is undefined',
				pageResponse: {
					...defaultPage,
					adConfig: { ...defaultAdConfigData, adsData: undefined },
				},
			},
			{
				caseName: 'adsData is null',
				pageResponse: {
					...defaultPage,
					adConfig: { ...defaultAdConfigData, adsData: null as any },
				},
			},
			{
				caseName: 'adsData is an empty object',
				pageResponse: {
					...defaultPage,
					adConfig: { ...defaultAdConfigData, adsData: {} },
				},
			},
			{
				caseName: 'adsData is not an object',
				pageResponse: {
					...defaultPage,
					adConfig: { ...defaultAdConfigData, adsData: 'not-an-object' as any },
				},
			},
		])('should return null if $caseName', ({ pageResponse }) => {
			expect(extractAdsDataFromPageResponse(pageResponse)).toBeNull();
		});
	});

	describe('extractThirdPartyConfigsFromPageResponse', () => {
		it('should extract thirdPartyConfigs when it exists and is a non-empty array', () => {
			const thirdPartyConfigsToExtract = [
				{
					thirdPartyConfig: 'test-third-party-config-1',
					id: 'test-third-party-config-id-1',
				},
			];
			const pageResponse = {
				...defaultPage,
				adConfig: {
					...defaultAdConfigData,
					thirdpartyConfigs: thirdPartyConfigsToExtract,
				},
			};
			expect(extractThirdPartyConfigsFromPageResponse(pageResponse)).toEqual(
				thirdPartyConfigsToExtract,
			);
		});

		it.each([
			{
				caseName: 'adConfig is undefined',
				pageResponse: { ...defaultPage, adConfig: undefined },
			},
			{
				caseName: 'adConfig is null',
				pageResponse: { ...defaultPage, adConfig: null as any },
			},
			{
				caseName: 'thirdpartyConfigs is undefined',
				pageResponse: {
					...defaultPage,
					adConfig: { ...defaultAdConfigData, thirdpartyConfigs: undefined },
				},
			},
			{
				caseName: 'thirdpartyConfigs is null',
				pageResponse: {
					...defaultPage,
					adConfig: { ...defaultAdConfigData, thirdpartyConfigs: null },
				},
			},
			{
				caseName: 'thirdpartyConfigs is an object that is not an array',
				pageResponse: {
					...defaultPage,
					adConfig: {
						...defaultAdConfigData,
						thirdpartyConfigs: { not: 'an array' } as any,
					},
				},
			},
			{
				caseName: 'thirdpartyConfigs is an empty array',
				pageResponse: {
					...defaultPage,
					adConfig: {
						...defaultAdConfigData,
						thirdpartyConfigs: [],
					},
				},
			},
		])('should return null if $caseName', ({ pageResponse }) => {
			expect(extractThirdPartyConfigsFromPageResponse(pageResponse)).toBeNull();
		});
	});

	describe('extractAdSlotsFromPageResponse', () => {
		it('should extract an AdSlot object from valid pageResponse content', () => {
			const adSlot1 = { ...defaultAdSlot, id: 'test-ad-slot-id-1' };
			const pageResponse = {
				...defaultPage,
				content: {
					layout: [
						{
							blocks: [
								{ blockType: 'Text', value: 'some test text' } as any,
								{ blockType: 'Ad', adSlot: adSlot1 } as any,
								{ blockType: 'Image', value: 'test-image.png' } as any,
							],
						},
					] as any[],
				} as any,
			};
			expect(extractAdSlotsFromPageResponse(pageResponse)).toEqual([adSlot1]);
		});

		it('should extract AdSlot objects from valid pageResponse content with multiple AdSlot objects', () => {
			const adSlot1 = { ...defaultAdSlot, id: 'test-ad-slot-id-1' };
			const adSlot2 = { ...defaultAdSlot, id: 'test-ad-slot-id-2' };
			const pageResponse = {
				...defaultPage,
				content: {
					layout: [
						{
							blocks: [
								{ blockType: 'Text', value: 'some test text' } as any,
								{ blockType: 'Ad', adSlot: adSlot1 } as any,
							],
						},
						{
							blocks: [
								{ blockType: 'Ad', adSlot: adSlot2 } as any,
								{ blockType: 'Image', value: 'test-image.png' } as any,
							],
						},
					] as any[],
				} as any,
			};
			expect(extractAdSlotsFromPageResponse(pageResponse)).toEqual([
				adSlot1,
				adSlot2,
			]);
		});

		it.each([
			{
				caseName: 'content is missing in pageResponse',
				pageResponse: { ...defaultPage, content: undefined } as any,
			},
			{
				caseName: 'content is null in pageResponse',
				pageResponse: { ...defaultPage, content: null as any },
			},
			{
				caseName: 'content.layout is missing in pageResponse',
				pageResponse: { ...defaultPage, content: { layout: undefined } } as any,
			},
			{
				caseName: 'pageResponse.content.layout is null',
				pageResponse: { ...defaultPage, content: { layout: null } } as any,
			},
			{
				caseName: 'pageResponse.content.layout is not an array',
				pageResponse: {
					...defaultPage,
					content: { layout: 'not-an-array' },
				} as any,
			},
			{
				caseName: 'pageResponse.content.layout is an empty array',
				pageResponse: { ...defaultPage, content: { layout: [] } } as any,
			},
			{
				caseName: 'pageResponse.content.layout.blocks is missing',
				pageResponse: {
					...defaultPage,
					content: { layout: [{ blocks: undefined }] },
				} as any,
			},
			{
				caseName: 'pageResponse.content.layout.blocks is null',
				pageResponse: {
					...defaultPage,
					content: { layout: [{ blocks: null }] },
				} as any,
			},
			{
				caseName: 'pageResponse.content.layout.blocks is not an array',
				pageResponse: {
					...defaultPage,
					content: { layout: [{ blocks: 'not-an-array' }] },
				} as any,
			},
			{
				caseName: 'pageResponse.content.layout has no blocks',
				pageResponse: {
					...defaultPage,
					content: { layout: [{ blocks: [] }] },
				} as any,
			},
			{
				caseName: 'pageResponse.content.layout has no Ad blocks',
				pageResponse: {
					...defaultPage,
					content: {
						layout: [
							{
								blocks: [
									{ blockType: 'Text', value: 'some text' } as any,
									{ blockType: 'Image', value: 'image.png' } as any,
								],
							},
						],
					},
				} as any,
			},
			{
				caseName:
					'a valid Ad block in pageResponse.content.layout.blocks is missing adSlot',
				pageResponse: {
					...defaultPage,
					content: {
						layout: [{ blocks: [{ blockType: 'Ad', adSlot: undefined }] }],
					},
				} as any,
			},
			{
				caseName:
					'a valid Ad block in pageResponse.content.layout.blocks has an adSlot that is null',
				pageResponse: {
					...defaultPage,
					content: {
						layout: [{ blocks: [{ blockType: 'Ad', adSlot: null }] }],
					},
				} as any,
			},
			{
				caseName:
					'a valid Ad block in pageResponse.content.layout.blocks has an adSlot that is not a valid object',
				pageResponse: {
					...defaultPage,
					content: {
						layout: [
							{ blocks: [{ blockType: 'Ad', adSlot: 'not an object' }] },
						],
					},
				} as any,
			},
		])('should return an empty array if $caseName', ({ pageResponse }) => {
			expect(extractAdSlotsFromPageResponse(pageResponse)).toEqual([]);
		});
	});

	describe('getAdUnitPath', () => {
		it.each([
			{
				deviceClass: 'desktop',
				expected: '/7646/web_weather_us/test-adzone',
				device: 'desktop',
			},
			{
				deviceClass: 'mobile',
				expected: '/7646/mobile_smart_us/test-adzone',
				device: 'mobile',
			},
			{
				deviceClass: 'tablet',
				expected: '/7646/tablet_weather_us/test-adzone',
				device: 'tablet',
			},
		])(
			'should use default values when no overrides are provided for $device',
			({ deviceClass, expected }) => {
				const adUnitPath = getAdUnitPath('test-adzone', deviceClass);
				expect(adUnitPath).toBe(expected);
			},
		);

		it.each([
			{
				deviceClass: 'desktop',
				networkCodeOverride: '1234',
				adUnitsOverride: undefined,
				isTestOverride: false,
				expected: '/1234/web_weather_us/test-adzone',
				description: 'networkCodeOverride',
			},
			{
				deviceClass: 'desktop',
				networkCodeOverride: undefined,
				adUnitsOverride: 'custom_ad_unit',
				isTestOverride: false,
				expected: '/7646/custom_ad_unit/test-adzone',
				description: 'adUnitsOverride',
			},
			{
				deviceClass: 'mobile',
				networkCodeOverride: '1234',
				adUnitsOverride: 'custom_ad_unit',
				isTestOverride: false,
				expected: '/1234/custom_ad_unit/test-adzone',
				description: 'all overrides',
			},
			{
				deviceClass: 'desktop',
				networkCodeOverride: '1234',
				adUnitsOverride: 'custom_ad_unit',
				isTestOverride: true,
				expected: '/1234/test_custom_ad_unit/test-adzone',
				description: 'isTestOverride',
			},
		])(
			'should use $description when provided',
			({
				deviceClass,
				networkCodeOverride,
				adUnitsOverride,
				isTestOverride,
				expected,
			}) => {
				const adUnitPath = getAdUnitPath(
					'test-adzone',
					deviceClass,
					networkCodeOverride,
					adUnitsOverride,
					isTestOverride,
				);
				expect(adUnitPath).toBe(expected);
			},
		);

		it('should use the adzone fallback value if adzone value is empty', () => {
			const adUnitPath = getAdUnitPath('', 'mobile');
			expect(adUnitPath).toBe('/7646/mobile_smart_us/video');
		});

		it.each([
			{ name: 'null', value: null },
			{ name: 'undefined', value: undefined },
			{ name: 'empty string', value: '' },
		])(
			'should use default network code when networkCodeOverride is $name',
			({ value }) => {
				const adUnitPath = getAdUnitPath('test-adzone', 'desktop', value);
				expect(adUnitPath).toBe('/7646/web_weather_us/test-adzone');
			},
		);

		it.each([
			{ name: 'null', value: null },
			{ name: 'undefined', value: undefined },
			{ name: 'empty string', value: '' },
		])(
			'should use default ad unit when adUnitsOverride is $name for desktop',
			({ value }) => {
				const adUnitPath = getAdUnitPath(
					'test-adzone',
					'desktop',
					undefined,
					value,
				);
				expect(adUnitPath).toBe('/7646/web_weather_us/test-adzone');
			},
		);

		it.each([
			{ name: 'null', value: null },
			{ name: 'undefined', value: undefined },
			{ name: 'empty string', value: '' },
		])(
			'should use default ad unit when adUnitsOverride is $name for mobile',
			({ value }) => {
				const adUnitPath = getAdUnitPath(
					'test-adzone',
					'mobile',
					undefined,
					value,
				);
				expect(adUnitPath).toBe('/7646/mobile_smart_us/test-adzone');
			},
		);
	});

	describe('getSlotsNamesForPage', () => {
		it('should return desktop slot names when isMobile is false', () => {
			const expectedSlots = [
				'WX_Bot300',
				'WX_BottomLeader',
				'WX_DriverUnit',
				'WX_Hidden',
				'WX_Mid300',
				'WX_PaidSearch',
				'WX_PromoDriver1',
				'WX_SpotLight',
				'WX_Top300Variable',
				'WX_WindowShade',
			];
			expect(getSlotsNamesForPage('desktop', 'article')).toEqual(expectedSlots);
			expect(getSlotsNamesForPage('desktop', 'any-page-key')).toEqual(
				expectedSlots,
			);
		});

		it('should return mobile video slot names when isMobile is true and pageKey is "video"', () => {
			const expectedSlots = [
				'WX_Hidden',
				'MW_Position_ContentFeedAd',
				'MW_Position_ContentFeedAd1',
			];
			expect(getSlotsNamesForPage('mobile', 'video')).toEqual(expectedSlots);
		});

		it('should return mobile article slot names when isMobile is true and pageKey is not "video"', () => {
			const expectedSlots = [
				'WX_Hidden',
				'MW_Position1',
				'MW_Position2',
				'MW_Position3',
				'MW_Position4',
				'MW_Position5',
			];
			expect(getSlotsNamesForPage('mobile', 'article')).toEqual(expectedSlots);
			expect(getSlotsNamesForPage('mobile', 'some-other-key')).toEqual(
				expectedSlots,
			);
		});
	});

	describe('getSubscriptionTier', () => {
		it.each([
			{ tier: UserSubscriptionTiers.premium.toString(), expected: 'premium' },
			{ tier: UserSubscriptionTiers.standard.toString(), expected: 'standard' },
			{
				tier: UserSubscriptionTiers.premiumWithAd.toString(),
				expected: 'premiumWithAd',
			},
			{ tier: UserSubscriptionTiers.adFree.toString(), expected: 'adFree' },
			{ tier: UserSubscriptionTiers.none.toString(), expected: 'none' },
			{ tier: undefined, expected: 'none' },
		])(
			'should return "$expected" as a string for tier enum "$tier"',
			({ tier, expected }) => {
				expect(getSubscriptionTier(tier)).toBe(expected);
			},
		);
	});

	describe('getCfValue', () => {
		it.each([
			{
				wxnodeTypes: undefined,
				pageKey: 'video',
				expected: 'v',
				case: 'pageKey is video',
			},
			{
				wxnodeTypes: ['block_video'],
				pageKey: 'article',
				expected: 'av',
				case: 'wxnodeTypes is block_video and pageKey is article',
			},
			{
				wxnodeTypes: ['content_slideshow'],
				pageKey: 'article',
				expected: 'ss',
				case: 'wxnodeTypes is content_slideshow and pageKey is article',
			},
			{
				wxnodeTypes: undefined,
				pageKey: 'article',
				expected: 'a',
				case: 'wxnodeTypes is undefined',
			},
			{
				wxnodeTypes: [],
				pageKey: 'article',
				expected: 'a',
				case: 'wxnodeTypes is an empty array',
			},
			{
				wxnodeTypes: ['block_image'],
				pageKey: 'article',
				expected: 'a',
				case: 'wxnodeTypes array value is not block_video or content_slideshow',
			},
			{
				wxnodeTypes: undefined,
				pageKey: 'someOtherPage',
				expected: 'a',
				case: 'pageKey is not video and wxnodeTypes is undefined',
			},
		])(
			'should return a cf value of "$expected" when $case',
			({ wxnodeTypes, pageKey, expected }) => {
				expect(getCfValue(wxnodeTypes, pageKey)).toBe(expected);
			},
		);
	});
});
