'use client';

import Script from 'next/script';

interface HeliosConfigScriptProps {
	configJson: string;
}

export default function HeliosConfigScript({
	configJson,
}: HeliosConfigScriptProps) {
	return (
		<Script id="helios-config">
			{`window.__HeliosConfig = ${configJson};
			window.__HeliosConfig.generalConfig.breakpoint.active = window.screen.width > 1024 ? 'lg' : window.screen.width >= 768 ? 'md' : 'sm';
			window.__HeliosQ = window.__HeliosQ || [];
			window.__HeliosQ.push(function () { window.__Helios.emit('USER_PROFILE_READY'); });`}
		</Script>
	);
}
