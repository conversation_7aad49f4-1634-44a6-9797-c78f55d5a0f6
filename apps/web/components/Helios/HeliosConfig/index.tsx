'use client';

import Script from 'next/script';
import { Article } from '@/payload-types';
import { useMobileMedia } from '@/hooks/useMobileMedia';
import { useUser } from '@/user/hooks/useUser';
import { usePreferredLocation } from '@/user/hooks/preferredLocation';
import {
	getHeliosConfig,
	getSubscriptionTier,
	HeliosConfigType,
} from './heliosTransformer';

declare global {
	interface Window {
		__HeliosConfig: HeliosConfigType;
	}
}

// Add articleData to the props
interface HeliosConfigProps {
	article?: Article | null;
}

export default function HeliosConfigComponent({
	article,
}: HeliosConfigProps = {}) {
	usePreferredLocation();
	const { DISCONNECTED_user: user } = useUser();
	const { subscriptionTier, isUserPremium } = user ?? {};
	const isMobile = useMobileMedia();

	// Create the config object with article data
	const heliosConfig = getHeliosConfig({
		subscriptionTier: getSubscriptionTier(subscriptionTier),
		isUserPremium,
		article, // Pass article data to the config function
		isMobile,
	});

	// Safely stringify the config with error handling
	let configJson;
	try {
		configJson = JSON.stringify(heliosConfig);
	} catch (jsonError) {
		console.error('HeliosConfig JSON Parse Error', jsonError);

		// Fall back to config without article data
		const fallbackConfig = getHeliosConfig({
			subscriptionTier: getSubscriptionTier(subscriptionTier),
			isUserPremium,
			article: null, // Exclude article data in fallback
			isMobile,
		});

		configJson = JSON.stringify(fallbackConfig);
	}

	// TODO: use burda config on de-DE pages, requires also changing div ids, e.g. WX_WindowShade becomes DE_DESKTOP_BANNER_TOP
	return (
		<Script id="helios-config">
			{`window.__HeliosConfig = ${configJson}; 
			window.__HeliosConfig.generalConfig.breakpoint.active = window.screen.width > 1024 ? 'lg' : window.screen.width >= 768 ? 'md' : 'sm';
			window.__HeliosQ = window.__HeliosQ || [];
			window.__HeliosQ.push(function () { window.__Helios.emit('USER_PROFILE_READY'); });`}
		</Script>
	);
}
