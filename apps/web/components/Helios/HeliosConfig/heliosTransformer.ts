import type {
	Page,
	AdSlot,
	Article,
	Tag,
	ThirdPartyConfig,
} from '@/payload-types';
import { UserSubscriptionTiers } from '@/user/utils/consts';

// Define a type for the article data in the config
type IAB = {
	v1: string[];
	v2: string[];
	v3: string[];
};

type ArticleConfigData = {
	adzone: string;
	collectionAdZone: string;
	iab: IAB;
	wxnodeTypes: string[];
} | null;

type AdsData = {
	adZone?: string | null;
	adUnits?: string;
	networkCode?: string | null;
};

type PageThirdPartyConfig = {
	thirdPartyConfig: string | ThirdPartyConfig;
	id?: string | null;
};

type AdServer = {
	script?: string;
	layoutMap?: Record<string, string>;
};

type BidParams = {
	slotName?: string;
	zoneid?: number;
};

type Bid = {
	params: BidParams;
};

type ABProps = {
	chanceAbcd?: {
		both: number;
		liveintent: number;
		none: number;
		optable: number;
	};
};

type Module = {
	bidMode?: string;
	disableBids?: string[];
	interval?: number;
	inview?: boolean;
	refreshDelay?: number | null;
	on?: boolean;
	interactive?: boolean;
	allowedLocaleRegexp?: string;
	allowedRegimeRegexp?: string;
	pubId?: string;
	scriptUrl?: string;
	bundleUrl?: string;
	ab?: ABProps;
};

type CModule = {
	ortb2Site?: {
		keywords: string;
	};
	page?: {
		v1: string[];
		v2: string[];
		v3: string[];
	};
	site?: {
		v1: string[];
		v3: string[];
	};
	activeVideoAdUnitPath?: string;
	duration?: string;
	hashedMpid?: string;
	id?: string;
	pcollid?: string;
	providername?: string;
	acctId?: string;
	apiKey?: string;
	locale?: string;
	locales?: string[];
	locationId?: string | null;
	segId?: string;
	timeout?: number;
	userId?: string;
	on?: boolean;
	placeId?: string | null;
	wxApiHost?: string;
	alerts?: string | null;
	dailyForecast?: string | null;
	location?: string | null;
	observation?: string | null;
	pollen?: string | null;
	unitCode?: string;
	includeMetaKvs?: string[];
	includePrebidAdUnitCodes?: string;
	services?: string[];
};

type SSP = {
	pubId?: number;
	scripts?: string[];
	slotId?: string;
	mediaTypes?: {
		video?: {
			api: number[];
			context: string;
			maxduration: number;
			mimes: string[];
			placement: number;
			playbackmethod: number[];
			playerSize: number[];
			plcmt: number;
			protocols: number[];
			skip: number;
		};
	};
	networkId?: number;
	publisherId?: string;
	slotName?: string;
};

type Targeting = {
	adUnits: string;
	adZone: string;
	cf: string;
	env1?: string;
	experience: string | null;
	locale: string;
	networkCode: string;
	pageKey: string;
	regime: string;
	siteMode: string | null;
	tf: string;
	variantKey: string | null;
	videoCount: number | null;
};

type Size = (number[] | string[])[];

// Define the HeliosConfig type structure based on the returned object
export type HeliosConfigType = {
	generalConfig: {
		adServers: Record<string, AdServer>;
		bidTimeout: number;
		breakpoint: Record<string, number>;
		buffer: Record<string, number>;
		cModules: Record<string, CModule>;
		hotfixVideoLocationChangeGraceTime: number;
		modules: Record<string, Module>;
		specialSlotRules: Array<{
			elementIds: string[];
			handler: string;
			matchPageKey: string;
		}>;
		ssps: Record<string, SSP>;
		sub: {
			noAds: boolean;
			tier: string;
		};
		targeting: Targeting;
		useVideoAdRulesUrlWhenAdTagEmpty: boolean;
		video?: {
			ssps: Record<string, SSP>;
		};
	};
	slotsConfig: Record<
		string,
		{
			adUnitPath: string;
			bidMode: string;
			bids: Record<string, Bid>;
			modules: Record<string, Module>;
			pos: string;
			sizes: Record<string, Size | null>;
		}
	>;
};

export interface HeliosConfigTransformerProps {
	subscriptionTier: string;
	isUserPremium: boolean | undefined;
	article?: Article | null;
	isMobile: boolean;
	page?: Page | null;
}

interface ArticleBodyChild {
	type: string;
	version: number;
	fields?: {
		__wxnode?: {
			type: string;
		};
	};
	[k: string]: unknown;
}

/**
 * Extracts adsData from a Page response object.
 *
 * @param pageResponse - The Page response object from PayloadCMS.
 * @returns The adsData object if found, otherwise null.
 */
export const extractAdsDataFromPageResponse = (pageResponse: Page) => {
	const adsData = pageResponse?.adConfig?.adsData;

	if (
		adsData &&
		typeof adsData === 'object' &&
		Object.keys(adsData).length > 0
	) {
		return adsData;
	}
	return null;
};

/**
 * Extracts thirdPartyConfigs from a Page response object.
 *
 * @param pageResponse - The Page response object from PayloadCMS.
 * @returns The array of thirdpartyConfigs objects. Returns null if not found, invalid, or empty.
 */
export const extractThirdPartyConfigsFromPageResponse = (
	pageResponse: Page,
) => {
	const thirdpartyConfigs = pageResponse?.adConfig?.thirdpartyConfigs;

	if (
		thirdpartyConfigs &&
		Array.isArray(thirdpartyConfigs) &&
		thirdpartyConfigs.length > 0
	) {
		return thirdpartyConfigs;
	}
	return null;
};

/**
 * Extracts AdSlot objects from a page response.
 *
 * @param pageResponse - The Page response object from PayloadCMS.
 * @returns An array of AdSlot objects. Returns an empty array if none are found or if data is invalid.
 */
export const extractAdSlotsFromPageResponse = (
	pageResponse: Page,
): AdSlot[] => {
	const contentLayouts = pageResponse?.content?.layout;

	if (!contentLayouts || !Array.isArray(contentLayouts)) {
		return [];
	}

	const adSlots: AdSlot[] = [];

	for (const layout of contentLayouts) {
		if (layout && layout.blocks && Array.isArray(layout.blocks)) {
			for (const block of layout.blocks) {
				if (
					block?.blockType === 'Ad' &&
					block.adSlot &&
					typeof block.adSlot === 'object'
				) {
					adSlots.push(block.adSlot as AdSlot);
				}
			}
		}
	}

	return adSlots;
};

/**
 * Safely extract and transform article data from an Article object.
 *
 * @param articleData - The article object from which to extract data, or null/undefined if not available.
 * @returns An object containing select fields from the Article object (adzone, collectionAdZone, iab, wxnodeTypes),
 *          or null if articleData is missing or invalid.
 */
const getSafeArticleData = (
	articleData: Article | null | undefined,
): ArticleConfigData => {
	if (!articleData) {
		console.debug('HeliosConfig getSafeArticleData no articleData');
		return null;
	}

	try {
		// adzone
		const tags = Array.isArray(articleData?.tags)
			? articleData?.tags || []
			: [];
		const trueTags = tags.filter((tag) => typeof tag === 'object') as Tag[];
		const adZones = trueTags.filter((tag) => tag?.type === 'adZones');
		const adZone =
			adZones.find((tag) => tag?.id === '__article_adzone')?.name || '';
		const collectionAdZone =
			adZones.find((tag) => tag?.id === '__collection_adzone')?.name || '';

		// iab
		const iabString =
			trueTags.find((tag) => tag?.type === 'iabTags')?.name || '{}';

		// Parse iabString with error handling
		let iabTags: IAB = { v1: [], v2: [], v3: [] }; // Default empty structure
		try {
			const parsedIab = JSON.parse(iabString);
			// Validate the structure has the expected properties
			if (parsedIab && typeof parsedIab === 'object') {
				iabTags = {
					v1: Array.isArray(parsedIab.v1) ? parsedIab.v1 : [],
					v2: Array.isArray(parsedIab.v2) ? parsedIab.v2 : [],
					v3: Array.isArray(parsedIab.v3) ? parsedIab.v3 : [],
				};
			}
		} catch (error) {
			// Keep using the default empty structure
			console.error('HeliosConfig IAB JSON Parse Error', error);
		}

		// Extract wxnode types from block nodes with __wxnode field
		const wxnodeTypes: string[] = [];

		// Check if we have content with children
		if (articleData?.content?.body?.root?.children) {
			articleData.content.body.root.children.forEach(
				(node: ArticleBodyChild) => {
					// Check if this is a block node with __wxnode field
					if (node?.type === 'block' && node?.fields?.__wxnode?.type) {
						wxnodeTypes.push(node?.fields?.__wxnode?.type || '');
					}
				},
			);
		}

		// Extract only the necessary fields with safe fallbacks
		return {
			adzone: adZone,
			collectionAdZone,
			iab: iabTags,
			wxnodeTypes: wxnodeTypes,
		};
	} catch (error) {
		console.error('HeliosConfig getSafeArticleData Error', error);
		return null;
	}
};

/**
 * Converts a UserSubscriptionTiers enum value to its string representation.
 *
 * @param subscriptionTier - The subscription tier enum value.
 * @returns The string representation of the subscription tier. Defaults to 'none'.
 */
export const getSubscriptionTier = (
	subscriptionTier: UserSubscriptionTiers | undefined,
): string => {
	switch (subscriptionTier) {
		case UserSubscriptionTiers.premium:
			return 'premium';
		case UserSubscriptionTiers.standard:
			return 'standard';
		case UserSubscriptionTiers.premiumWithAd:
			return 'premiumWithAd';
		case UserSubscriptionTiers.adFree:
			return 'adFree';
		case UserSubscriptionTiers.none:
			return 'none';
		default:
			return 'none';
	}
};

/**
 * Determines a 'cf' value based on wxnodeTypes and pageKey:
 * - 'v' for video pageKey
 * - 'av' for article pageKey with video assetType
 * - 'ss' for slideshow assetType
 * - 'a' for article
 *
 * @param wxnodeTypes - An array of wxnode type strings (e.g. 'block_video', 'content_slideshow').
 * @param pageKey - A string identifying the current page's primary type (e.g., 'article', 'video').
 * @returns The calculated 'cf' value. Defaults to 'a'
 */
export const getCfValue = (
	wxnodeTypes: string[] | undefined,
	pageKey: string,
) => {
	if (pageKey === 'video') {
		return 'v';
	}

	if (wxnodeTypes && wxnodeTypes[0]) {
		const assetType = wxnodeTypes[0]?.split('_')[1];

		if (assetType === 'video' && pageKey === 'article') {
			return 'av';
		}
		if (assetType === 'slideshow') {
			return 'ss';
		}
	}

	return 'a';
};

/**
 * Constructs the ad unit path to be used in HeliosConfig.
 *
 * @param adzone - The adzone string, extracted from article data.
 * @param isMobile - Boolean indicating if the current device is mobile.
 * @param networkCodeOverride - Optional network code to override the default.
 * @param adUnitsOverride - Optional ad units to override the default.
 * @param isTestOverride - Optional flag to mark as a test ad unit.
 * @returns The fully constructed ad unit path.
 */
export const getAdUnitPath = (
	adzone: string,
	isMobile: boolean,
	networkCodeOverride?: string | null,
	adUnitsOverride?: string | null,
	isTestOverride?: boolean,
) => {
	const pageNetworkCode = networkCodeOverride || '7646';
	const pageDeviceAdUnit =
		adUnitsOverride || (isMobile ? 'mobile_smart_us' : 'web_weather_us');
	const activeVideoAdZone = adzone || 'video';
	const adsTest = isTestOverride ?? false;
	return `/${pageNetworkCode}/${adsTest ? 'test_' : ''}${pageDeviceAdUnit}/${activeVideoAdZone}`;
};

/**
 * Determines the list of ad slot names to be returned in HeliosConfig.slotsConfig,
 * based on the device type and page key.
 *
 * @param isMobile - Boolean indicating if the current device is mobile.
 * @param pageKey - String representing the current page type (e.g., 'article', 'video').
 * @returns An array of slot names.
 */
export const getSlotsNamesForPage = (
	isMobile: boolean,
	pageKey: string,
): string[] => {
	if (!isMobile) {
		// Desktop
		return [
			'WX_Bot300',
			'WX_BottomLeader',
			'WX_DriverUnit',
			'WX_Hidden',
			'WX_Mid300',
			'WX_PaidSearch',
			'WX_PromoDriver1',
			'WX_SpotLight',
			'WX_Top300Variable',
			'WX_WindowShade',
		];
	}
	if (pageKey === 'video') {
		// Mobile video page
		return [
			'WX_Hidden',
			'MW_Position_ContentFeedAd',
			'MW_Position_ContentFeedAd1',
		];
	}
	// Mobile article page
	return [
		'WX_Hidden',
		'MW_Position1',
		'MW_Position2',
		'MW_Position3',
		'MW_Position4',
		'MW_Position5',
	];
};

/**
 * Generates the HeliosConfig object (new version with page data integration).
 *
 * @param isUserPremium - Whether the user has a premium subscription.
 * @param subscriptionTier - The user's subscription tier string.
 * @param article - The article data, if available.
 * @param isMobile - Boolean indicating if the current device is mobile.
 * @param page - The page data, if available (used for dynamic ad configurations).
 * @returns The complete HeliosConfig object, with generalConfig and slotsConfig.
 */
export const getHeliosConfig = ({
	isUserPremium,
	subscriptionTier,
	article,
	isMobile,
	page,
}: HeliosConfigTransformerProps): HeliosConfigType => {
	// Create a safe version of the article data
	let safeArticleData: ArticleConfigData | null = null;
	let adsDataFromPage: AdsData | null = null;
	let thirdPartyConfigsFromPage: PageThirdPartyConfig[] | null = null;
	let adSlotsFromPage: AdSlot[] = [];

	try {
		if (article) {
			safeArticleData = getSafeArticleData(article);
		}
		if (page) {
			adsDataFromPage = extractAdsDataFromPageResponse(page);
			thirdPartyConfigsFromPage =
				extractThirdPartyConfigsFromPageResponse(page);
			adSlotsFromPage = extractAdSlotsFromPageResponse(page);
		}
	} catch (error) {
		console.error('HeliosConfig data extraction Error', error);
	}

	const currentPageKey = page?.assetName || 'article';
	const cf = getCfValue(safeArticleData?.wxnodeTypes, currentPageKey);
	const iabPageConfig = safeArticleData?.iab || { v1: [], v2: [], v3: [] };

	const currentNetworkCode = adsDataFromPage?.networkCode;
	const currentAdUnits = adsDataFromPage?.adUnits;
	const currentAdZone =
		adsDataFromPage?.adZone ||
		safeArticleData?.collectionAdZone ||
		safeArticleData?.adzone ||
		'';

	const adUnitPath = getAdUnitPath(
		currentAdZone,
		isMobile,
		currentNetworkCode,
		currentAdUnits,
	);

	// List of all possible slotsConfig values
	const allSlotsConfig: HeliosConfigType['slotsConfig'] = {
		WX_Bot300: {
			adUnitPath,
			bidMode: '',
			bids: {
				amazon: {
					params: {
						slotName: '/7646/web_weather_us/WX_Bot300',
					},
				},
				criteo: {
					params: {
						zoneid: 761235,
					},
				},
				demandManager: {
					params: {},
				},
			},
			modules: {},
			pos: 'wx_bot300',
			sizes: {
				lg: [[300, 250], ['fluid']],
			},
		},
		WX_BottomLeader: {
			adUnitPath,
			bidMode: '',
			bids: {
				amazon: {
					params: {
						slotName: '/7646/web_weather_us/WX_BottomLeader',
					},
				},
				criteo: {
					params: {
						zoneid: 761234,
					},
				},
				demandManager: {
					params: {},
				},
			},
			modules: {
				refresh: {
					bidMode: 'btf',
					disableBids: [],
					interval: 32000,
					inview: true,
					refreshDelay: null,
				},
			},
			pos: 'wx_botldr',
			sizes: {
				lg: [[728, 90]],
			},
		},
		WX_DriverUnit: {
			adUnitPath,
			bidMode: '',
			bids: {
				amazon: {
					params: {
						slotName: '/7646/web_weather_us/WX_DriverUnit',
					},
				},
				criteo: {
					params: {
						zoneid: 761234,
					},
				},
				demandManager: {
					params: {},
				},
			},
			modules: {
				refresh: {
					bidMode: 'btf',
					disableBids: [],
					interval: 32000,
					inview: true,
					refreshDelay: null,
				},
			},
			pos: 'wx_du',
			sizes: {
				'md-sm': [[300, 250]],
				lg: [
					[728, 90],
					[665, 90],
					[980, 64],
				],
			},
		},
		WX_Hidden: {
			adUnitPath,
			bidMode: '',
			bids: {},
			modules: {},
			pos: 'wx_hdn',
			sizes: {
				lg: [[1, 1]],
				sm: [[1, 1]],
			},
		},
		WX_Mid300: {
			adUnitPath,
			bidMode: '',
			bids: {
				amazon: {
					params: {
						slotName: '/7646/web_weather_us/WX_Mid300',
					},
				},
				criteo: {
					params: {
						zoneid: 761235,
					},
				},
				demandManager: {
					params: {},
				},
			},
			modules: {
				refresh: {
					bidMode: 'btf',
					disableBids: [],
					interval: 32000,
					inview: true,
					refreshDelay: null,
				},
			},
			pos: 'wx_mid300',
			sizes: {
				lg: [[300, 250], [320, 300], ['fluid']],
			},
		},
		WX_PaidSearch: {
			adUnitPath,
			bidMode: '',
			bids: {
				amazon: {
					params: {
						slotName: '/7646/web_weather_us/WX_PaidSearch',
					},
				},
				criteo: {
					params: {
						zoneid: 761235,
					},
				},
				demandManager: {
					params: {},
				},
			},
			modules: {},
			pos: 'wx_pds',
			sizes: {
				lg: [[300, 250], ['fluid']],
			},
		},
		WX_PromoDriver1: {
			adUnitPath: `${adUnitPath}/promo`,
			bidMode: '',
			bids: {},
			modules: {},
			pos: 'wx_promodriver1',
			sizes: {
				lg: [[285, 100]],
			},
		},
		WX_SpotLight: {
			adUnitPath,
			bidMode: '',
			bids: {},
			modules: {},
			pos: 'wx_sl',
			sizes: {
				lg: [[300, 36]],
			},
		},
		WX_Top300Variable: {
			adUnitPath,
			bidMode: '',
			bids: {
				amazon: {
					params: {
						slotName: '/7646/web_weather_us/WX_Top300Variable',
					},
				},
				criteo: {
					params: {
						zoneid: 761229,
					},
				},
				demandManager: {
					params: {},
				},
			},
			modules: {
				refresh: {
					bidMode: 'atf',
					disableBids: [],
					interval: 30000,
					inview: true,
					refreshDelay: null,
				},
			},
			pos: 'wx_300var',
			sizes: {
				lg: [
					[300, 600],
					[300, 250],
					[320, 300],
					[160, 600],
					[320, 480],
				],
			},
		},
		WX_WindowShade: {
			adUnitPath,
			bidMode: '',
			bids: {
				amazon: {
					params: {
						slotName: '/7646/web_weather_us/WX_WindowShade',
					},
				},
				criteo: {
					params: {
						zoneid: 761231,
					},
				},
				demandManager: {
					params: {},
				},
			},
			modules: {
				refresh: {
					bidMode: 'atf',
					disableBids: [],
					interval: 30000,
					inview: true,
					refreshDelay: null,
				},
			},
			pos: 'wx_ws',
			sizes: {
				md: [
					[728, 50],
					[970, 90],
					[728, 90],
				],
				sm: [[300, 250]],
				lg: [
					[970, 250],
					[728, 50],
					[970, 90],
					[728, 90],
				],
			},
		},
		MW_Position1: {
			pos: 'mw_p1',
			adUnitPath,
			sizes: {
				sm: [[320, 50], [300, 45], [320, 75], ['fluid']],
			},
			bidMode: '',
			modules: {
				refresh: {
					interval: 30000,
					inview: true,
					disableBids: [],
					refreshDelay: null,
					bidMode: 'atf',
				},
			},
			bids: {
				criteo: {
					params: {
						zoneid: 1554128,
					},
				},
				amazon: {
					params: {
						slotName: '/7646/mobile_smart_us/MW_Position1',
					},
				},
				demandManager: {
					params: {},
				},
			},
		},
		MW_Position2: {
			pos: 'mw_p2',
			adUnitPath,
			sizes: {
				sm: [
					[300, 250],
					[320, 50],
					[320, 75],
					[320, 150],
					[320, 480],
					['fluid'],
				],
			},
			bidMode: '',
			modules: {
				refresh: {
					interval: 32000,
					inview: true,
					disableBids: [],
					refreshDelay: null,
					bidMode: 'btf',
				},
			},
			bids: {
				criteo: {
					params: {
						zoneid: 761262,
					},
				},
				amazon: {
					params: {
						slotName: '/7646/mobile_smart_us/MW_Position2',
					},
				},
				demandManager: {
					params: {},
				},
			},
		},
		MW_Position3: {
			pos: 'mw_p3',
			adUnitPath,
			sizes: {
				sm: [
					[300, 250],
					[320, 75],
					[320, 150],
					[320, 480],
					[410, 792],
					['fluid'],
				],
			},
			bidMode: '',
			modules: {
				refresh: {
					interval: 32000,
					inview: true,
					disableBids: [],
					refreshDelay: null,
					bidMode: 'btf',
				},
			},
			bids: {
				criteo: {
					params: {
						zoneid: 761262,
					},
				},
				amazon: {
					params: {
						slotName: '/7646/mobile_smart_us/MW_Position3',
					},
				},
				demandManager: {
					params: {},
				},
			},
		},
		MW_Position4: {
			pos: 'mw_p4',
			adUnitPath,
			sizes: {
				sm: [
					[300, 250],
					[320, 50],
					[320, 75],
					[320, 150],
					[320, 480],
					['fluid'],
				],
			},
			bidMode: '',
			modules: {
				refresh: {
					interval: 32000,
					inview: true,
					disableBids: [],
					refreshDelay: null,
					bidMode: 'btf',
				},
			},
			bids: {
				criteo: {
					params: {
						zoneid: 761262,
					},
				},
				amazon: {
					params: {
						slotName: '/7646/mobile_smart_us/MW_Position4',
					},
				},
				demandManager: {
					params: {},
				},
			},
		},
		MW_Position5: {
			pos: 'mw_p5',
			adUnitPath,
			sizes: {
				sm: [
					[300, 250],
					[300, 600],
					[160, 600],
					[320, 50],
					[320, 75],
					[320, 150],
					[320, 480],
					['fluid'],
				],
			},
			bidMode: '',
			modules: {},
			bids: {
				criteo: {
					params: {
						zoneid: 761262,
					},
				},
				amazon: {
					params: {
						slotName: '/7646/mobile_smart_us/MW_Position5',
					},
				},
				demandManager: {
					params: {},
				},
			},
		},
		MW_Position_ContentFeedAd: {
			pos: 'mw_cfeed1',
			adUnitPath,
			sizes: {
				sm: [['fluid']],
			},
			bidMode: '',
			modules: {},
			bids: {},
		},
		MW_Position_ContentFeedAd1: {
			pos: 'mw_cfeed2',
			adUnitPath,
			sizes: {
				sm: [['fluid']],
			},
			bidMode: '',
			modules: {},
			bids: {},
		},
	};

	const finalSlotsConfig: HeliosConfigType['slotsConfig'] = {};
	const slotNamesForPage = getSlotsNamesForPage(isMobile, currentPageKey);

	for (const slotName of slotNamesForPage) {
		if (allSlotsConfig[slotName]) {
			const currSlotConfig = JSON.parse(
				JSON.stringify(allSlotsConfig[slotName]),
			) as (typeof allSlotsConfig)[keyof typeof allSlotsConfig];

			// Conditionally set sizes for WX_Hidden slot based on mobile vs desktop
			if (slotName === 'WX_Hidden') {
				if (isMobile) {
					// If mobile, only keep 'sm' size
					if (currSlotConfig.sizes?.lg) {
						delete currSlotConfig.sizes.lg;
					}
				} else {
					// If desktop, only keep 'lg' size
					if (currSlotConfig.sizes?.sm) {
						delete currSlotConfig.sizes.sm;
					}
				}
			}

			finalSlotsConfig[slotName] = currSlotConfig;
		}
	}

	const heliosConfig: HeliosConfigType = {
		generalConfig: {
			adServers: {
				gpt: {
					script: 'https://securepubads.g.doubleclick.net/tag/js/gpt.js',
				},
			},
			bidTimeout: 2200,
			breakpoint: {
				//"active": "lg",
				lg: 9999,
				md: 1024,
				sm: 767,
			},
			buffer: {
				'md-lg': 100,
				sm: 200,
			},
			cModules: {
				eum: {
					includeMetaKvs: [
						'pf',
						'hb_pb',
						'hb_bidder',
						'hb_pb_criteo',
						'hb_source',
						'crt_pb',
						'amznbid',
						'nonpf',
						'hb_adid',
						'hb_deal',
						'hb_size',
						'twcay',
						'ortb2usrseg',
					],
					includePrebidAdUnitCodes: '^WX_WindowShade＄',
					services: ['newrelic'],
				},
				iabc: {
					ortb2Site: {
						keywords:
							'science,weather,barbecues,grilling,health,fitness,exercise,running,jogging,wellness,home,garden,gardening,allergies,cold,flu,diabetes,lung,respiratory,skin,dermatology,pets,environment,space,astronomy,travel,adventure,road trips',
					},
					page: iabPageConfig,
					site: {
						v1: ['IAB15-10_Weather'],
						v3: ['390_Weather'],
					},
				},
				premiumAdDataLake: {
					alerts: null,
					dailyForecast: null,
					location: null,
					observation: null,
					pollen: null,
					unitCode: 'e',
				},
				videoTag: {
					activeVideoAdUnitPath: '/7646/web_weather_us/weather/news',
					duration: '00:24',
					hashedMpid: '',
					id: '46eb1752-1085-4de7-89e5-ae9f778d8d57',
					pcollid: 'news/weather',
					providername: 'TWC - Digital (No Distro)',
				},
				wfxtg: {
					acctId: 'qCtEFW9rBw',
					apiKey: 'lsiSzOE8:svXHzZSDifkHqcJ0Drfd9DGT',
					locale: 'en-US',
					locales: [
						'en-US',
						'en-GB',
						'en-CA',
						'en-IN',
						'hi-IN',
						'en-AU',
						'en-NZ',
					],
					locationId: null,
					segId: 'VfrxOi5vOV',
					timeout: 1000,
				},
				wxAdTargeting: {
					locale: 'en-US',
					placeId: null,
					wxApiHost: 'weather.com',
				},
				wxuuser: {
					on: true,
				},
			},
			hotfixVideoLocationChangeGraceTime: 500,
			modules: {
				confiant: {},
				//contentIgnite: {
				//	on: true,
				//},
				ias: {
					pubId: '8584',
				},
				//iqvia: {
				//	allowedLocaleRegexp: 'DISABLED',
				//	on: true,
				//},
				liveRampAts: {
					allowedLocaleRegexp: '^en-US＄',
					allowedRegimeRegexp: '^usa',
					bundleUrl:
						'https://launchpad.privacymanager.io/latest/launchpad.bundle.js',
					scriptUrl:
						'https://launchpad-wrapper.privacymanager.io/************************************/launchpad-liveramp.js',
				},
				optable: {
					ab: {
						chanceAbcd: {
							both: 0.91,
							liveintent: 0.03,
							none: 0.03,
							optable: 0.03,
						},
					},
					on: true,
				},
				qualityTargeting: {
					on: true,
				},
				refresh: {
					interactive: false,
					interval: 30000,
				},
			},
			specialSlotRules: [
				//{
				//	elementIds: ['MW_Position2', 'MW_Position3_Hourly', 'MW_Position4'],
				//	handler: 'rehydrate',
				//	matchPageKey: 'hourly',
				//},
				//{
				//	elementIds: [
				//		'WX_FlexLeader',
				//		'WX_MidLeader2',
				//		'WX_MidLeader3',
				//		'WX_MidLeader4',
				//		'WX_PromoDriver1',
				//		'WX_SpotLight',
				//	],
				//	handler: 'rehydrate',
				//	matchPageKey: '.*',
				//},
			],
			ssps: {
				amazon: {
					pubId: 1004,
				},
				demandManager: {
					scripts: ['micro.rubiconproject.com/prebid/dynamic/10738.js'],
				},
			},
			sub: {
				noAds: isUserPremium ?? false,
				tier: subscriptionTier,
			},
			targeting: {
				adUnits: isMobile ? 'mobile_smart_us' : 'web_weather_us',
				adZone: 'ros',
				experience: null,
				locale: 'en-US',
				networkCode: '/7646',
				pageKey: 'article',
				regime: 'usa',
				siteMode: null,
				variantKey: null,
				videoCount: 0,
				tf: 'nl',
				cf,
				env1: 'next',
			},
			useVideoAdRulesUrlWhenAdTagEmpty: false,
			video: {
				ssps: {
					amazon: {
						pubId: 1004,
						slotId: 'WX_Video_Player',
					},
					criteo: {
						mediaTypes: {
							video: {
								api: [1, 2],
								context: 'instream',
								maxduration: 30,
								mimes: ['video/mp4'],
								placement: 1,
								playbackmethod: [1, 2, 3, 4, 5, 6],
								playerSize: [640, 480],
								plcmt: 1,
								protocols: [1, 2, 3, 4, 5, 6],
								skip: 0,
							},
						},
						networkId: 2305,
						publisherId: '5471_V52EGS',
						slotId: 'WX_Video_Player',
					},
					demandManager: {
						slotName: '/7646/web_weather_us/video',
					},
				},
			},
		},
		slotsConfig: finalSlotsConfig,
	};

	if (thirdPartyConfigsFromPage) {
		thirdPartyConfigsFromPage.forEach((tpConfig) => {
			if (
				tpConfig &&
				tpConfig.thirdPartyConfig &&
				typeof tpConfig.thirdPartyConfig === 'object'
			) {
				const moduleConfig = tpConfig.thirdPartyConfig;
				const moduleName = moduleConfig.name;

				if (moduleName) {
					const newConfig = moduleConfig.thirdPartyConfigJson;
					heliosConfig.generalConfig.modules[moduleName] = {
						...(heliosConfig.generalConfig.modules[moduleName] || {}),
						...(typeof newConfig === 'object' && newConfig !== null
							? newConfig
							: {}),
						on: true,
					};
				}
			}
		});
	}

	if (adSlotsFromPage && adSlotsFromPage.length > 0) {
		adSlotsFromPage.forEach((pageSlot) => {
			if (pageSlot && pageSlot.name) {
				const slotName = pageSlot.name;
				const slotAdZoneForPath = currentAdZone;
				const slotSpecificAdUnitPath = getAdUnitPath(
					slotAdZoneForPath,
					isMobile,
					currentNetworkCode,
					currentAdUnits,
				);

				const formattedSizes: Record<string, Size | null> = {};
				if (pageSlot.adSizes && pageSlot.adSizes.length > 0) {
					pageSlot.adSizes.forEach(
						(s: { breakpoint?: string; width: number; height: number }) => {
							const bp = s.breakpoint || 'lg';
							if (s.width != null && s.height != null) {
								if (!formattedSizes[bp]) {
									formattedSizes[bp] = [];
								}
								(formattedSizes[bp] as Size).push([s.width, s.height]);
							}
						},
					);
				}

				const baseSlotConfig = heliosConfig.slotsConfig[slotName];
				heliosConfig.slotsConfig[slotName] = {
					adUnitPath: slotSpecificAdUnitPath,
					bidMode: baseSlotConfig?.bidMode || '',
					bids: baseSlotConfig?.bids || {},
					modules: baseSlotConfig?.modules || {},
					pos: baseSlotConfig?.pos || slotName,
					sizes:
						Object.keys(formattedSizes).length > 0
							? formattedSizes
							: baseSlotConfig?.sizes || { lg: null },
				};
			}
		});
	}
	return heliosConfig;
};

// Export for potential use with de-DE pages as mentioned in the TODO comment
export const getHeliosConfigBurda = ({
	isUserPremium,
	subscriptionTier,
}: HeliosConfigTransformerProps): HeliosConfigType => {
	// config for this article:
	//
	// id: 'e880d541-639e-4e16-aa2b-1784e10fb13d'
	// assetName: '/de-DE/wetter/deutschland/news/2025-03-11-ungewohnlich-mild-sylt-erlebt-warmsten-marztag-seit-1937
	return {
		generalConfig: {
			adServers: {
				burda: {
					layoutMap: {
						airQuality: 'detail',
						allergy: 'detail',
						article: 'videoarticle',
						home: 'homepage',
						hourly: 'detail',
						monthly: 'detail',
						radar: 'maps',
						tenday: 'detail',
						today: 'detail',
						video: 'videoarticle',
						weekend: 'detail',
					},
					script: 'https://a.bf-ad.net/adengine/twc/adengine.js',
				},
			},
			bidTimeout: 2200,
			breakpoint: {
				//"active": "lg",
				lg: 9999,
				md: 1024,
				sm: 767,
			},
			buffer: {
				'md-lg': 100,
				sm: 200,
			},
			cModules: {
				iabc: {
					ortb2Site: {
						keywords:
							'science,weather,barbecues,grilling,health,fitness,exercise,running,jogging,wellness,home,garden,gardening,allergies,cold,flu,diabetes,lung,respiratory,skin,dermatology,pets,environment,space,astronomy,travel,adventure,road trips',
					},
					page: {
						v1: [],
						v2: [],
						v3: [],
					},
					site: {
						v1: ['IAB15-10_Weather'],
						v3: ['390_Weather'],
					},
				},
				//"premiumAdDataLake": {
				//	"alerts": null,
				//	"dailyForecast": null,
				//	"location": null,
				//	"observation": null,
				//	"pollen": null,
				//	"unitCode": "m"
				//},
				wfxtg: {
					acctId: 'qCtEFW9rBw',
					apiKey: 'lsiSzOE8:svXHzZSDifkHqcJ0Drfd9DGT',
					locale: 'de-DE',
					locales: [
						'en-US',
						'en-GB',
						'en-CA',
						'en-IN',
						'hi-IN',
						'en-AU',
						'en-NZ',
					],
					locationId: null,
					segId: 'VfrxOi5vOV',
					timeout: 1000,
					userId: '-8591340546321025309',
				},
				wxAdTargeting: {
					locale: 'de-DE',
					placeId: null,
					wxApiHost: 'weather.com',
				},
				//"wxuperf": {
				//	"on": true
				//},
				wxuuser: {
					on: true,
				},
			},
			hotfixVideoLocationChangeGraceTime: 500,
			modules: {
				//"contentIgnite": {
				//	"on": true
				//},
				//"iqvia": {
				//	"allowedLocaleRegexp": "DISABLED",
				//	"on": true
				//},
				//"liveRampAts": {
				//	"allowedLocaleRegexp": "^en-US＄",
				//	"allowedRegimeRegexp": "^usa",
				//	"bundleUrl": "https://launchpad.privacymanager.io/latest/launchpad.bundle.js",
				//	"scriptUrl": "https://launchpad-wrapper.privacymanager.io/************************************/launchpad-liveramp.js"
				//},
				//"qualityTargeting": {
				//	"on": true
				//},
				refresh: {
					interactive: false,
					interval: 30000,
				},
			},
			specialSlotRules: [
				//{
				//	elementIds: ['MW_Position2', 'MW_Position3_Hourly', 'MW_Position4'],
				//	handler: 'rehydrate',
				//	matchPageKey: 'hourly',
				//},
				//{
				//	elementIds: [
				//		'WX_FlexLeader',
				//		'WX_MidLeader2',
				//		'WX_MidLeader3',
				//		'WX_MidLeader4',
				//		'WX_PromoDriver1',
				//		'WX_SpotLight',
				//	],
				//	handler: 'rehydrate',
				//	matchPageKey: '.*',
				//},
			],
			ssps: {},
			sub: {
				noAds: isUserPremium ?? false,
				tier: subscriptionTier,
			},
			targeting: {
				adUnits: 'wx_digital',
				adZone: 'article',
				cf: 'a',
				env1: 'next',
				experience: null,
				locale: 'de-DE',
				networkCode: '/3673,7646',
				pageKey: 'article',
				regime: 'usa',
				siteMode: null,
				tf: 'nl',
				variantKey: null,
				videoCount: null,
			},
			useVideoAdRulesUrlWhenAdTagEmpty: false,
		},
		slotsConfig: {
			DE_DESKTOP_BANNER_TOP: {
				adUnitPath: '/3673,7646/wx_digital/news/germany',
				bidMode: '',
				bids: {},
				modules: {},
				pos: 'DESKTOP_BANNER_TOP',
				sizes: {
					lg: null,
				},
			},
			DE_DESKTOP_LEADER1: {
				adUnitPath: '/3673,7646/wx_digital/news/germany',
				bidMode: '',
				bids: {},
				modules: {},
				pos: 'DESKTOP_BANNER_ANY',
				sizes: {
					lg: null,
				},
			},
			DE_DESKTOP_RECTANGLE1: {
				adUnitPath: '/3673,7646/wx_digital/news/germany',
				bidMode: '',
				bids: {},
				modules: {},
				pos: 'DESKTOP_RECTANGLE_SINGLE_ANY',
				sizes: {
					lg: null,
				},
			},
			DE_DESKTOP_TOP_RECTANGLE: {
				adUnitPath: '/3673,7646/wx_digital/news/germany',
				bidMode: '',
				bids: {},
				modules: {},
				pos: 'DESKTOP_RECTANGLE_ANY',
				sizes: {
					lg: null,
				},
			},
			WX_Hidden: {
				adUnitPath: '/3673,7646/wx_digital/news/germany',
				bidMode: '',
				bids: {},
				modules: {},
				pos: 'wx_hdn',
				sizes: {
					lg: [[1, 1]],
				},
			},
		},
	};
};
