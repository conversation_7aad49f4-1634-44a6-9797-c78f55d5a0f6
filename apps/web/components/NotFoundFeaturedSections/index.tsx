'use client';

import React from 'react';
import Link from '@repo/ui/components/Link/Link';
import { Image } from '@repo/ui/components/Image/Image';
import { useLocationSource } from '@/location/hooks/useLocationSource';
import { getClientSideURL } from '@/utils/getURL';

export const NotFoundFeaturedSections: React.FC = () => {
	// Use the hook to get location data including placeId
	const { effectiveLocation } = useLocationSource({});

	// Generate location-based URLs
	const localWeatherUrl = effectiveLocation?.placeId
		? `${getClientSideURL()}/weather/today/l/${effectiveLocation.placeId}`
		: '/weather';

	const mapsUrl = effectiveLocation?.placeId
		? `${getClientSideURL()}/weather/radar/interactive/l/${effectiveLocation.placeId}`
		: '/';

	return (
		<div className="mt-10 grid grid-cols-1 gap-8 md:grid-cols-3">
			{/* Video Section */}
			<Link href="/video" className="block">
				<div className="flex flex-col overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm hover:shadow-md">
					<div className="relative h-48 w-full">
						<Image
							src="https://s.w-x.co/404-video.jpg"
							alt="Video thumbnail"
							fill
							sizes="(max-width: 768px) 100vw, 33vw"
							objectFit="cover"
						/>
					</div>
					<div className="flex flex-1 flex-col p-3">
						<h3 className="mb-2 text-center text-xl font-semibold">Video</h3>
						<p className="text-center">
							Watch today&lsquo;s top stories and most popular videos. Looking
							to watch incredible tornadoes? We&lsquo;ve got that too.
						</p>
					</div>
				</div>
			</Link>

			{/* Maps Section - updated with location-based URL */}
			<Link href={mapsUrl} className="block">
				<div className="flex flex-col overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm hover:shadow-md">
					<div className="relative h-48 w-full">
						<Image
							src="https://s.w-x.co/404-maps.jpg"
							alt="Weather maps"
							fill
							sizes="(max-width: 768px) 100vw, 33vw"
							objectFit="cover"
						/>
					</div>
					<div className="flex flex-1 flex-col p-3">
						<h3 className="mb-2 text-center text-xl font-semibold">Maps</h3>
						<p className="text-center">
							Check out our interactive maps, which provide you with features
							like past/future radar and customizable layers.
						</p>
					</div>
				</div>
			</Link>

			{/* Local Weather Section - updated with location-based URL */}
			<Link href={localWeatherUrl} className="block">
				<div className="flex flex-col overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm hover:shadow-md">
					<div className="relative h-48 w-full">
						<Image
							src="https://s.w-x.co/404-local.jpg"
							alt="Local weather"
							fill
							sizes="(max-width: 768px) 100vw, 33vw"
							objectFit="cover"
						/>
					</div>
					<div className="flex flex-1 flex-col p-3">
						<h3 className="mb-2 text-center text-xl font-semibold">
							Local Weather
						</h3>
						<p className="text-center">
							Trust us to help you plan the best day possible, with the most
							accurate weather forecast available.
						</p>
					</div>
				</div>
			</Link>
		</div>
	);
};

export default NotFoundFeaturedSections;
