import Text from '@repo/ui/components/Text/Text';
import Image from '@repo/ui/components/Image/Image';
import { formatDateWithOffset } from '@/utils/formatDateWithOffset';
import Link from '@repo/ui/components/Link/Link';
import { PlayOutline } from '@repo/icons/Control';
import ListFooter from './ListFooter';
import { DSXCollection } from '@repo/dal/content/collections/types';
import { DsxCollectionItem } from '@/app/(web)/[code]/content/classic/utils/transformDsxCollectionItem';

interface ArticlesListProps {
	page?: number;
	items: DsxCollectionItem[];
	collection: DSXCollection;
	hasNextPage?: boolean;
	locale: string;
}

function ArticleListItem({
	item,
}: {
	item: DsxCollectionItem;
	locale: string;
}) {
	const {
		description,
		featuredImage,
		assetName,
		title,
		publishDate,
		subHeadline,
		type,
	} = item;

	return (
		<Link
			href={assetName}
			className="group flex flex-1 p-4 text-base tracking-normal no-underline"
		>
			<div className="grid w-full grid-cols-12 gap-4">
				<div className="col-span-12 sm:col-span-3">
					<div
						className="relative aspect-[1/1] max-h-32 w-full rounded-md md:max-h-24"
						style={{
							background:
								'linear-gradient(135deg, #f3f3f3, #e5e5e5 50%, #e5e5e5 51%, #f3f3f3)',
						}}
					>
						<Image
							src={featuredImage.url}
							alt={featuredImage.seo.altText ?? featuredImage.seo.caption}
							quality={60}
							fill
							rounded
							className="rounded-md"
							objectFit="cover"
							sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
						/>
						{type === 'video' && (
							<div className="absolute inset-0 flex items-center justify-center">
								<PlayOutline size="lg" color="inverse" />
							</div>
						)}
					</div>
				</div>
				<div className="col-span-12 flex sm:col-span-9">
					<div className="flex flex-col gap-1.5">
						<Text
							elementType="h2"
							className="line-clamp-2 text-base font-bold leading-5 group-hover:text-[#1B4DE4]"
						>
							{title}
						</Text>
						{publishDate && (
							<Text className="text-sm font-normal text-[#6f7585]">
								{formatDateWithOffset(publishDate, {
									daysFromNow: 7,
									showFullTime: true,
								})}
							</Text>
						)}
						<Text className="line-clamp-2 text-sm font-normal">
							{description || subHeadline}
						</Text>
					</div>
				</div>
			</div>
		</Link>
	);
}

export default function ArticlesList({
	collection,
	items,
	page = 1,
	hasNextPage,
	locale,
}: ArticlesListProps) {
	return (
		<div className="flex flex-col">
			<ul className="m-0 flex list-none flex-col p-0">
				{items
					?.filter((item) => !!item.assetName)
					.map((item) => (
						<li key={item.id} className="m-0 flex p-0">
							<ArticleListItem item={item} locale={locale} />
						</li>
					))}
			</ul>
			<div className="mx-4 mt-4">
				<ListFooter
					page={page}
					hasPreviousPage={page > 1}
					hasNextPage={hasNextPage}
					pathname={collection.id}
					locale={locale}
				/>
			</div>
		</div>
	);
}
