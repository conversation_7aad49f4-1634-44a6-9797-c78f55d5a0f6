import { ChevronDown } from '@repo/icons/Navigation';
import Link from '@repo/ui/components/Link/Link';
import { cn } from '@repo/ui/lib/utils';
import { PropsWithChildren } from 'react';

interface PaginationButtonProps extends PropsWithChildren {
	rel?: string;
	isActive?: boolean;
	href: string;
	disabled?: boolean;
}

function PaginationButton({
	rel,
	isActive,
	href,
	children,
	disabled,
}: PaginationButtonProps) {
	if (disabled) {
		return (
			<button disabled className="cursor-pointer text-[#252422CC] opacity-40">
				{children}
			</button>
		);
	}

	return (
		<Link
			rel={rel}
			href={href}
			className={cn(
				'cursor-pointer text-[#1b4de4] hover:text-[#252422CC]',
				!isActive && 'opacity-40',
			)}
		>
			{children}
		</Link>
	);
}

export interface ListFooterProps {
	page: number;
	pathname: string;
	hasNextPage?: boolean;
	locale: string;
	hasPreviousPage?: boolean;
}

export default function ListFooter({
	page,
	pathname,
	hasPreviousPage,
	hasNextPage,
	locale,
}: ListFooterProps) {
	const nextPage = page + 1;
	const prevPage = page > 1 ? page - 1 : page;

	let basePathname = pathname;

	if (locale !== 'en-US') {
		basePathname = `${locale}/${pathname}`;
	}

	return (
		<div className="flex items-center justify-between border-t">
			<PaginationButton
				rel="prev"
				href={`/${basePathname}?pg=${prevPage}`}
				isActive={hasPreviousPage}
				disabled={!hasPreviousPage}
			>
				<div className="flex items-center gap-1 py-4">
					<div className="rotate-90">
						<ChevronDown className="h-5 w-5 text-inherit" />
					</div>
					<span>Previous</span>
				</div>
			</PaginationButton>

			<PaginationButton
				rel="next"
				href={`/${basePathname}?pg=${nextPage}`}
				isActive={hasNextPage}
				disabled={!hasNextPage}
			>
				<div className="flex items-center gap-1 py-4">
					<span>Next</span>
					<div className="-rotate-90">
						<ChevronDown className="h-5 w-5 text-inherit" />
					</div>
				</div>
			</PaginationButton>
		</div>
	);
}
