import { getLexicalDsxArticlesByCollectionId } from '@/app/(web)/[code]/content/classic/utils/getLexicalDsxArticle';
import { DSXCollection } from '@repo/dal/content/collections/types';
import Text from '@repo/ui/components/Text/Text';
import { collectionTitlesByPCollId } from '../constants';
import { formatDateWithOffset } from '@/utils/formatDateWithOffset';
import Image from '@repo/ui/components/Image/Image';
import Link from '@repo/ui/components/Link/Link';
import { PlayOutline } from '@repo/icons/Control';
import { VideoArticle } from '@repo/icons/Deprecated';
import { TextArticle } from '@repo/icons/Deprecated';
import { cn } from '@repo/ui/lib/utils';
import type { DsxCollectionItem } from '@/app/(web)/[code]/content/classic/utils/transformDsxCollectionItem';

interface MainSectionProps {
	collection: DSXCollection;
	locale: string;
}

interface ArticleItemProps {
	item: DsxCollectionItem;
	locale: string;
}

function HeroArticleItem({ item }: ArticleItemProps) {
	const { assetName, featuredImage, title, publishDate, type } = item;

	return (
		<Link href={assetName} className="group no-underline">
			<div className="flex flex-col gap-3">
				<div className="relative aspect-[16/9] w-full">
					<Image
						src={featuredImage.url}
						alt={featuredImage.seo.altText ?? featuredImage.seo.caption}
						fill
						style={{
							background:
								'linear-gradient(135deg, #f3f3f3, #e5e5e5 50%, #e5e5e5 51%, #f3f3f3)',
						}}
						sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
					/>
					{type === 'video' && (
						<div className="absolute inset-0 flex items-center justify-center">
							<PlayOutline size="lg" color="inverse" />
						</div>
					)}
				</div>
				<div className="flex flex-col gap-4 group-hover:underline">
					<Text
						elementType="h3"
						variant="Title.M"
						className="line-clamp-3 font-normal leading-8"
					>
						{title}
					</Text>
					<Text className="text-sm font-normal text-[#686c74]">
						Published on {formatDateWithOffset(publishDate, { timeAgo: false })}
					</Text>
				</div>
			</div>
		</Link>
	);
}

function ArticleItem({ item }: ArticleItemProps) {
	const { assetName, featuredImage, title, type } = item;

	return (
		<Link href={assetName!} className="group no-underline">
			<div className="flex flex-col gap-2">
				<div>
					<div className="relative aspect-[16/9] w-full">
						<Image
							src={featuredImage.url}
							alt={featuredImage.seo.altText ?? featuredImage.seo.caption}
							fill
							style={{
								background:
									'linear-gradient(135deg, #f3f3f3, #e5e5e5 50%, #e5e5e5 51%, #f3f3f3)',
							}}
							sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
						/>
						{type === 'video' && (
							<div className="absolute inset-0 flex items-center justify-center">
								<PlayOutline size="lg" color="inverse" />
							</div>
						)}
					</div>
				</div>
				<Text
					elementType="h3"
					variant="Title.M"
					className="line-clamp-3 text-[0.9375rem] font-normal leading-7 group-hover:underline"
				>
					{title}
				</Text>
			</div>
		</Link>
	);
}

export default async function MainSection({
	collection,
	locale,
}: MainSectionProps) {
	const items = await getLexicalDsxArticlesByCollectionId({
		id: collection.id,
		locale,
		limit: 8,
	});

	if (!items?.length) {
		return null;
	}

	const [head, ...tail] = items;
	const list = tail.slice(3);

	return (
		<div className="@container">
			<header className="py-6">
				<Text variant="Title.S" elementType="h2">
					{collectionTitlesByPCollId[collection.id] ?? collection.title}
				</Text>
			</header>
			<div className="grid grid-cols-3 gap-4">
				<div className="@sm:col-span-2 col-span-3 row-span-1">
					{head && <HeroArticleItem locale={locale} item={head} />}
				</div>
				<div className="@sm:col-span-1 col-span-3 row-span-3">
					<div className="flex flex-col gap-8">
						{tail.slice(0, 3).map((item) => (
							<ArticleItem locale={locale} key={item.id} item={item} />
						))}
					</div>
				</div>
				<div className="@sm:col-span-2 col-span-3 row-span-1 pb-4">
					<ul className="m-0 flex flex-col px-0 py-4">
						{list.map((item, index) => (
							<li
								key={item.id}
								className={cn(
									'm-0 flex p-0',
									index !== list.length - 1 && 'border-b',
								)}
							>
								<Link
									href={item.assetName!}
									className="flex flex-1 p-4 hover:underline"
								>
									<div className="flex gap-2.5">
										{item.type === 'video' ? <VideoArticle /> : <TextArticle />}
										<div className="flex flex-1">
											<Text className="line-clamp-3 font-normal">
												{item.title}
											</Text>
										</div>
									</div>
								</Link>
							</li>
						))}
					</ul>
				</div>
			</div>
		</div>
	);
}
