import { DSXCollection } from '@repo/dal/content/collections/types';
import Text from '@repo/ui/components/Text/Text';
import Link from '@repo/ui/components/Link/Link';
import { getCollectionById } from '@repo/dal/content/collections/byCollectionId';
import { cn } from '@repo/ui/lib/utils';
import Section from './section';
import { HEALTH_INDEX_GROUP } from '../constants';
import { PropsWithChildren } from 'react';
import MainSection from './main';

interface HealthIndexProps {
	locale: string;
	collection: DSXCollection;
}

interface NavLinkProps extends PropsWithChildren {
	isActive?: boolean;
	href: string;
	prefetch?: boolean;
	locale: string;
}

const NavLink = ({
	locale,
	isActive,
	href,
	children,
	prefetch = true,
}: NavLinkProps) => {
	const hrefWithLocale = locale === 'en-US' ? href : `/${locale}${href}`;

	return (
		<Link
			mpa={false}
			prefetch={prefetch}
			href={hrefWithLocale}
			className={cn(
				'text-nowrap rounded-lg px-5 py-1.5 text-sm font-normal',
				isActive
					? 'bg-primary text-primary-foreground hover:bg-primary/90 rounded-md'
					: 'hover:bg-accent hover:text-accent-foreground border text-[#6f7585]',
			)}
		>
			{children}
		</Link>
	);
};

export default async function HealthIndex({
	locale,
	collection,
}: HealthIndexProps) {
	const [main, ...sections] = await Promise.all(
		HEALTH_INDEX_GROUP.map((id) => {
			if (id === collection.id) {
				return collection;
			}

			return getCollectionById(id, locale);
		}),
	);

	return (
		<div className="flex flex-col gap-1 py-2">
			<div>
				<header className="p-4">
					<Text elementType="h1" variant="Title.S">
						Health & Wellness
					</Text>
				</header>
				<div className="px-4">
					<Text className="text-base font-normal">
						Discover valuable insights about the connection between weather and
						your well-being, and get inspired on your journey to a healthier
						lifestyle.
					</Text>
				</div>
				<div className="flex flex-wrap gap-3 p-4">
					<NavLink
						locale={locale}
						href={`/${main!.id}`}
						isActive={main!.id === collection.id}
					>
						Main
					</NavLink>
					{sections.map((section) => (
						<NavLink
							locale={locale}
							key={section.id}
							href={`/${section.id}`}
							isActive={section.id === collection.id}
						>
							{section.title}
						</NavLink>
					))}
				</div>
			</div>

			<div>
				<div className="px-4">
					<div className="flex flex-col gap-4 border-t">
						<MainSection collection={collection} locale={locale} />
						<div className="border-t">
							{sections
								.filter((member) => member.id !== collection.id)
								.map((member) => (
									<div key={member.id}>
										<Section collection={member} locale={locale} />
									</div>
								))}
							{main && (
								<Section
									title="More Health & Wellness News"
									collection={main}
									locale={locale}
								/>
							)}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
