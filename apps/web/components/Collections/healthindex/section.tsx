import { getLexicalDsxArticlesByCollectionId } from '@/app/(web)/[code]/content/classic/utils/getLexicalDsxArticle';
import { DSXCollection } from '@repo/dal/content/collections/types';
import Text from '@repo/ui/components/Text/Text';
import Link from '@repo/ui/components/Link/Link';
import Image from '@repo/ui/components/Image/Image';
import { PlayOutline } from '@repo/icons/Control';
import { DsxCollectionItem } from '@/app/(web)/[code]/content/classic/utils/transformDsxCollectionItem';

interface SectionProps {
	title?: string;
	collection: DSXCollection;
	locale: string;
}

interface ArticleListItemProps {
	item: DsxCollectionItem;
	locale: string;
}

function ArticleListItem({ item, locale }: ArticleListItemProps) {
	const { featuredImage, assetName, title, type } = item;

	const href = locale === 'en-US' ? assetName : `/${locale}${assetName}`;

	return (
		<Link href={href} className="group flex no-underline">
			<div className="flex flex-col gap-2 pb-4 text-base tracking-normal">
				<div className="relative aspect-[16/9] w-full">
					<Image
						alt={title}
						fill
						src={featuredImage.url}
						style={{
							background:
								'linear-gradient(135deg, #f3f3f3, #e5e5e5 50%, #e5e5e5 51%, #f3f3f3)',
						}}
						sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
					/>
					{type === 'video' && (
						<div className="absolute inset-0 flex items-center justify-center">
							<PlayOutline size="lg" color="inverse" />
						</div>
					)}
				</div>
				<Text
					elementType="h3"
					variant="Body.S"
					className="line-clamp-3 font-normal group-hover:underline"
				>
					{title}
				</Text>
			</div>
		</Link>
	);
}

export default async function Section({
	title,
	collection,
	locale,
}: SectionProps) {
	const items = await getLexicalDsxArticlesByCollectionId({
		id: collection.id,
		locale,
		limit: 4,
	});

	if (!items?.length) {
		return null;
	}

	return (
		<section className="@container py-2">
			<header className="py-4">
				<Text elementType="h2" variant="Title.S">
					{title ?? collection.title}
				</Text>
			</header>
			<div className="grid grid-cols-4 gap-4">
				{items.map((item) => (
					<div
						key={item.id}
						className="@sm:col-span-2 @md:col-span-1 col-span-4"
					>
						<ArticleListItem item={item} locale={locale} />
					</div>
				))}
			</div>
		</section>
	);
}
