import Link from '@repo/ui/components/Link/Link';
import React, { forwardRef, useState, useMemo } from 'react';
import { useLocationSource } from '@/location/hooks/useLocationSource';
import { useUser } from '@/user/hooks/useUser';
import { cn } from '@repo/ui/lib/utils';
import { ExternalLink } from '@repo/icons/Deprecated';
import { Minus } from '@repo/icons/Operations';
import { Plus } from '@repo/icons/Operations';
import { useRefMemo } from '@/hooks/useRefMemo';
import { useMobileMedia } from '@/hooks/useMobileMedia';
import useSWR from 'swr';
import { getVideoLink } from '@/components/Header/utils/getVideoLink';
import { useAtomValue } from 'jotai';
import SettingsMenu from '@/components/Header/SettingsMenu';
import { pageLocaleAtom } from '@/atoms/pageLocale';

interface NavigationLink {
	name: string;
	url?: string;
	action?: () => void;
	isNew?: boolean;
	isExternal?: boolean;
}

interface NavigationCategories {
	[key: string]: NavigationLink[];
}

const Navigation = forwardRef<HTMLDivElement>((props, ref) => {
	// State to track if settings dropdown is open
	const [isSettingsDropdownOpen, setIsSettingsDropdownOpen] = useState(false);

	const { effectiveLocation } = useLocationSource({});
	const {
		user: { isUserLoggedIn },
		logout,
	} = useUser();
	const logoutRef = useRefMemo(logout);
	const placeId = effectiveLocation?.placeId || '';
	const pageLocale = useAtomValue(pageLocaleAtom);
	const { data: videoLink } = useSWR(
		['navigation/getVideoLink', pageLocale],
		([_, locale]) => getVideoLink(locale),
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
			shouldRetryOnError: true,
			errorRetryCount: 2,
			errorRetryInterval: 2000,
		},
	);

	const [expandedCategories, setExpandedCategories] = useState<
		Record<string, boolean>
	>({});
	const isMobile = useMobileMedia();

	// Toggle category expansion
	const toggleCategory = (category: string) => {
		setExpandedCategories((prev) => ({
			...prev,
			[category]: !prev[category],
		}));
	};

	const navigationCategories: NavigationCategories = useMemo(
		() => ({
			'Weather Forecasts': [
				{
					name: 'My Dashboard',
					url: '/mydashboard',
					isNew: false,
				},
				{ name: 'Today', url: `/weather/today/l/${placeId}` },
				{ name: 'Hourly', url: `/weather/hourbyhour/l/${placeId}` },
				{ name: '10 Day', url: `/weather/tenday/l/${placeId}` },
			],
			'Radar & Maps': [
				{
					name: 'Interactive Radar Map',
					url: `/weather/radar/interactive/l/${placeId}?animation=true`,
				},
				{ name: 'US Forecast', url: '/maps/currentusweather' },
				{ name: 'US Satellite & Radar', url: '/maps/ussatellitemap' },
				{
					name: 'World Satellite',
					url: '/maps/satellite/caribbeanweathermap',
				},
				{ name: 'US Severe Alerts', url: '/maps/severealerts' },
				{ name: 'US Health', url: '/maps/health/allergies/treepollen' },
			],
			'News & Media': [
				{ name: 'Top Weather Stories', url: '/news' },
				{ name: 'Hurricane Central', url: '/storms/hurricane-central' },
				{
					name: 'Tornado Central',
					url: '/storms/tornado-central',
					isNew: Date.now() < 1757808000000, // New until 2025-09-14
				},
				{ name: 'Safety & Prep', url: '/safety/index' },
				{ name: 'Space & Skywatching', url: '/science/space' },
				...(videoLink ? [{ name: 'Videos', url: videoLink }] : []),
			],
			Lifestyle: [
				{ name: 'Atmosphere Reviews', url: '/atmosphere' },
				{ name: 'Recipes', url: '/home-garden/recipes' },
				{ name: 'Home & Garden', url: '/home-garden' },
				{ name: 'Travel & Outdoors', url: '/travel' },
				{ name: 'Pets & Animals', url: '/pets' },
				{ name: 'Health', url: '/health' },
				{ name: 'Allergy Tracker', url: `/forecast/allergy/l/${placeId}` },
				{
					name: 'Air Quality Index',
					url: `/forecast/air-quality/l/${placeId}`,
				},
				{
					name: 'Cold & Flu Tracker',
					url: `/forecast/cold-flu/l/${placeId}`,
				},
				{ name: 'Skin Health', url: '/health/skin-health' },
			],
			// categories with no links will not be displayed, but their space will be preserved
			skip: [],
			skip2: [],
			'Products & Account': [
				{
					name: 'Alexa Skill',
					url: 'https://www.amazon.com/The-Weather-Company-Channel/dp/B07YPYHQ1Q',
					isExternal: true,
				},
				...(isUserLoggedIn
					? [
							{ name: 'Manage Account', url: '/member/settings' },
							{ name: 'Logout', action: logoutRef.current },
						]
					: [
							{ name: 'Create An Account', url: '/signup' },
							{ name: 'Sign in', url: '/login' },
						]),
			],
		}),
		[isUserLoggedIn, logoutRef, placeId, videoLink],
	);

	const cols = 4;
	const navColumns = useMemo(
		() =>
			Object.entries(navigationCategories).reduce(
				(columns, entry, ix) => {
					columns[ix % cols]?.push(entry);
					return columns;
				},
				Array.from({ length: cols }, (): [string, NavigationLink[]][] => []),
			),
		[navigationCategories, cols],
	);

	return (
		<nav
			ref={ref}
			className="max-h-(--nav-menu-height) md:max-h-(--nav-menu-height-md) absolute left-0 right-0 top-full z-40 w-full overflow-y-auto border-t border-gray-200 bg-white shadow-md md:py-8"
			data-testid="header-nav-menu"
		>
			{isMobile && (
				<div className="px-6 pt-4">
					<h2 className="text-xl font-semibold text-gray-900">Menu</h2>
					<SettingsMenu
						inNavigation={true}
						onDropdownToggle={setIsSettingsDropdownOpen}
					/>
				</div>
			)}

			{(!isSettingsDropdownOpen || !isMobile) && (
				<div
					className="mx-auto flex w-full max-w-7xl flex-col justify-center md:flex-row md:gap-3 md:px-4"
					data-testid="header-nav-content"
				>
					{navColumns.map((columnNavLinks, ix) => (
						<div key={ix} className="flex w-full flex-col md:w-1/4">
							{columnNavLinks.map(([category, links]) =>
								!links?.length ? null : (
									<div key={category} className="w-full md:mb-6">
										<div
											className={cn(
												isMobile && 'px-6 py-2.5',
												'flex cursor-pointer items-center justify-between md:cursor-default',
											)}
											{...(isMobile && {
												onClick: () => toggleCategory(category),
												role: 'button',
												tabIndex: 0,
												'aria-label': 'Toggle Menu Category',
											})}
										>
											<h2
												className={cn(
													!isMobile && 'mb-4',
													'text-gray-900 md:font-bold',
												)}
											>
												{category}
											</h2>
											{isMobile && (
												<span
													aria-label={
														expandedCategories[category] ? 'Collapse' : 'Expand'
													}
												>
													{expandedCategories[category] ? (
														<Minus size="md" className="text-black" />
													) : (
														<Plus size="md" className="text-black" />
													)}
												</span>
											)}
										</div>
										<ul
											className={cn(
												'md:space-y-2',
												isMobile && 'bg-[#ebecee]',
												isMobile && !expandedCategories[category] && 'hidden',
											)}
										>
											{links.map((link) => {
												if (!link.url && !link.action) return null;

												const linkContent = (
													<>
														<span className="relative flex items-center">
															{link.name}
															{link.isNew && (
																<span className="ml-2 inline-block rounded bg-black px-1.5 py-0.5 text-xs font-medium text-white">
																	NEW
																</span>
															)}
														</span>
														{link.isExternal && (
															<ExternalLink
																size="sm"
																data-testid="external-link-icon"
															/>
														)}
													</>
												);
												const linkClassName = cn(
													'group flex items-center gap-1 text-gray-700 hover:text-blue-600',
													isMobile && 'px-10 py-2.5',
												);

												return (
													<li
														key={link.name}
														className={
															isMobile
																? 'border-b border-[rgba(37,36,34,0.10)]'
																: ''
														}
													>
														{link.url ? (
															<Link
																href={link.url}
																className={linkClassName}
																target={link.isExternal ? '_blank' : undefined}
															>
																{linkContent}
															</Link>
														) : (
															<button
																onClick={link.action}
																className={cn(linkClassName, 'cursor-pointer')}
															>
																{linkContent}
															</button>
														)}
													</li>
												);
											})}
										</ul>
									</div>
								),
							)}
						</div>
					))}
				</div>
			)}
		</nav>
	);
});

Navigation.displayName = 'Navigation';

export default Navigation;
