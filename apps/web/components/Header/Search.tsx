'use client';

import { useCallback, useState, useEffect, useRef, useMemo } from 'react';
import { usePageNavigation } from '@repo/ui/hooks/usePageNavigation';
import { Input } from '@repo/ui/components/Input/DeprecatedInput';
import { X as CloseIcon } from 'lucide-react';
import RecentSearches, { RecentSearch } from './RecentSearches';
import { getLocationsByQuery } from '@repo/dal/locations/search';
import useSWR from 'swr';
import { useAtom } from 'jotai';
import { wxuUserRecentLocationsAtom } from '@/user/atoms/wxu/user';
import { cn } from '@repo/ui/lib/utils';
import { useDebounce } from '@/hooks/useDebounce';
import { formatPresentationName } from '@/location/utils/formatPresentationName';

interface SearchProps {
	className?: string;
}

export default function Search({ className }: SearchProps) {
	const { navigate } = usePageNavigation();
	const [searchQuery, setSearchQuery] = useState('');
	const debouncedQuery = useDebounce(searchQuery, 200);
	const [isRecentSearchesOpen, setIsRecentSearchesOpen] = useState(false);
	const [recentLocations, setRecentLocations] = useAtom(
		wxuUserRecentLocationsAtom,
	);
	// const setHasUserChangedLocation = useSetAtom(hasUserChangedLocationAtom); TODO; Phase 2
	const searchContainerRef = useRef<HTMLDivElement>(null);

	// Determine placeholder text based on location priority (same as CurrentConditions)
	const placeholderText = 'Search City or Zip Code';

	const { data } = useSWR(
		debouncedQuery.length >= 2 ? ['locations', debouncedQuery] : null,
		(args: [string, string]) => {
			return getLocationsByQuery(args[1]);
		},
		{
			revalidateOnFocus: false,
			dedupingInterval: 2000,
		},
	);

	// Transform API response to RecentSearch format
	const recentSearches: RecentSearch[] = useMemo(
		() =>
			data
				? data.slice(0, 5).map((location, index) => {
						return {
							id: location.placeId,
							city: location.city || location.displayName || 'Unknown',
							location: location.adminDistrictCode
								? `${location.adminDistrictCode}${location.country ? ', ' + location.country : ''}`
								: location.country || '',
							presentationName: formatPresentationName(location),
							temperature: 0, // Will be populated by WeatherDisplay component
							iconCode: 44, // Default to 'na' (not available)
							geocode: `${location.latitude},${location.longitude}`, // Add geocode for weather data fetching
							isPrimary: index === 0, // First result is primary
						};
					})
				: [],
		[data],
	);

	useEffect(() => {
		function handleClickOutside(event: MouseEvent) {
			if (
				searchContainerRef.current &&
				!searchContainerRef.current.contains(event.target as Node)
			) {
				setIsRecentSearchesOpen(false);
			}
		}

		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, []);

	const handleClearRecentLocations = useCallback(() => {
		setRecentLocations([]);
	}, [setRecentLocations]);

	const handleSelectSearch = useCallback(
		(search: RecentSearch) => {
			setIsRecentSearchesOpen(false);

			// Informs mParticle to send the "location-viewed" event when the new location
			// is loaded.
			// setHasUserChangedLocation(true); TODO; Phase 2

			// Add to recent locations when selected
			if (search.id && search.geocode) {
				// Add to recent locations, avoid duplicates, and limit to 5
				setRecentLocations((prev) => {
					const filtered = prev?.filter((loc) => loc !== search.id) || [];
					return [search.id, ...filtered].slice(0, 5);
				});
			}

			// Navigate to the weather page for this location
			if (search.id) {
				navigate(`/weather/today/l/${search.id}`);
			}
		},
		[navigate, setRecentLocations],
	);

	const handleRemoveRecentSearchLocation = useCallback(
		(search: RecentSearch) => {
			setRecentLocations((prev) => {
				return prev?.filter((loc) => loc !== search.id);
			});
		},
		[setRecentLocations],
	);

	return (
		<div
			className={cn(
				isRecentSearchesOpen &&
					'bg-brand-400 fixed left-0 right-0 z-50 px-2 sm:static sm:bg-transparent sm:p-0',
				'w-full transition-all sm:transition-none',
			)}
			data-testid="header-search"
		>
			<div
				ref={searchContainerRef}
				className={cn(
					'relative w-full sm:flex sm:max-w-md sm:flex-1',
					className,
				)}
			>
				<Input
					value={searchQuery}
					placeholder={placeholderText}
					onChange={(e) => {
						const value = e.target.value;
						setSearchQuery(value);
					}}
					onClick={() => {
						setIsRecentSearchesOpen(true);
					}}
					className="w-full cursor-pointer overflow-ellipsis rounded-sm border-none !bg-[#293248] py-2 pl-4 pr-10 text-center text-[1rem] text-white transition-colors placeholder:text-white focus:placeholder-transparent focus-visible:ring-2 focus-visible:ring-white sm:h-[50px] md:cursor-auto md:rounded-lg md:text-[1rem]"
				/>
				<div className="absolute right-3 top-1/2 flex -translate-y-1/2 transform items-center">
					{isRecentSearchesOpen ? (
						<button
							tabIndex={0}
							className="cursor-pointer"
							onClick={(event) => {
								event.stopPropagation();

								setIsRecentSearchesOpen(false);
								setSearchQuery('');
							}}
						>
							<CloseIcon className="h-6 w-6 text-white" />
						</button>
					) : (
						<svg
							name="location-2"
							className="h-6 w-6 text-white"
							fill="none"
							xmlns="http://www.w3.org/2000/svg"
							viewBox="0 0 21 20"
						>
							<path
								fillRule="evenodd"
								clipRule="evenodd"
								d="M8.757 3.54c2.284 0 4.142 1.866 4.142 4.16 0 2.293-1.858 4.16-4.142 4.16-2.283 0-4.141-1.867-4.141-4.16 0-2.294 1.858-4.16 4.141-4.16Zm4.467 8.18a6.016 6.016 0 0 0 1.54-4.02c0-3.327-2.694-6.034-6.007-6.034-3.312 0-6.007 2.707-6.007 6.034s2.695 6.033 6.007 6.033a5.943 5.943 0 0 0 3.023-.828l4.327 5.1a.93.93 0 0 0 1.316.104.94.94 0 0 0 .104-1.32l-4.303-5.07Z"
								fill="#fff"
							></path>
						</svg>
					)}
				</div>
				{isRecentSearchesOpen && (
					<div className="absolute left-0 right-0 top-full z-[80] mt-1 max-h-96 overflow-y-auto rounded-md bg-white text-gray-800 shadow-lg sm:mx-auto sm:mt-2 sm:max-w-md">
						<RecentSearches
							searches={recentSearches}
							showFavorites={!recentSearches.length}
							recentLocations={recentLocations}
							onClearAll={() => {
								// Since we're using SWR, we can't directly clear the cache
								// But we can set the query to empty which effectively clears the results
								setSearchQuery('');
							}}
							onSelectSearch={handleSelectSearch}
							onClearRecentLocations={handleClearRecentLocations}
							onRemoveRecentSearchLocation={handleRemoveRecentSearchLocation}
						/>
					</div>
				)}
			</div>
		</div>
	);
}
