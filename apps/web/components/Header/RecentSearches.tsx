'use client';

import React, { PropsWithChildren, useEffect, useRef, useState } from 'react';
import { useAtomValue } from 'jotai';
import { userLocationsAtom } from '@/user/atoms/preferences/location';
import type { Location } from '@/user/atoms/types';
import {
	DndContext,
	closestCenter,
	KeyboardSensor,
	PointerSensor,
	useSensor,
	useSensors,
	DragEndEvent,
} from '@dnd-kit/core';
import {
	arrayMove,
	SortableContext,
	sortableKeyboardCoordinates,
	useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { getCurrentObservations } from '@repo/dal/weather/current/observations';
import { ICON_CODE_MAP, Units } from '@repo/dal/weather/types';
import useSWR from 'swr';
import { MoreVertical } from 'lucide-react';
import { WxIcon } from '@repo/icons/WxIcon';
import { NoData } from '@repo/icons/Data';
import { cn } from '@repo/ui/lib/utils';
import { useUser } from '@/user/hooks/useUser';
import Text from '@repo/ui/components/Text/Text';
import { userUnitPreferenceAtom } from '@/user/atoms/preferences/units';
import { unitsSystemByName } from '@/constants/unitData';
import { getLocationPointByPlaceId } from '@repo/dal/locations/point';
import { useUpdatePreferences } from '@/user/hooks/useUpdatePreferences';

export interface RecentSearch {
	id: string;
	city: string;
	location: string;
	temperature?: number;
	iconCode?: number;
	geocode?: string;
	isPrimary?: boolean;
	presentationName?: string;
}

export interface FavoritedLocation {
	id: string;
	city: string;
	location: string;
	geocode?: string;
}

interface FavoriteButtonProps {
	onClick?: (e: React.MouseEvent) => void;
	isFavorite?: boolean;
}

function FavoriteButton({ isFavorite, onClick }: FavoriteButtonProps) {
	return (
		<div className="group/favorite relative flex items-center">
			<span className="absolute hidden -translate-x-full text-nowrap text-xs capitalize group-hover/favorite:inline-block">
				{isFavorite ? 'Remove Location' : 'Save Location'}
			</span>
			<button
				onClick={onClick}
				className="flex cursor-pointer items-center justify-center p-2"
			>
				<span className="h-6 w-6">
					{isFavorite ? (
						<svg
							name="favorite"
							className="h-full w-full fill-[#fdc107]"
							viewBox="0 0 20 19"
							xmlns="http://www.w3.org/2000/svg"
						>
							<title>Favorite</title>
							<path
								d="M10 15.27L16.18 19l-1.64-7.03L20 7.24l-7.19-.61L10 0 7.19 6.63 0 7.24l5.46 4.73L3.82 19z"
								fillRule="evenodd"
							></path>
						</svg>
					) : (
						<svg
							name="favorite-stroke"
							className="h-full w-full hover:fill-[#fdc107] group-hover:fill-white"
							viewBox="0 0 20 19"
							xmlns="http://www.w3.org/2000/svg"
						>
							<title>Favorite</title>
							<path
								d="M12.81 6.62L10 0 7.19 6.63 0 7.24l5.46 4.73L3.82 19 10 15.27 16.18 19l-1.63-7.03L20 7.24l-7.19-.62zm-6.57 9.05l1-4.28-3.32-2.88 4.38-.38L10 4.1l1.71 4.04 4.38.38-3.32 2.88 1 4.28L10 13.4l-3.76 2.27z"
								fillRule="evenodd"
							></path>
						</svg>
					)}
				</span>
			</button>
		</div>
	);
}

interface SectionHeaderProps {
	title: string;
	onClearAll?: () => void;
	testId?: string;
}

function SectionHeader({ title, onClearAll, testId }: SectionHeaderProps) {
	return (
		<div className="flex items-center justify-between p-4" data-testid={testId}>
			<h2 className="text-xl font-medium leading-none text-black">{title}</h2>
			{onClearAll && (
				<button
					onClick={onClearAll}
					className="cursor-pointer text-sm font-medium"
				>
					Clear All
				</button>
			)}
		</div>
	);
}

// Component to display weather information with SWR data fetching
function WeatherDisplay({ search }: { search: RecentSearch }) {
	const unit = useAtomValue(userUnitPreferenceAtom);
	const unitCode = unitsSystemByName(unit)?.code as Units;

	// Fetch weather data using SWR if geocode is available
	const { data: weatherData } = useSWR(
		search.geocode ? ['currentConditions', search.geocode, unitCode] : null,
		async ([, geocode, units]) => {
			if (!geocode) return null;

			return getCurrentObservations({
				geocode,
				units,
				language: 'en-US',
			});
		},
		{
			revalidateIfStale: true,
			revalidateOnFocus: true,
			dedupingInterval: 300000, // 5 minutes
		},
	);

	// Use weather data if available, otherwise use the search data
	const iconCode = weatherData?.iconCode ?? search.iconCode;
	const temperature = weatherData
		? Math.round(weatherData.temperature)
		: search.temperature;

	return (
		<div
			className="flex items-center gap-1"
			data-testid="location-search-weather"
		>
			<div className="flex items-center justify-center text-gray-400">
				{typeof iconCode === 'number' &&
				(ICON_CODE_MAP[iconCode] || 'na') !== 'na' ? (
					<WxIcon
						iconCode={iconCode}
						className="lightBG"
						aria-label={
							ICON_CODE_MAP[iconCode] || `Weather condition ${iconCode}`
						}
						iconTheme="lightBG"
						size="md"
					/>
				) : (
					<NoData className="lightBG" aria-label="not available" size="md" />
				)}
			</div>
			<div className="text-l text-black group-hover:text-white">
				{temperature}°
			</div>
		</div>
	);
}

interface WeatherItemProps {
	search: RecentSearch;
	onClick?: (search: RecentSearch) => void;
}

function WeatherItem({ search, onClick }: WeatherItemProps) {
	return (
		<div
			className="flex flex-1 cursor-pointer items-center gap-4 p-2"
			onClick={() => {
				onClick?.(search);
			}}
		>
			<WeatherDisplay search={search} />

			<div className="flex flex-1 flex-col">
				<div className="flex items-center gap-1 text-left">
					<h3 className="font-bold text-black group-hover:text-white">
						{search.city}
						{!!search.isPrimary && (
							<span
								className="ml-[5px] rounded-[20px] bg-[#ff7b0f] px-[7px] text-sm text-white"
								style={{
									textShadow: '0 1px 1px #00000080',
								}}
							>
								1
							</span>
						)}
					</h3>
				</div>
				<p className="text-gray-500 group-hover:text-white">
					{search.location}
				</p>
			</div>
		</div>
	);
}

interface SortableFavoriteItemProps {
	location: Location;
	onSelect: (search: RecentSearch) => void;
	onRemove: (id: string) => void;
	showMoreButton: boolean;
	openMore: boolean;
	onClickMore: (id: string) => void;
	onDismissMore: () => void;
}

// SortableFavoriteItem component that wraps FavoriteItem with drag and drop functionality
function SortableFavoriteItem({
	location,
	onSelect,
	onRemove,
	onDismissMore,
	openMore,
	showMoreButton,
	onClickMore,
}: SortableFavoriteItemProps) {
	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({ id: location.placeID || '' });

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
		opacity: isDragging ? 0.5 : 1,
		zIndex: isDragging ? 10 : 1,
	};

	return (
		<div
			ref={setNodeRef}
			style={style}
			className={`flex flex-1 items-center ${isDragging ? 'bg-gray-50 shadow-lg' : ''}`}
		>
			<div className="flex flex-1" {...attributes} {...listeners}>
				<FavoriteItem
					location={location}
					onSelect={onSelect}
					onRemove={onRemove}
					onDismissMore={onDismissMore}
					openMore={openMore}
					showMoreButton={showMoreButton}
					onClickMore={onClickMore}
				/>
			</div>
		</div>
	);
}

function ItemContainer({ children }: PropsWithChildren) {
	return (
		<div className="hover:bg-brand-400 group flex flex-1 cursor-pointer px-2 hover:text-white">
			{children}
		</div>
	);
}

interface FavoriteItemProps {
	location: Location;
	onSelect: (search: RecentSearch) => void;
	onRemove: (id: string) => void;
	showMoreButton?: boolean;
	openMore?: boolean;
	onClickMore?: (id: string) => void;
	onDismissMore: () => void;
}

// FavoriteItem component to handle individual favorite location
function FavoriteItem({
	location,
	onSelect,
	onRemove,
	onClickMore,
	onDismissMore,
	openMore,
	showMoreButton,
}: FavoriteItemProps) {
	// For locations with name already provided, we don't need to fetch
	const placeId = location.placeID || '';
	const coordinate = location.coordinate || '';
	const name = location.name || 'Unknown Location';

	// Create a location object for display
	const favorite = {
		id: placeId,
		city: name,
		location: name, // Use name as location display too
		geocode: coordinate,
		temperature: 0, // Will be populated by WeatherDisplay
	};

	const handleRemove = () => {
		onRemove(placeId);
	};

	return (
		<ItemContainer>
			<WeatherItem search={favorite} onClick={onSelect} />
			<FavoriteButton isFavorite onClick={handleRemove} />
			{showMoreButton && (
				<RecentItemMoreButton
					open={openMore}
					onClick={() => {
						onClickMore?.(favorite.id);
					}}
					onDismiss={onDismissMore}
					onDelete={handleRemove}
				/>
			)}
		</ItemContainer>
	);
}

interface SearchResultItemProps {
	item: RecentSearch;
	disabledFavorites?: boolean;
	onSelect?: (item: RecentSearch) => void;
	onToggleSave?: (item: RecentSearch) => void;
}

function SearchResultItem({
	item,
	disabledFavorites,
	onSelect,
	onToggleSave,
}: SearchResultItemProps) {
	const { presentationName } = item;

	return (
		<button
			onClick={() => {
				onSelect?.(item);
			}}
		>
			<ItemContainer>
				<div className="flex flex-1 items-center">
					<div className="flex flex-1 p-4 px-2">
						<Text
							variant="Body.S"
							className="text-left text-black group-hover:text-white"
						>
							{presentationName}
						</Text>
					</div>
					{!disabledFavorites && (
						<FavoriteButton
							onClick={(e) => {
								e.stopPropagation();
								onToggleSave?.(item);
							}}
						/>
					)}
				</div>
			</ItemContainer>
		</button>
	);
}

interface RecentItemMoreButtonProps {
	open?: boolean;
	onClick?: () => void;
	onDismiss: () => void;
	onDelete?: () => void;
}

function RecentItemMoreButton({
	open,
	onClick,
	onDismiss,
	onDelete,
}: RecentItemMoreButtonProps) {
	const ref = useRef<HTMLButtonElement | null>(null);

	useEffect(() => {
		let listener: (e: MouseEvent) => void;

		if (open) {
			listener = (e: MouseEvent) => {
				if (ref.current?.contains(e.target as Node)) {
					e.stopPropagation();
				} else {
					onDismiss?.();
				}
			};

			document.addEventListener('click', listener);
		}

		return () => {
			if (listener) {
				document.removeEventListener('click', listener);
			}
		};
	}, [open, onDismiss]);

	return (
		<div className="relative flex items-center">
			<button
				className="flex cursor-pointer items-center justify-center p-2"
				onClick={(e) => {
					e.stopPropagation();
					onClick?.();
				}}
			>
				<MoreVertical />
			</button>
			<button
				ref={ref}
				className={cn(
					'bg-brand-400 invisible absolute -right-full -top-full !m-0 h-10 cursor-pointer text-nowrap rounded-lg border border-[#1b4de4] px-[14px] py-[6px] text-sm font-medium text-white opacity-0 transition-[right,opacity] duration-300 ease-out hover:bg-white hover:text-black',
					open && 'visible right-[10px] top-1/2 -translate-y-1/2 opacity-100',
				)}
				onClick={(e) => {
					e.stopPropagation();
					onDelete?.();
					onDismiss();
				}}
			>
				Delete
			</button>
		</div>
	);
}

interface RecentLocationItemProps {
	id: string;
	disabledFavorites?: boolean;
	showMoreButton: boolean;
	onSelect: (search: RecentSearch) => void;
	onSaveToFavorites: (location: RecentSearch) => void;
	onClickMore?: (id: string) => void;
	onDismissMore: () => void;
	openMore?: boolean;
	onDelete?: (item: RecentSearch) => void;
}

// RecentLocationItem component to handle individual recent location
function RecentLocationItem({
	id,
	onSelect,
	showMoreButton,
	onSaveToFavorites,
	onClickMore,
	onDismissMore,
	openMore,
	onDelete,
	disabledFavorites,
}: RecentLocationItemProps) {
	const locationFetcher = (placeId: string) => {
		return getLocationPointByPlaceId(placeId);
	};

	const { data } = useSWR(
		// Only fetch if we have a favorite and no direct location prop
		id ? ['location-data', id] : null,
		() => locationFetcher(id),
		{
			revalidateOnFocus: false,
			dedupingInterval: 300000, // 5 minutes
		},
	);

	let minimal;
	if (data) {
		const { location } = data;
		minimal = {
			id: location.placeId,
			city: location.city || location.displayName || 'Unknown',
			location: location.adminDistrict
				? `${location.adminDistrict}${location.country ? ', ' + location.country : ''}`
				: location.country || '',
			geocode: `${location.latitude},${location.longitude}`,
		};
	} else {
		return null;
	}

	return (
		<ItemContainer>
			<WeatherItem search={minimal} onClick={onSelect} />
			{!disabledFavorites && (
				<>
					<FavoriteButton onClick={() => onSaveToFavorites(minimal)} />
					{showMoreButton && (
						<RecentItemMoreButton
							open={openMore}
							onClick={() => {
								onClickMore?.(minimal.id);
							}}
							onDismiss={onDismissMore}
							onDelete={() => {
								onDelete?.(minimal);
							}}
						/>
					)}
				</>
			)}
		</ItemContainer>
	);
}

interface RecentSearchesProps {
	searches: RecentSearch[];
	onClearAll: () => void;
	onSelectSearch: (search: RecentSearch) => void;
	showFavorites?: boolean; // New prop to control visibility of favorites
	recentLocations?: string[]; // New prop for recently visited locations
	onClearRecentLocations?: () => void; // New prop for clearing recent locations
	onRemoveRecentSearchLocation?: (item: RecentSearch) => void;
}

export default function RecentSearches({
	searches,
	onSelectSearch,
	showFavorites = true, // Default to true for backward compatibility
	recentLocations = [], // Default to empty array
	onClearRecentLocations, // Add this prop
	onRemoveRecentSearchLocation,
}: RecentSearchesProps) {
	const { user } = useUser();
	const { isUserLoggedIn } = user;
	const updatePreferences = useUpdatePreferences();

	const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
	// Use userLocationsAtom for favorites
	const userLocations = useAtomValue(userLocationsAtom);

	// Favorites are always from userLocationsAtom, ensure it's an array
	const favoritedLocationsMinimal = userLocations || [];

	// Set up sensors for drag and drop
	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 8, // Minimum distance before a drag starts
			},
		}),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		}),
	);

	// Handle drag end event to reorder favorites
	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;

		if (over && active.id !== over.id) {
			const oldIndex = favoritedLocationsMinimal.findIndex(
				(item: Location) => item.placeID === active.id,
			);
			const newIndex = favoritedLocationsMinimal.findIndex(
				(item: Location) => item.placeID === over.id,
			);

			const newOrder = arrayMove(
				[...favoritedLocationsMinimal],
				oldIndex,
				newIndex,
			);

			// Update userLocationsAtom directly
			updatePreferences({
				locations: newOrder,
			});
		}
	};

	const handleSaveLocation = (search: RecentSearch) => {
		console.debug('Current favorites:', userLocations);

		// Check if location is already favorited
		const isAlreadyFavorited = favoritedLocationsMinimal.some(
			(favorite: Location) => favorite.placeID === search.id,
		);

		if (!isAlreadyFavorited && search.geocode) {
			console.debug('Adding to favorites:', search.city);
			const newFavorite: Location = {
				placeID: search.id, // Use the original ID (placeId) for navigation
				coordinate: search.geocode, // Save geocode for weather data fetching
				name: search.city, // Use the city name as the location name
				position: favoritedLocationsMinimal.length + 1, // Add at the end of the list
			};

			// Add the new favorite to the list
			const updated = [...favoritedLocationsMinimal, newFavorite];

			// Update userLocationsAtom directly
			updatePreferences({
				locations: updated,
			});
		} else {
			console.debug(
				'Location already favorited or missing geocode:',
				search.city,
			);
		}
	};

	const handleRemoveFavorite = (favoriteId: string) => {
		console.debug('Remove favorite clicked for ID:', favoriteId);
		console.debug('Current favorites:', userLocations);

		// Remove the favorite from the list
		const updated = favoritedLocationsMinimal.filter(
			(favorite: Location) => favorite.placeID !== favoriteId,
		);
		console.debug('Updated favorites:', updated);

		// Update userLocationsAtom directly
		updatePreferences({
			locations: updated,
		});
	};

	return (
		<div
			data-testid="location-search-results-dropdown"
			className="overflow-x-hidden"
		>
			{/* Favorited Locations Section - Only show if showFavorites is true */}
			{showFavorites && favoritedLocationsMinimal.length > 0 && (
				<>
					<SectionHeader
						title="Favorites"
						testId="location-search-favorites-header"
					/>
					<DndContext
						sensors={sensors}
						collisionDetection={closestCenter}
						onDragEnd={handleDragEnd}
					>
						<SortableContext
							items={favoritedLocationsMinimal.map(
								(item) => item.placeID || '',
							)}
						>
							<div
								className="divide-y"
								data-testid="location-search-favorites-list"
							>
								{favoritedLocationsMinimal.map((location) => (
									<SortableFavoriteItem
										key={location.placeID}
										location={location}
										onSelect={onSelectSearch}
										onRemove={handleRemoveFavorite}
										openMore={
											`favorites-${location.placeID}` === selectedItemId
										}
										onClickMore={(id) => {
											setSelectedItemId(`favorites-${id}`);
										}}
										onDismissMore={() => setSelectedItemId(null)}
										showMoreButton
									/>
								))}
							</div>
						</SortableContext>
					</DndContext>
				</>
			)}

			{/* Recent Locations Section - Only show if there are recent locations */}
			{!searches.length && recentLocations.length > 0 && (
				<>
					<SectionHeader
						title="Recents"
						onClearAll={onClearRecentLocations}
						testId="location-search-recent-header"
					/>
					<div className="divide-y" data-testid="location-search-recent-list">
						{recentLocations.map((locationID) => (
							<RecentLocationItem
								key={locationID}
								id={locationID}
								disabledFavorites={!isUserLoggedIn}
								showMoreButton={recentLocations.length > 1}
								onSelect={onSelectSearch}
								onSaveToFavorites={handleSaveLocation}
								openMore={`recents-${locationID}` === selectedItemId}
								onClickMore={(id) => {
									setSelectedItemId(`recents-${id}`);
								}}
								onDismissMore={() => setSelectedItemId(null)}
								onDelete={onRemoveRecentSearchLocation}
							/>
						))}
					</div>
				</>
			)}

			{/* Show "no recent locations" message only if everything is empty */}
			{(!showFavorites || favoritedLocationsMinimal.length === 0) &&
				recentLocations.length === 0 &&
				searches.length === 0 && (
					<div
						className="p-6 text-center text-gray-500"
						data-testid="location-search-empty-message"
					>
						You have no recent locations
					</div>
				)}

			{/* Search Results Section - Only show if there are searches */}
			{searches.length > 0 && (
				<div
					className="flex flex-1 flex-col divide-y"
					data-testid="location-search-results-list"
				>
					{searches.map((search) => (
						<SearchResultItem
							disabledFavorites={!isUserLoggedIn}
							item={search}
							key={search.id}
							onSelect={onSelectSearch}
							onToggleSave={handleSaveLocation}
						/>
					))}
				</div>
			)}
		</div>
	);
}
