'use client';

import Text from '@repo/ui/components/Text/Text';
import { Phone } from '@repo/icons/components/Deprecated';
import { useUser } from '@/user/hooks/useUser';
import Link from '../Link';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@repo/ui/components/Popover/Popover';
import Image from '@repo/ui/components/Image/Image';

function DownloadButtonDropDownContent() {
	return (
		<div className="flex flex-col gap-4 py-4 pb-0">
			<Text variant="Title.S" className="text-center font-bold">
				Your weather, wherever you are.
			</Text>
			<Text className="text-center">
				The forecast you love, plus real time notifications, widgets, activites
				forecast, and more.
			</Text>
			<div className="flex items-center justify-center gap-2">
				<div className="h-auto w-full">
					<Image
						src="https://s.w-x.co/twc-app-overview.png"
						width={320}
						sizes="180px"
						alt="app view"
					/>
				</div>
				<div className="flex w-full flex-col items-center justify-center gap-1">
					<Image
						src="https://s.w-x.co/twc-app-qr-code.png"
						width={320}
						sizes="180px"
						alt="qr code"
					/>
					<Text variant="Body.S">Scan to download</Text>
				</div>
			</div>
		</div>
	);
}

export default function ActionButtons() {
	const { user } = useUser();

	const { isUserPremium } = user;

	return (
		<div className="flex items-center gap-4">
			{!isUserPremium && (
				<Link href="/subscribe?tpcc=mktg-home-mainmenu-subscribe">
					<button className="flex h-6 cursor-pointer items-center justify-center text-nowrap rounded-lg border-white bg-white p-2 text-[0.625rem] font-semibold uppercase text-[#252422]">
						Go Premium
					</button>
				</Link>
			)}
			<Popover trigger="hover" openDelay={0}>
				<PopoverTrigger asChild>
					<button className="flex h-full max-h-6 cursor-pointer items-center justify-center gap-1 text-nowrap rounded-lg border-white bg-white p-2 text-[0.625rem] font-semibold uppercase text-[#252422]">
						<Phone className="h-3.5 w-auto" />
						Download App
					</button>
				</PopoverTrigger>
				<PopoverContent className="w-md">
					<DownloadButtonDropDownContent />
				</PopoverContent>
			</Popover>
		</div>
	);
}
