import Link from '@repo/ui/components/Link/Link';
import { TWC } from '@repo/icons/Deprecated';
import Search from '@/components/Header/Search';
import MenuButton from '@/components/Header/MenuButton';
import UserProfileMenu from '@/components/Header/UserProfileMenu';
import SettingsMenu from '@/components/Header/SettingsMenu';
import ActionButtons from './ActionButtons';

export function Header() {
	return (
		<header className="sticky top-0 z-50 w-full">
			{/* Main header */}
			<div className="bg-brand-400 text-white" data-testid="header-main">
				<div className="mx-auto flex max-w-7xl items-center justify-between gap-2 p-[11px] sm:gap-8">
					{/* Logo */}
					<Link
						href="/"
						className="flex items-center"
						data-testid="header-logo"
					>
						<TWC
							className="h-[36.88px] w-[36.88px] md:h-[60px] md:w-[60px]"
							color="inverse"
						/>
					</Link>

					<div className="flex flex-1 items-center justify-between gap-2 sm:gap-8">
						{/* Search component */}
						<Search className="m-auto" />

						<div
							className="flex items-center gap-2 sm:gap-8"
							data-testid="header-actions"
						>
							<div className="flex items-center gap-4">
								<div className="hidden md:block">
									<SettingsMenu />
								</div>
								<div className="hidden md:block">
									<ActionButtons />
								</div>
								<UserProfileMenu />
							</div>

							{/* Menu button component (client) */}
							<MenuButton />
						</div>
					</div>
				</div>
			</div>
		</header>
	);
}

export default Header;
