'use client';

import React, { forwardRef, useState, useEffect } from 'react';
import { Minus } from '@repo/icons/Operations';
import { Plus } from '@repo/icons/Operations';
import Text from '@repo/ui/components/Text/Text';
import { Button } from '@repo/ui/components/Button/Button';
import { UNIT_IMPERIAL, UNIT_METRIC, UNIT_HYBRID } from '@/constants/unitData';
import { cn } from '@repo/ui/lib/utils';

interface CountryType {
	name: string;
	locale: string;
	language: string;
}

interface RegionType {
	title: string;
	countries: CountryType[];
}

interface SettingsMenuContentProps {
	isImperial: boolean;
	isMetric: boolean;
	isHybrid: boolean;
	locale: string;
	expandedRegions: Record<string, boolean>;
	LOCALE_DATA: RegionType[];
	handleUnitChange: (unit: string) => void;
	toggleRegion: (region: string) => void;
	handleLocaleChange: (locale: string) => void;
	inNavigation?: boolean;
}

const SettingsMenuContent = forwardRef<
	HTMLDivElement,
	SettingsMenuContentProps
>(
	(
		{
			isImperial,
			isMetric,
			isHybrid,
			expandedRegions,
			LOCALE_DATA,
			handleUnitChange,
			toggleRegion,
			handleLocaleChange,
			inNavigation = false,
		},
		ref,
	) => {
		// State to track the number of columns based on screen size
		const [columns, setColumns] = useState(1);

		// Update columns based on window size
		useEffect(() => {
			// Default to 1 column
			let cols = 1;

			// Function to update columns based on window width
			const updateColumns = () => {
				if (window.innerWidth >= 1024) {
					cols = 3; // lg screens
				} else if (window.innerWidth >= 768) {
					cols = 2; // md screens
				} else {
					cols = 1; // sm screens
				}
				setColumns(cols);
			};

			// Set initial value
			updateColumns();

			// Add event listener for window resize
			window.addEventListener('resize', updateColumns);

			// Clean up event listener
			return () => window.removeEventListener('resize', updateColumns);
		}, []);

		// Helper function to calculate rows needed for each region
		const calculateRows = (countryCount: number) => {
			return Math.ceil(countryCount / columns);
		};

		const BUTTONS = [
			{
				label: 'Imperial',
				unitName: UNIT_IMPERIAL,
				active: isImperial,
				symbol: '°F',
			},
			{
				label: 'Metric',
				unitName: UNIT_METRIC,
				active: isMetric,
				symbol: '°C',
			},
			{
				label: 'Hybrid',
				unitName: UNIT_HYBRID,
				active: isHybrid,
				symbol: 'Hybrid',
			},
		];

		return (
			<div
				ref={ref}
				className={cn(
					'overflow-auto bg-white',
					inNavigation
						? 'relative' // When in navigation menu - full width
						: 'absolute left-0 right-0 top-full z-40 max-h-[calc(100vh-60px)] w-full shadow-md md:max-h-[calc(100vh-82px)]', // Default positioning
				)}
			>
				{/* Container to center content */}
				<div
					className={cn('mx-auto max-w-screen-xl', {
						'px-[15px] py-[10px]': !inNavigation,
					})}
				>
					{/* Units tab-like interface - centered with proper spacing */}
					<div className="flex justify-center border-b pt-4">
						{BUTTONS.map(({ unitName, active, symbol }) => (
							<Button
								key={unitName}
								onClick={() => handleUnitChange(unitName)}
								variant="ghost"
								className={cn(
									'text-brand-400 cursor-pointer rounded-none border-b-2 border-transparent',
									active && 'border-brand-400 border-black',
								)}
							>
								{symbol}
							</Button>
						))}
					</div>

					{/* Description of selected unit system - centered */}
					<div className="py-3 text-center text-xs text-[#6f7585]">
						{isImperial && 'Imperial - F / mph / miles / inches'}
						{isMetric && 'Metric - C / millimeters / km / kmh / millibars'}
						{isHybrid && 'Hybrid - C / millimeters / miles / mph / millibars'}
					</div>

					{/* Language regions accordion */}
					<div className="">
						{LOCALE_DATA.map((region) => (
							<div key={region.title} className="border-b last:border-b-0">
								{/* Region header */}
								<Button
									onClick={() => toggleRegion(region.title)}
									variant="ghost"
									className="flex w-full items-center justify-between rounded-none p-6"
								>
									<Text className="text-lg font-bold uppercase">
										{region.title}
									</Text>
									{expandedRegions[region.title] ? (
										<Minus className="h-[18px] w-[18px] text-black" />
									) : (
										<Plus className="h-[18px] w-[18px] text-black" />
									)}
								</Button>

								{/* Countries list - column-first grid */}
								{expandedRegions[region.title] && (
									<div
										className="grid grid-cols-1 bg-gray-50 px-[21px] md:grid-cols-2 lg:grid-cols-3"
										style={{
											gridAutoFlow: 'column',
											gridTemplateRows: `repeat(${calculateRows(region.countries.length)}, auto)`,
										}}
									>
										{region.countries.map((country) => {
											return (
												<Button
													key={country.locale}
													variant="link"
													onClick={() => handleLocaleChange(country.locale)}
													className={`flex items-center justify-between`}
												>
													{/* TODO: Text? No Text? inside Button? */}
													{/* <Text elementType='span' variant='Body.M'> */}
													{country.name} | {country.language}
													{/* </Text> */}
												</Button>
											);
										})}
									</div>
								)}
							</div>
						))}
					</div>
				</div>
			</div>
		);
	},
);

SettingsMenuContent.displayName = 'SettingsMenuContent';

export default SettingsMenuContent;
