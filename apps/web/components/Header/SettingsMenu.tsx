'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useAtomValue } from 'jotai';
import { Globe, TriangleUp, TriangleDown } from '@repo/icons/Deprecated';
import SettingsMenuContent from '@/components/Header/SettingsMenuContent';
import { Button } from '@repo/ui/components/Button/Button';
import Text from '@repo/ui/components/Text/Text';
import { cn } from '@repo/ui/lib/utils';
import {
	isImperialUnitsAtom,
	isMetricUnitsAtom,
	isHybridUnitsAtom,
} from '@/user/atoms/preferences/units';
import {
	userLocaleAtom,
	userCountryAtom,
} from '@/user/atoms/preferences/locale';
import { LOCALE_DATA, convertLocaleFormat } from '@/constants/localeData';
import { getCookie, setCookie } from 'cookies-next';
import { userPreferencesAtom } from '@/user/atoms';
import { UnitsCode, unitsSystemByCode } from '@/constants/unitData';
import { useUpdatePreferences } from '@/user/hooks/useUpdatePreferences';

/**
 * SettingsMenuClient Component
 *
 * This component allows users to change their unit preference (imperial, metric, or hybrid)
 * and language preference in a combined dropdown menu.
 * It works for both logged-in and non-logged-in users, saving preferences to localStorage.
 * For logged-in users, preferences are also synced with the server.
 *
 * @param {Object} props - Component props
 * @param {boolean} [props.inNavigation=false] - Whether the component is being used in the navigation menu
 * @param {Function} [props.onDropdownToggle] - Callback function called when dropdown is toggled
 */
interface SettingsMenuProps {
	inNavigation?: boolean;
	onDropdownToggle?: (isOpen: boolean) => void;
}

export default function SettingsMenu({
	inNavigation = false,
	onDropdownToggle,
}: SettingsMenuProps) {
	// State for controlling dropdown visibility
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);

	const toggleDropdown = useCallback(
		(state?: boolean) => {
			const newState = state ?? !isDropdownOpen;
			setIsDropdownOpen(newState);
			if (onDropdownToggle) {
				onDropdownToggle(newState);
			}
		},
		[isDropdownOpen, onDropdownToggle],
	);

	// Refs for click outside detection
	const buttonRef = useRef<HTMLButtonElement>(null);
	const menuRef = useRef<HTMLDivElement>(null);

	// State for tracking which regions are expanded in the language dropdown
	const [expandedRegions, setExpandedRegions] = useState<
		Record<string, boolean>
	>({});

	// Get current unit preference from localStorage via Jotai atoms
	const isImperial = useAtomValue(isImperialUnitsAtom);
	const isMetric = useAtomValue(isMetricUnitsAtom);
	const isHybrid = useAtomValue(isHybridUnitsAtom);

	// Get current locale and country preference from localStorage via Jotai atoms
	const locale = useAtomValue(userLocaleAtom);
	const country = useAtomValue(userCountryAtom);

	// Get the update functions from the custom hooks
	const updatePreferences = useUpdatePreferences();

	// Determine which unit symbol to display imperial=F metrics=C hybrid=C
	const unitSymbol = isImperial ? '°F' : '°C';

	const userPreferences = useAtomValue(userPreferencesAtom);

	useEffect(() => {
		//sync wxu-web storage
		const unitsCookieCode = getCookie('unitOfMeasurement')?.toString();
		console.log('UNITS_COOKIE', unitsCookieCode);

		if (unitsCookieCode) {
			const unitsSystem = unitsSystemByCode(unitsCookieCode as UnitsCode);
			const unitsSystemName = unitsSystem?.name;

			const userPreferencesUnit = userPreferences?.unit;

			if (unitsSystemName && unitsSystemName !== userPreferencesUnit) {
				updatePreferences({ unit: unitsSystemName });
			}
		}
	}, [updatePreferences, userPreferences?.unit]);

	// Handle unit change - works for all users (logged in or not)
	// Changes are saved to localStorage and synced with server if user is logged in
	const handleUnitChange = async (unitName: string) => {
		try {
			await updatePreferences({ unit: unitName });
		} catch (error) {
			console.error('Failed to update unit preference:', error);
		}
	};

	// Handle locale change - works for all users (logged in or not)
	// Changes are saved to localStorage and synced with server if user is logged in
	// Also sets a sitePref cookie with the locale value
	const handleLocaleChange = async (locale: string) => {
		try {
			// Convert from 'en-US' format to 'en_US' format for storage
			const formattedLocale = convertLocaleFormat(locale);

			// Update the locale preference with underscore format
			await updatePreferences({ locale: formattedLocale });

			// Set the sitePref cookie with the original hyphen format
			// No expiration date = session cookie (deleted when browser closes)
			setCookie('sitePref', locale, {
				domain: `.${window.location.hostname}`,
			});

			toggleDropdown(false);

			window.location.href = '/';
		} catch (error) {
			console.error('Failed to update locale preference:', error);
		}
	};

	// Toggle region expansion in language dropdown
	// Only one region can be expanded at a time
	const toggleRegion = (region: string) => {
		setExpandedRegions((prev) => {
			// If this region is already expanded, just collapse it
			if (prev[region]) {
				return {
					...prev,
					[region]: false,
				};
			}

			// Otherwise, collapse all regions and expand only this one
			const newState: Record<string, boolean> = {};
			Object.keys(prev).forEach((key) => {
				newState[key] = false;
			});

			return {
				...newState,
				[region]: true,
			};
		});
	};

	// Close dropdown when clicking outside
	useEffect(() => {
		function handleClickOutside(event: MouseEvent) {
			if (
				isDropdownOpen &&
				buttonRef.current &&
				menuRef.current &&
				!buttonRef.current.contains(event.target as Node) &&
				!menuRef.current.contains(event.target as Node)
			) {
				toggleDropdown(false);
			}
		}

		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, [toggleDropdown, isDropdownOpen]);

	return (
		<>
			<Button
				ref={buttonRef}
				variant="ghostUI"
				className={cn('flex cursor-pointer items-center px-2 py-1', {
					'px-0 pt-2': inNavigation,
				})}
				onClick={() => {
					toggleDropdown();
				}}
			>
				<Globe
					size="md"
					className={inNavigation ? 'fill-gray-900' : 'fill-white'}
				/>
				<Text
					elementType="span"
					color={inNavigation ? 'default' : 'inverse'}
					variant="Body.M"
					className="px-[10px]"
				>
					{country}
				</Text>
				<Text
					elementType="span"
					color={inNavigation ? 'default' : 'inverse'}
					variant="Body.M"
					className={cn(
						'border-l-[1px] px-[10px] font-mono',
						inNavigation ? 'border-l-gray-300' : 'border-l-white',
					)}
				>
					{unitSymbol}
				</Text>
				<div className="ml-[5px]">
					{isDropdownOpen ? (
						<TriangleUp
							size="sm"
							className={inNavigation ? 'fill-gray-900' : 'fill-white'}
						/>
					) : (
						<TriangleDown
							size="sm"
							className={inNavigation ? 'fill-gray-900' : 'fill-white'}
						/>
					)}
				</div>
			</Button>

			{/* Settings Menu Content */}
			{isDropdownOpen && (
				<SettingsMenuContent
					ref={menuRef}
					isImperial={isImperial}
					isMetric={isMetric}
					isHybrid={isHybrid}
					locale={locale}
					expandedRegions={expandedRegions}
					LOCALE_DATA={LOCALE_DATA}
					handleUnitChange={handleUnitChange}
					toggleRegion={toggleRegion}
					handleLocaleChange={handleLocaleChange}
					inNavigation={inNavigation}
				/>
			)}
		</>
	);
}
