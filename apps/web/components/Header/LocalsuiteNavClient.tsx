'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from '@repo/ui/components/Link/Link';
import { ChevronDown, ExternalLink } from 'lucide-react';
import { Button } from '@repo/ui/components/Button/Button';

// Define the type for nav items
type NavItem = {
	name: string;
	href: string;
	isNew?: boolean;
	hasDropdown?: boolean;
	isButton?: boolean;
	icon?: React.ReactNode;
};

interface LocalsuiteNavClientProps {
	navItems: NavItem[];
}

export default function LocalsuiteNavClient({
	navItems,
}: LocalsuiteNavClientProps) {
	const [isMoreForecastsOpen, setIsMoreForecastsOpen] = useState(false);
	const dropdownRef = useRef<HTMLDivElement>(null);
	const moreItemRef = useRef<HTMLDivElement>(null);

	const toggleMoreForecasts = () => {
		setIsMoreForecastsOpen(!isMoreForecastsOpen);
	};

	// <PERSON>le click outside to close dropdown
	useEffect(() => {
		function handleClickOutside(event: MouseEvent) {
			if (
				dropdownRef.current &&
				moreItemRef.current &&
				!dropdownRef.current.contains(event.target as Node) &&
				!moreItemRef.current.contains(event.target as Node)
			) {
				setIsMoreForecastsOpen(false);
			}
		}

		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, []);

	return (
		<div
			className="relative w-full border-b border-gray-200 bg-[#f7f3f0]"
			data-testid="forecast-menu"
		>
			<div className="mx-auto flex max-w-7xl items-center px-4">
				<div className="flex w-full items-center justify-center space-x-4">
					{navItems.map((item) =>
						item.isButton ? (
							<Button
								key={item.name}
								variant="ghost"
								className="flex items-center gap-1 px-4 py-2 font-medium text-gray-800 hover:text-blue-600"
							>
								<span className="flex items-center">
									{item.icon}
									{item.name}
								</span>
								<ChevronDown className="h-4 w-4" />
							</Button>
						) : (
							<div key={item.name} className="flex items-center px-4 py-2">
								{item.href && !item.hasDropdown ? (
									<Link
										href={item.href}
										className="flex items-center font-medium text-gray-800 hover:text-blue-600"
									>
										{item.name}
										{item.isNew && (
											<span className="ml-2 rounded bg-black px-1.5 py-0.5 text-xs text-white">
												NEW
											</span>
										)}
									</Link>
								) : item.hasDropdown ? (
									<div
										ref={moreItemRef}
										className="relative flex cursor-pointer items-center"
										onClick={toggleMoreForecasts}
									>
										<span className="font-medium text-gray-800 hover:text-blue-600">
											{item.name}
										</span>
										<ChevronDown className="ml-1 h-4 w-4" />

										{/* More Forecasts Dropdown */}
										{isMoreForecastsOpen && (
											<div
												ref={dropdownRef}
												className="absolute z-50 w-64 rounded-md border border-gray-200 bg-white p-4 shadow-lg"
												style={{
													top: '100%',
													left: '50%',
													transform: 'translateX(-50%)',
													marginTop: '0.5rem',
												}}
											>
												<h3 className="mb-4 text-lg font-bold">
													SPECIALTY FORECASTS
												</h3>
												<div className="grid grid-cols-2 gap-4">
													<Link
														href="/yesterday"
														className="flex items-center text-lg font-medium text-gray-800 hover:text-blue-600"
														data-testid="forecast-menu-yesterday"
													>
														Yesterday&lsquo;s Weather{' '}
														<ExternalLink className="ml-1 h-4 w-4 text-blue-600" />
													</Link>
													<Link
														href="/allergy"
														className="text-lg font-medium text-gray-800 hover:text-blue-600"
														data-testid="forecast-menu-allergy"
													>
														Allergy Tracker
													</Link>
													<Link
														href="/cold-flu"
														className="text-lg font-medium text-gray-800 hover:text-blue-600"
														data-testid="forecast-menu-cold-flu"
													>
														Cold & Flu
													</Link>
													<Link
														href="/air-quality"
														className="text-lg font-medium text-gray-800 hover:text-blue-600"
														data-testid="forecast-menu-air-quality"
													>
														Air Quality Forecast
													</Link>
												</div>
											</div>
										)}
									</div>
								) : (
									<div className="flex items-center">
										<span className="font-medium text-gray-800">
											{item.name}
										</span>
									</div>
								)}
							</div>
						),
					)}
				</div>
			</div>
		</div>
	);
}
