'use client';

import Link from '@/components/Link';
import { PropsWithChildren, useState } from 'react';
import { Button } from '@repo/ui/components/Button/Button';
import { UseUser, useUser } from '@/user/hooks/useUser';
import { UserAvatar } from '@/components/UserAvatar';
import Text from '@repo/ui/components/Text/Text';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@repo/ui/components/Popover/Popover';
import { useAtomValue } from 'jotai';
import { userHasLoggedInBeforeAtom } from '@/user/atoms/wxu/user';
import { cn } from '@repo/ui/lib/utils';

const LinkButton = ({
	href,
	children,
	className,
}: PropsWithChildren & { href: string; className?: string }) => {
	return (
		<Link
			href={href}
			className={cn(
				'flex h-6 cursor-pointer items-center justify-center gap-0.5 text-nowrap rounded-lg p-2 text-[0.625rem] font-semibold uppercase sm:border sm:border-white',
				className,
			)}
		>
			{children}
		</Link>
	);
};

function AnonymousDropDownButton() {
	const userHasLoggedInBefore = useAtomValue(userHasLoggedInBeforeAtom);
	const [open, setOpen] = useState(false);

	if (userHasLoggedInBefore) {
		return (
			<div>
				<LinkButton href="/login" className="hidden sm:flex">
					<UserAvatar isLoggedIn={false} stroke={false} size="sm" />
					SIGN IN
				</LinkButton>
				<Button size="xsm" className="flex items-center sm:hidden">
					<UserAvatar isLoggedIn={false} />
				</Button>
			</div>
		);
	}

	return (
		<Popover
			trigger="hover"
			openDelay={0}
			closeDelay={300}
			open={open}
			onOpenChange={setOpen}
		>
			<PopoverTrigger asChild>
				<button className="flex items-center">
					<div
						className={cn(
							'flex h-6 cursor-pointer items-center justify-center gap-0.5 text-nowrap rounded-lg p-2 text-[0.625rem] font-semibold uppercase md:border md:border-white',
							'hidden md:flex',
						)}
					>
						<UserAvatar isLoggedIn={false} stroke={false} size="sm" />
						SIGN UP
					</div>
					<div className="flex items-center justify-center md:hidden">
						<UserAvatar isLoggedIn={false} />
					</div>
				</button>
			</PopoverTrigger>
			<PopoverContent align="end" alignOffset={-8} className="px-0 pb-0">
				<div className="flex flex-col">
					<div className="flex flex-col gap-4 border-b p-4">
						<Text variant="Title.S" className="font-bold">
							Create account
						</Text>
						<Text>
							Save your favourite locations and forecast preferences, and set up
							your own weather dashboard.
						</Text>
						<div className="py-1">
							<Link href="/signup">
								<Button className="bg-[#252422] px-4 py-2.5 text-sm">
									Create Account
								</Button>
							</Link>
						</div>
						<Text variant="Body.S">
							Already have an account? <Link href="/login">Sign In</Link>
						</Text>
					</div>
					<div className="p-4">
						<Link href="/subscribe?tpcc=mktg-today-mainmenu-flyout">
							Try Premium For Free
						</Link>
					</div>
				</div>
			</PopoverContent>
		</Popover>
	);
}

function LoggedInUserDropDownButton({ user, userProfile, logout }: UseUser) {
	const [open, setOpen] = useState(false);

	const { isUserLoggedIn, isUserPremium, subscriptionTier } = user;

	const handleLogout = async () => {
		try {
			// Call the logout function which will handle router.refresh()
			await logout();
		} catch (error) {
			console.error('Logout failed:', error);
		}
	};

	return (
		<Popover
			trigger="hover"
			openDelay={100}
			closeDelay={300}
			open={open}
			onOpenChange={setOpen}
		>
			<PopoverTrigger asChild>
				<button
					className="flex cursor-pointer items-center rounded-md border-white bg-transparent hover:border-white hover:bg-transparent hover:text-white"
					data-testid="header-profile-button"
				>
					<UserAvatar
						firstName={userProfile?.firstName}
						isLoggedIn={isUserLoggedIn}
						isPremium={isUserPremium}
						subscriptionTier={subscriptionTier}
					/>
				</button>
			</PopoverTrigger>
			<PopoverContent className="p-0">
				<div className="flex flex-col">
					<div>
						<Link href="/member/settings" data-testid="header-profile-account">
							<div className="flex items-center gap-3 p-4">
								<UserAvatar
									firstName={userProfile?.firstName}
									isLoggedIn={isUserLoggedIn}
									isPremium={isUserPremium}
									subscriptionTier={subscriptionTier}
									stroke={false}
									size="lg"
									variant="brand"
								/>
								<div className="flex flex-col gap-1">
									<Text variant="Body.S">{userProfile?.firstName}</Text>
									<Text
										className="text-sm text-gray-500 hover:text-gray-700"
										data-testid="header-profile-account"
									>
										Manage Account
									</Text>
								</div>
							</div>
						</Link>

						{!isUserPremium && (
							<div className="flex flex-col gap-3 border-t border-gray-200 p-4">
								<Text variant="Body.L" className="font-semibold">
									Unlock our full potential
								</Text>
								<Text>
									Try our best radar and forecasting features for weather
									planning.
								</Text>
								<Link href="/subscribe?tpcc=mktg-today-mainmenu-flyout">
									<Button variant="default">Go Premium</Button>
								</Link>
							</div>
						)}
					</div>

					{/* Sign out option */}
					<div className="border-t border-gray-200 p-2">
						<Button
							variant="link"
							onClick={handleLogout}
							data-testid="header-profile-signout"
						>
							Sign Out
						</Button>
					</div>
				</div>
			</PopoverContent>
		</Popover>
	);
}

export default function UserProfileMenu() {
	const { user, userProfile, logout } = useUser();

	const { isUserLoggedIn } = user;

	return (
		<div data-testid="header-profile">
			{isUserLoggedIn ? (
				<LoggedInUserDropDownButton
					user={user}
					userProfile={userProfile}
					logout={logout}
				/>
			) : (
				<AnonymousDropDownButton />
			)}
		</div>
	);
}
