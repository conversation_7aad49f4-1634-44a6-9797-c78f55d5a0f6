'use client';

import React from 'react';
import Link from '@repo/ui/components/Link/Link';
import {
	useLocationSource,
	LocationFallbackOrder,
} from '@/location/hooks/useLocationSource';
import type { LocationData } from '@/location/types';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@repo/ui/components/Popover/Popover';
import { TriangleUp } from '@repo/icons/Navigation';
import { TriangleDown } from '@repo/icons/Navigation';
import { ExternalLink } from '@repo/icons/Navigation';
import { PlaylistVideo } from '@repo/icons/File';
import { Calendar } from '@repo/icons/Time';
import { Time } from '@repo/icons/Time';
import { Radar } from '@repo/icons/Instrument';
import { cn } from '@repo/ui/lib/utils';

// Define the type for nav items
type NavItem = {
	name: string;
	href: string;
	isNew?: boolean;
	hasDropdown?: boolean;
	isButton?: boolean;
	icon?: React.ReactNode;
	mobileIcon?: React.ReactNode;
	isMobile?: boolean;
};

function DropdownContent({ placeId }: { placeId: string }) {
	const dropdownLinks = [
		{
			name: "Yesterday's Weather",
			href: `https://www.wunderground.com/history/daily/${placeId}/yesterday`,
			icon: <ExternalLink className="h-4 w-4" />,
		},
		{
			name: 'Allergy Tracker',
			href: `/forecast/allergy/l/${placeId}`,
		},
		{
			name: 'Cold & Flu',
			href: `/forecast/cold-flu/l/${placeId}`,
		},
		{
			name: 'Air Quality Forecast',
			href: `/forecast/air-quality/l/${placeId}`,
		},
	];

	return (
		<div>
			<h3 className="mb-4 text-lg font-bold">SPECIALTY FORECASTS</h3>
			<div className="grid grid-cols-2 gap-4">
				{dropdownLinks.map((link) => (
					<Link key={link.name} href={link.href} className="hover:underline">
						<span className="text-base font-normal text-[#252422]">
							{link.name}
							<span>{link.icon}</span>
						</span>
					</Link>
				))}
			</div>
		</div>
	);
}

export default function LocalsuiteNav({
	location,
}: {
	location?: LocationData;
}) {
	const [isDropdownOpen, setIsDropdownOpen] = React.useState(false);

	// Use the hook to get location data including placeId
	const { effectiveLocation, isLocationLoading } = useLocationSource({
		location,
		fallbackOrder: LocationFallbackOrder.PREFER_RECENT,
	});

	// Build nav items with the placeId from effectiveLocation
	const navItems: NavItem[] =
		!isLocationLoading && effectiveLocation
			? [
					{
						name: 'My Dashboard',
						href: `/mydashboard`,
						isNew: false,
					},
					{
						name: 'Today',
						href: `/weather/today/l/${effectiveLocation.placeId}`,
						mobileIcon: <Calendar className="h-8 w-8" />,
						isMobile: true,
					},
					{
						name: 'Hourly',
						href: `/weather/hourbyhour/l/${effectiveLocation.placeId}`,
						mobileIcon: <Time className="h-8 w-8" />,
						isMobile: true,
					},
					{
						name: '10 Day',
						href: `/weather/tenday/l/${effectiveLocation.placeId}`,
						mobileIcon: <Calendar className="h-8 w-8" />,
						isMobile: true,
					},
					{
						name: 'Weekend',
						href: `/weather/weekend/l/${effectiveLocation.placeId}`,
					},
					{
						name: 'Monthly',
						href: `/weather/monthly/l/${effectiveLocation.placeId}`,
					},
					{
						name: 'Radar',
						href: `/weather/radar/interactive/l/${effectiveLocation.placeId}`,
						mobileIcon: <Radar className="h-8 w-8" />,
						isMobile: true,
					},
					{
						name: 'Tropics',
						href: '/storms/hurricane/video/tropics-refresh-your-guide-to-tropical-terminology?adRef=nav',
						icon: <PlaylistVideo className="h-4 w-4" />,
						mobileIcon: <PlaylistVideo className="h-8 w-8" />,
						isMobile: true,
					},
				]
			: [];

	const date = new Date();
	const todayDate = date.getDate();

	return (
		<nav className="z-100 fixed bottom-0 left-0 right-0 border-b border-[#2524221a] bg-[var(--background)] md:relative md:z-10">
			<div className="mx-auto flex w-full max-w-7xl">
				{isLocationLoading || !effectiveLocation ? null : ( // Or a loading indicator
					<div className="h-15 flex w-full md:h-10">
						<div className="relative flex w-full flex-wrap items-stretch justify-around overflow-hidden">
							{navItems.map((item) => (
								<Link
									key={item.name}
									href={item.href}
									className={cn(
										'relative mt-[1px] flex h-[calc(100%-2px)] items-center justify-center gap-[5px] text-nowrap px-[15px] text-xs text-[#676767] outline-offset-0',
										'md:text-base md:text-gray-800',
										"after:absolute after:bottom-[-1px] after:left-1/2 after:h-[2px] after:w-0 after:-translate-x-1/2 after:bg-gray-800 after:transition-[width] after:duration-300 after:content-[''] hover:after:w-[calc(100%-10px)]",
										{
											'hidden md:flex': !item.isMobile,
										},
									)}
								>
									{!item.mobileIcon && item.icon}
									<span className="flex flex-col items-center">
										{item.mobileIcon && (
											<span className="relative md:hidden">
												{item.mobileIcon}
												{item.name === 'Today' && (
													<span className="absolute left-0 top-3 w-full text-center text-xs">
														{todayDate}
													</span>
												)}
											</span>
										)}
										{item.name}
										{item.isNew && (
											<span className="ml-2 rounded bg-black px-1.5 py-0.5 text-xs text-white">
												NEW
											</span>
										)}
									</span>
								</Link>
							))}
						</div>
						<div className="flex hidden shrink-0 items-center justify-center md:inline">
							<Popover open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
								<PopoverTrigger asChild>
									<button className="relative mt-[1px] flex h-[calc(100%-2px)] cursor-pointer items-center px-[15px] after:absolute after:bottom-[-1px] after:left-1/2 after:h-[2px] after:w-0 after:-translate-x-1/2 after:bg-gray-800 after:transition-[width] after:duration-300 after:content-[''] hover:after:w-[calc(100%-10px)]">
										<span className="text-nowrap text-gray-800">
											More <span className="hidden lg:inline">Forecasts</span>
										</span>
										{isDropdownOpen ? (
											<TriangleUp className="ml-1 h-4 w-4" />
										) : (
											<TriangleDown className="ml-1 h-4 w-4" />
										)}
									</button>
								</PopoverTrigger>
								<PopoverContent showArrow={false}>
									<DropdownContent placeId={effectiveLocation.placeId} />
								</PopoverContent>
							</Popover>
						</div>
					</div>
				)}
			</div>
		</nav>
	);
}
