'use client';

import { useEffect } from 'react';
import { useHydrateAtoms } from 'jotai/utils';
import { debugDataAtom } from './atoms';
import type { DebugData } from './types';
import { usePathname } from 'next/navigation';
import { createLogger } from '@repo/logger';

// Import hook directly to avoid circular dependency
import { useDebugSystem } from './hooks';

const logger = createLogger('AtomDebugHydrationBoundaries');

/**
 * AtomDebugHydrationBoundaries Props
 */
interface AtomDebugHydrationBoundariesProps {
	debugData?: DebugData | null;
}

/**
 * AtomDebugHydrationBoundaries - Client Component
 *
 * This component hydrates Jotai atoms with server-side data.
 * It follows the Collector-Translator-Formatter pattern as a bridge between
 * server-side collection/translation and client-side formatting.
 *
 * It receives data collected and translated on the server and makes it
 * available to client components via Jotai atoms.
 */
export const AtomDebugHydrationBoundaries = ({
	debugData,
}: AtomDebugHydrationBoundariesProps) => {
	const { updateSection } = useDebugSystem();
	// Add pathname to detect route changes
	const pathname = usePathname();

	// Log what data we're receiving for hydration
	logger.info('Initial props received', {
		hasDebugData: !!debugData,
		currentPath: pathname,
	});

	// Initial hydration - only happens once
	useHydrateAtoms([
		[debugDataAtom, debugData || { timestamp: new Date().toISOString() }],
	]);

	// Handle client-side navigation and data updates
	useEffect(() => {
		logger.lifecycle('Path changed', pathname);

		// Store the previous URL to detect if this is a direct load or navigation
		const prevPath =
			typeof window !== 'undefined' ? sessionStorage.getItem('lastPath') : null;

		// Check if this is a direct page load (no previous path) or navigation
		const isDirectLoad = !prevPath || prevPath === pathname;

		logger.info(
			'Navigation type',
			isDirectLoad ? 'Direct load/refresh' : 'Client-side navigation',
		);

		// Update the route in debug data
		updateSection('route', pathname);

		// If this is a direct page load, we need to mark data as potentially stale
		if (isDirectLoad && !debugData) {
			// Mark existing data as stale if we don't have new data on direct load
			updateSection('meta', {
				dataState: 'stale',
				lastConfirmedPath: prevPath || null,
				currentPath: pathname,
				isDirectLoad: true,
				timestamp: new Date().toISOString(),
			});
		} else {
			// For client-side navigation or when we have debug data
			// Reset page-specific data sections that should be refreshed on navigation
			updateSection('components', {});

			// If we have debug data for this navigation, update relevant sections
			if (debugData) {
				logger.info('Updating with new debug data for path', pathname);

				// Manually update sections from debugData that should change with navigation
				if (debugData.page) updateSection('page', debugData.page);
				if (debugData.match) updateSection('match', debugData.match);
				if (debugData.timestamp)
					updateSection('timestamp', debugData.timestamp);
				if (debugData.headers) updateSection('headers', debugData.headers);
				if (debugData.environment)
					updateSection('environment', debugData.environment);

				// Also update location if it exists in debugData
				if (debugData.location) {
					updateSection('location', {
						...debugData.location,
						source: 'server-hydration',
						_updatedAt: new Date().toISOString(),
					});
				}

				// Mark data as fresh
				updateSection('meta', {
					dataState: 'fresh',
					lastConfirmedPath: pathname,
					currentPath: pathname,
					isDirectLoad: isDirectLoad,
					timestamp: new Date().toISOString(),
				});
			}
		}

		// Store current path for next navigation
		if (typeof window !== 'undefined') {
			sessionStorage.setItem('lastPath', pathname ?? '');
		}
	}, [pathname, debugData, updateSection]);

	// Log confirmation of hydration
	logger.lifecycle('Hydration complete', {
		debugData,
		pathname,
	});

	// This is a headless component
	return null;
};
