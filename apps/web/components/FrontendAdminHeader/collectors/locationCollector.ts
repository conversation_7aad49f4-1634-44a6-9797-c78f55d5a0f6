'use client';

import type { LocationData } from '@/location/types';
import { useLocationSource } from '@/location/hooks/useLocationSource';
import { useEffect } from 'react';
import { useDebugSystem } from '../state/hooks';
import { createLogger } from '@repo/logger';

const logger = createLogger('LocationCollector');

/**
 * useLocationCollector - Hook for collecting location data
 *
 * This hook uses the application's existing location hooks to collect location data
 * for debugging purposes. It follows the Collector pattern:
 * - It observes existing application state rather than creating its own
 * - It doesn't modify application state, only collects and reports it
 * - It updates the debug data atom when location changes
 *
 * @returns A function to manually update location information
 */
export function useLocationCollector() {
	const { updateSection } = useDebugSystem();
	const { effectiveLocation, isLocationLoading } = useLocationSource({});

	// Collect location data when it changes
	useEffect(() => {
		// Only update when we have valid location data and it's not loading
		if (effectiveLocation && !isLocationLoading) {
			logger.info('Location data updated', effectiveLocation.displayName);

			// Add location to debug data with metadata about collection
			updateSection('location', {
				...effectiveLocation,
				_observedFrom: 'useLocationSource',
				_collectedBy: 'LocationCollector',
				_updatedAt: new Date().toISOString(),
			});
		}
	}, [effectiveLocation, isLocationLoading, updateSection]);

	// Return a function to manually collect location
	return function updateLocationData(
		location: LocationData,
		source: string = 'manual',
	) {
		if (location) {
			updateSection('location', {
				...location,
				_observedFrom: source,
				_collectedBy: 'LocationCollector.manual',
				_updatedAt: new Date().toISOString(),
			});
		}
	};
}

/**
 * LocationCollectorComponent - Collector Component
 *
 * This headless component uses useLocationCollector to automatically collect
 * location data for the debug system.
 */
export function LocationCollectorComponent() {
	// Just call the hook - all the work happens in the effect
	useLocationCollector();

	// This is a headless component
	return null;
}
