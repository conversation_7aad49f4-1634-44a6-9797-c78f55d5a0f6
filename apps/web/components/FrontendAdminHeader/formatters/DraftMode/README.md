# Draft Mode Management

This directory contains components for managing Next.js draft mode in the wx-next application.

## DraftModeManager

The `DraftModeManager` component is a consolidated solution that handles all draft mode functionality:

1. **Integration with useDraft**: Uses the existing useDraft hook for state management
2. **UI Controls**: Provides buttons for enabling and disabling draft mode
3. **State Management**: Leverages Jotai atoms through the useDraft hook
4. **Error Handling**: Provides consistent error handling and display

### Usage

```tsx
// In FrontendAdminHeader.tsx
import { DraftModeManager } from './formatters/DraftMode/DraftModeManager';

// For admin users
<DraftModeManager isAdmin={true} />

// For non-admin users (only shows controls when in draft mode)
<DraftModeManager isAdmin={false} />
```

## Architecture

The draft mode system follows the Collector-Translator-Formatter pattern:

1. **Collection**: Uses useDraft hook to access draft mode state
2. **Translation**: Uses useDraft hook methods to enable/disable draft mode
3. **Formatting**: Renders appropriate UI based on current state

## State Management

Draft mode state is managed through <PERSON><PERSON> atoms:

- `draftModeAtom`: <PERSON><PERSON>an indicating if draft mode is enabled
- `draftModeLoadingAtom`: <PERSON><PERSON><PERSON> indicating if an operation is in progress
- `draftModeErrorAtom`: Error object for any errors that occur

## API Integration

The component interacts with the draft mode API:

- `GET /api/payload/v1/draft`: Check draft mode status
- `POST /api/payload/v1/draft`: Enable draft mode
- `DELETE /api/payload/v1/draft`: Disable draft mode

## Performance Optimizations

- **Visibility Change Handling**: Refreshes state when tab becomes visible
- **Conditional Rendering**: Only shows relevant controls based on state
- **Error State Management**: Provides clear feedback for error conditions

## Security

- Draft mode operations require authentication and proper user roles
- Server-side validation ensures only authorized users can change draft mode

## Improvements from Previous Implementation

The DraftModeManager consolidates functionality that was previously spread across three separate components:

1. **DraftModeInitializer**: Handled initial state fetching
2. **EnableDraftModeButton**: Provided UI for enabling draft mode
3. **ExitDraftModeButton**: Provided UI for disabling draft mode

Benefits of consolidation:

- **Reduced Code Duplication**: Eliminates duplicate fetch logic and state management
- **Simplified Component Tree**: Fewer components to manage and maintain
- **Consistent Error Handling**: Centralized error handling and display
- **Improved State Management**: Single source of truth for draft mode state
- **Better Developer Experience**: Easier to understand and modify draft mode functionality
- **Reuse of Existing Hooks**: Leverages the existing useDraft hook instead of duplicating functionality
- **DRY Principle**: Follows "Don't Repeat Yourself" by using the shared hook
