.partner<PERSON><PERSON>ine img,
.partnerByline svg {
	height: 16px;
	margin: 0;
}

.partnerByline span {
	align-items: center;
	display: flex;
	gap: 4px;
	flex-wrap: wrap;
}

.partnerByline p {
	align-items: center;
	display: flex;
	flex-wrap: nowrap;
	margin: 0;
}

.partnerByline p > a {
	margin: 4px;
	padding-top: 2px;
}

.partnerByline div > span:not(:last-child) {
	margin-bottom: 16px;
}

.partner<PERSON>yline span a {
	/* Note: @include mixins.textLink is replaced with inline styles */
	text-decoration: underline;
	display: contents;
}
