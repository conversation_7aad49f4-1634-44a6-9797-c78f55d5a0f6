import React from 'react';
import Link from '@repo/ui/components/Link/Link';
import type { Article, User } from '@/payload-types';
import { isImageObject, isUser } from '@/utils/blockGuards';
import { hasLength } from '@/utils/hasLength';
import Text from '@repo/ui/components/Text/Text';
import Image from '@repo/ui/components/Image/Image';
import { cn } from '@repo/ui/lib/utils';
import { formatDateWithOffset } from '@/utils/formatDateWithOffset';

interface ArticleAuthorsProps {
	authors?: User[] | null;
	externalAuthors?: string | null;
	updateDate?: string | null;
	variant: Article['headerLayout'];
}

export const ArticleAuthorsAndUpdateDate: React.FC<ArticleAuthorsProps> = ({
	authors,
	externalAuthors,
	updateDate,
	variant,
}) => {
	// Return null only if both authors and externalAuthors are empty
	if (!hasLength(authors) && !externalAuthors) {
		return null;
	}

	const formattedDate = formatDateWithOffset(updateDate);

	// Only process profile pictures if authors exist
	const validProfilePictures = hasLength(authors)
		? authors
				.map((author: User) => {
					const profilePicture = author.authorData?.profilePicture;

					if (isImageObject(profilePicture)) {
						return profilePicture;
					}
				})
				.filter((author) => author !== undefined)
		: [];

	const imagesWidth = 32 + (validProfilePictures.length - 1) * 16;
	const shouldShowImages =
		validProfilePictures.length > 0 && variant !== 'noAuthorImages';

	return (
		<div
			className="flex items-center pb-8 pt-4"
			data-testid="article-authors-container"
		>
			{shouldShowImages && (
				<div
					className={cn('mr-2 flex -space-x-4')}
					style={{ minWidth: `${imagesWidth}px` }}
					data-testid="author-images-container"
				>
					{validProfilePictures.map((profilePicture, index) =>
						profilePicture?.url ? (
							<Image
								key={index}
								width={32}
								height={32}
								src={profilePicture.url}
								alt={`${authors?.[index]?.authorData?.firstName || ''} ${authors?.[index]?.authorData?.lastName || ''}`}
								className="flatplan_authorImage my-0 h-8 w-8 rounded-full border-2 border-white object-cover"
								style={{ zIndex: validProfilePictures.length - index }}
								data-flatplan-id="flatplan_authorImage"
								data-testid={`author-image-${index}`}
								objectFit="cover"
							/>
						) : null,
					)}
				</div>
			)}
			<p className="inline" data-testid="author-text-container">
				<Text
					variant="Body.S"
					className="mr-1 inline leading-[1rem]"
					data-testid="by-text"
					elementType="span"
				>
					By
				</Text>
				{hasLength(authors) &&
					authors.map((author, index) => {
						if (isUser(author)) {
							const authorFullName = `${author.authorData?.firstName} ${author.authorData?.lastName}`;
							const isLastAuthor = index === authors.length - 1;
							const isSecondToLastAuthor = index === authors.length - 2;

							return (
								<React.Fragment key={author.id}>
									{author?.authorData?.bioUrl ? (
										<Link
											href={author.authorData.bioUrl}
											data-testid={`author-link-${index}`}
										>
											<Text
												className="flatplan_authorName inline leading-[1rem] underline"
												variant="Body.S"
												elementType="span"
												data-flatplan-id="flatplan_authorName"
												data-testid={`author-name-linked-${index}`}
											>
												{authorFullName}
											</Text>
										</Link>
									) : (
										<Text
											className="flatplan_authorName inline leading-[1rem]"
											variant="Body.S"
											elementType="span"
											data-flatplan-id="flatplan_authorName"
											data-testid={`author-name-${index}`}
										>
											{authorFullName}
										</Text>
									)}
									{!isLastAuthor && (
										<Text
											variant="Body.S"
											elementType="span"
											className={`inline leading-[1rem] ${isSecondToLastAuthor ? 'mx-1' : 'mr-1'}`}
											data-testid={`author-separator-${index}`}
										>
											{isSecondToLastAuthor ? 'and' : ','}
										</Text>
									)}
								</React.Fragment>
							);
						}
						return null; // Return null if author is a string (ID)
					})}

				{/* Display external authors if they exist */}
				{externalAuthors && (
					<Text
						variant="Body.S"
						elementType="span"
						className={`inline leading-[1rem] ${hasLength(authors) ? 'ml-1' : ''}`}
						data-testid="external-authors"
					>
						{hasLength(authors) ? `, ${externalAuthors}` : externalAuthors}
					</Text>
				)}

				{/* Display the update date */}
				{formattedDate && (
					<span className="whitespace-nowrap">
						<Text
							variant="Body.S"
							elementType="span"
							color="secondary"
							className="mx-1 inline leading-[1rem]"
							data-testid="date-separator"
						>
							•
						</Text>
						<Text
							variant="Body.S"
							elementType="span"
							color="secondary"
							className="inline whitespace-nowrap leading-[1rem]"
							data-testid="update-date"
						>
							{formattedDate}
						</Text>
					</span>
				)}
			</p>
		</div>
	);
};
