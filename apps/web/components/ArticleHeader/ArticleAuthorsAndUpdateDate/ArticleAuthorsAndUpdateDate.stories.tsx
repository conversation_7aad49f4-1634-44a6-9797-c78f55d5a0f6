import type { Meta, StoryObj } from '@storybook/react';
import { ArticleAuthorsAndUpdateDate } from './ArticleAuthorsAndUpdateDate';
import type { User } from '@/payload-types';

const meta: Meta<typeof ArticleAuthorsAndUpdateDate> = {
	title: 'Components / Article Header / Authors And Update Date',
	component: ArticleAuthorsAndUpdateDate,
	parameters: {
		layout: 'padded',
		viewport: {
			defaultViewport: 'mobile2',
		},
	},
};

export default meta;
type Story = StoryObj<typeof ArticleAuthorsAndUpdateDate>;

const getDaysAgo = (days: number) => {
	const date = new Date();
	date.setDate(date.getDate() - days);
	return date.toISOString();
};

// Mock author data
const authors = {
	stephanie: {
		id: '1',
		collection: 'users',
		authorData: {
			firstName: 'Stephanie',
			lastName: 'Quick',
			bioUrl: '/bios/stephanie-quick',
			profilePicture: {
				id: 'authorPicJenn',
				url: 'https://s.w-x.co/util/image/w/Untitled-1_7_0.jpg?v=at&w=200&h=200',
				seo: {
					altText: 'Profile picture of <PERSON><PERSON>',
					caption: 'Jenn <PERSON>',
				},
				updatedAt: getDaysAgo(1),
				createdAt: getDaysAgo(1),
				width: 200,
				height: 200,
			},
		},
		role: ['editor'] as (
			| 'editor'
			| 'admin'
			| 'web-developer'
			| 'authenticated'
		)[],
		email: '<EMAIL>',
		updatedAt: '2023-01-01T00:00:00.000Z',
		createdAt: '2023-01-01T00:00:00.000Z',
	} as User,
	jenn: {
		id: '2',
		collection: 'users',
		authorData: {
			firstName: 'Jenn',
			lastName: 'Jordan',
			bioUrl: '/bios/jenn-jordan',
			profilePicture: {
				id: 'authorPicJenn',
				url: 'https://s.w-x.co/util/image/w/Untitled-1_7_0.jpg?v=at&w=200&h=200',
				seo: {
					altText: 'Profile picture of Jenn Jordan',
					caption: 'Jenn Jordan',
				},
				updatedAt: getDaysAgo(1),
				createdAt: getDaysAgo(1),
				width: 200,
				height: 200,
			},
		},
		role: ['editor'] as (
			| 'editor'
			| 'admin'
			| 'web-developer'
			| 'authenticated'
		)[],
		email: '<EMAIL>',
		updatedAt: '2023-01-01T00:00:00.000Z',
		createdAt: '2023-01-01T00:00:00.000Z',
	} as User,
	jan: {
		id: '3',
		collection: 'users',
		authorData: {
			firstName: 'Jan',
			lastName: 'Wesner Childs',
			bioUrl: '/bios/jan-childs',
			profilePicture: {
				id: 'authorPicJenn',
				url: 'https://s.w-x.co/util/image/w/Untitled-1_7_0.jpg?v=at&w=200&h=200',
				seo: {
					altText: 'Profile picture of Jenn Jordan',
					caption: 'Jenn Jordan',
				},
				updatedAt: getDaysAgo(1),
				createdAt: getDaysAgo(1),
				width: 200,
				height: 200,
			},
		},
		role: ['editor'] as (
			| 'editor'
			| 'admin'
			| 'web-developer'
			| 'authenticated'
		)[],
		email: '<EMAIL>',
		updatedAt: '2023-01-01T00:00:00.000Z',
		createdAt: '2023-01-01T00:00:00.000Z',
	} as User,
	alex: {
		id: '4',
		collection: 'users',
		authorData: {
			firstName: 'Alex',
			lastName: 'Johnson',
			bioUrl: '/authors/alex-johnson',
			profilePicture: null,
		},
		role: ['editor'] as (
			| 'editor'
			| 'admin'
			| 'web-developer'
			| 'authenticated'
		)[],
		email: '<EMAIL>',
		updatedAt: '2023-01-01T00:00:00.000Z',
		createdAt: '2023-01-01T00:00:00.000Z',
	} as User,
};

// Author count variants
const authorCountVariants: {
	name: string;
	authors: User[];
	externalAuthors?: string;
	description: string;
	updateDate: string;
}[] = [
	{
		name: 'Single Author - Just Updated',
		authors: [authors.stephanie],
		description: 'One author with profile picture, just updated',
		updateDate: getDaysAgo(0),
	},
	{
		name: 'Two Authors - Yesterday',
		authors: [authors.stephanie, authors.jenn],
		description: 'Two authors with profile pictures, updated yesterday',
		updateDate: getDaysAgo(1),
	},
	{
		name: 'Multiple Authors - May 23, 2025',
		authors: [authors.stephanie, authors.jenn, authors.jan],
		description: 'Three authors with profile pictures, updated May 23, 2025',
		updateDate: '2025-05-23T16:55:19.453Z', // May 23, 2025
	},
	{
		name: 'Author Without Picture - April 30, 2025',
		authors: [authors.alex],
		description: 'Author without profile picture, updated April 30, 2025',
		updateDate: '2025-04-30T16:55:19.453Z', // April 30, 2025
	},
	{
		name: 'External Authors Only - Last Week',
		authors: [],
		externalAuthors: <AUTHORS>
		description: 'Only external authors, updated last week',
		updateDate: '2025-06-17T16:55:19.453Z',
	},
	{
		name: 'Author with External Authors - 3 Days Ago',
		authors: [authors.stephanie],
		externalAuthors: <AUTHORS>
		description: 'One author with external authors, updated 3 days ago',
		updateDate: getDaysAgo(3),
	},
	{
		name: 'Multiple Authors with External Authors - June 15, 2025',
		authors: [authors.stephanie, authors.jenn],
		externalAuthors: <AUTHORS>
		description: 'Two authors with external authors, updated June 15, 2025',
		updateDate: '2025-06-15T16:55:19.453Z',
	},
];

export const Variants: Story = {
	render: () => (
		<div className="space-y-6 p-6">
			<h2 className="text-2xl font-bold">Variants</h2>
			<div className="space-y-4">
				{authorCountVariants.map((variant) => (
					<div key={variant.name} className="flex flex-col border-b">
						<div className="">
							<h3 className="pb-1 text-lg font-semibold">{variant.name}</h3>
							<p className="pb-2 text-sm text-gray-500">
								{variant.description}
							</p>
						</div>
						<ArticleAuthorsAndUpdateDate
							authors={variant.authors}
							externalAuthors={variant.externalAuthors}
							variant="default"
							updateDate={variant.updateDate}
						/>
					</div>
				))}
			</div>
		</div>
	),
};
