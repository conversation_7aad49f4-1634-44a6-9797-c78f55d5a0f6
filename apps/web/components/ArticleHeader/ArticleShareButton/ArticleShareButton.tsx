'use client';

import { FC, useState } from 'react';
import { X } from 'lucide-react';
import {
	Popover,
	PopoverArrow,
	PopoverContent,
	PopoverTrigger,
} from '@repo/ui/components/Popover/Popover';
import Text from '@repo/ui/components/Text/Text';
import { MobileDrawer } from '@repo/ui/components/MobileDrawer/MobileDrawer';
import { ShareButton } from './ShareButton';
import { ShareOptions } from './ShareOptions';
import type { User } from '@/payload-types';
import { useMobileMedia } from '@/hooks/useMobileMedia';

export const ArticleShareButton: FC<{
	articleTitle: string;
	articleImageUrl?: string;
	authors?: User[];
}> = ({ articleTitle, articleImageUrl, authors }) => {
	const [copied, setCopied] = useState(false);
	const [isOpen, setIsOpen] = useState(false);
	const isMobile = useMobileMedia();

	const authorNames = authors?.reduce((appended, { authorData }, ix) => {
		if (!authorData) return appended;
		const fullName = `${authorData.firstName} ${authorData.lastName}`;
		const isLast = ix === authors.length - 1;
		const isBeforeLast = ix === authors.length - 2;

		return appended + fullName + (isLast ? '' : isBeforeLast ? ' and ' : ', ');
	}, '');

	const renderShareOptions = () => (
		<ShareOptions
			setCopied={setCopied}
			setIsOpen={setIsOpen}
			copied={copied}
			title={articleTitle}
			imageUrl={articleImageUrl}
			subText={authorNames && 'By ' + authorNames}
		/>
	);

	return (
		<div
			className="flex items-center justify-between"
			data-testid="article-share-container"
		>
			{/* Use MobileDrawer for mobile view */}
			{isMobile ? (
				<>
					<ShareButton onClick={() => setIsOpen(true)} />
					<MobileDrawer
						title="Share this article"
						isOpen={isOpen}
						setIsOpen={setIsOpen}
						data-testid="share-mobile-drawer"
					>
						{renderShareOptions()}
					</MobileDrawer>
				</>
			) : (
				/* Use Popover for desktop view */
				<Popover
					open={isOpen}
					onOpenChange={setIsOpen}
					data-testid="share-popover"
				>
					<PopoverTrigger>
						<ShareButton asChild />
					</PopoverTrigger>
					<PopoverContent
						arrowPadding={2}
						align="end"
						alignOffset={-2}
						className="drop-shadow-lg/25 relative z-10 w-[312px] rounded-2xl border-0 bg-white p-[16px]"
						data-testid="share-popover-content"
					>
						<PopoverArrow width={21} height={14} className="fill-white" />
						<div className="mb-[8px] flex items-center justify-between">
							<Text
								variant="Body.L"
								className="font-bold"
								data-testid="share-title"
							>
								Share this article
							</Text>
							<button
								onClick={() => setIsOpen(false)}
								className="cursor-pointer text-black hover:text-gray-700"
								data-testid="share-close-button"
							>
								<X className="h-6 w-6" />
							</button>
						</div>
						{renderShareOptions()}
					</PopoverContent>
				</Popover>
			)}
		</div>
	);
};
