'use client';

import React from 'react';
import { Mail, Check, Link } from 'lucide-react';
import Text from '@repo/ui/components/Text/Text';
import { useTrackArticleShare } from '@/analytics/mparticle/hooks/useTrackArticleShare';
import { Facebook } from '@repo/icons/Social';
import { Twitter } from '@repo/icons/Social';
import { Reddit } from '@repo/icons/Social';
import { LinkedIn } from '@repo/icons/Social';
import { BlueSky } from '@repo/icons/Social';
import { WhatsApp } from '@repo/icons/Social';
import { cn } from '@repo/ui/lib/utils';
import Image from '@repo/ui/components/Image/Image';

interface ShareOptionsProps {
	setIsOpen: (isOpen: boolean) => void;
	setCopied: (isCopied: boolean) => void;
	copied: boolean;
	title: string;
	imageUrl?: string;
	subText?: string;
}

export const ShareOptions: React.FC<ShareOptionsProps> = ({
	setIsOpen,
	setCopied,
	copied,
	title,
	imageUrl,
	subText,
}) => {
	const trackArticleShare = useTrackArticleShare();

	const processSocialShare = (platform: string) => {
		if (typeof window === 'undefined') return;

		const url = encodeURIComponent(window.location.href);
		const text = encodeURIComponent(title);
		let shareUrl = '';

		switch (platform) {
			case 'twitter':
				shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${text}`;
				break;
			case 'facebook':
				shareUrl = `https://www.facebook.com/sharer.php?u=${url}`;
				break;
			case 'linkedin':
				shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}`;
				break;
			case 'reddit':
				shareUrl = `https://www.reddit.com/submit?url=${url}&title=${text}`;
				break;
			case 'bluesky':
				shareUrl = `https://bsky.app/intent/compose?text=${text}%20${url}`;
				break;
			case 'whatsapp':
				shareUrl = `https://wa.me/?text=${text}%20${url}`;
				break;
		}

		if (!shareUrl) return;

		window.open(shareUrl, '_blank');
		trackArticleShare({ sharedMethod: platform });
		setIsOpen(false);
	};

	const handleTwitterShare = () => processSocialShare('twitter');

	const handleFacebookShare = () => processSocialShare('facebook');

	const handleLinkedInShare = () => processSocialShare('linkedin');

	const handleRedditShare = () => processSocialShare('reddit');

	const handleBlueskyShare = () => processSocialShare('bluesky');

	const handleWhatsAppShare = () => processSocialShare('whatsapp');

	const handleEmailShare = () => {
		if (typeof window === 'undefined') return;

		const mailtoUrl = `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(window.location.href)}`;

		// For testing purposes, store the mailto URL in a data attribute
		document.body.setAttribute('data-mailto-url', mailtoUrl);

		// Proceed with the actual email sharing
		window.location.href = mailtoUrl;
		trackArticleShare({ sharedMethod: 'email' });
		setIsOpen(false);
	};

	const handleCopyLink = () => {
		if (typeof navigator === 'undefined') return;

		navigator.clipboard.writeText(window.location.href).then(() => {
			setCopied(true);
			trackArticleShare({ sharedMethod: 'linkCopy' });
			setTimeout(() => setIsOpen(false), 3000);
			setTimeout(() => setCopied(false), 3400);
		});
	};

	const sharingOptions = [
		{
			key: 'facebook',
			name: 'Facebook',
			icon: Facebook,
			action: handleFacebookShare,
		},
		{
			key: 'twitter',
			name: 'X',
			icon: Twitter,
			action: handleTwitterShare,
		},
		{
			key: 'reddit',
			name: 'Reddit',
			icon: Reddit,
			action: handleRedditShare,
		},
		{
			key: 'linkedin',
			name: 'LinkedIn',
			icon: LinkedIn,
			action: handleLinkedInShare,
		},
		{
			key: 'bluesky',
			name: 'Bluesky',
			icon: BlueSky,
			action: handleBlueskyShare,
		},
		{
			key: 'whatsapp',
			name: 'WhatsApp',
			icon: WhatsApp,
			action: handleWhatsAppShare,
		},
		{
			key: 'email',
			name: 'Email',
			icon: Mail,
			action: handleEmailShare,
		},
		{
			key: 'copy',
			name: copied ? 'Copied' : 'Copy link',
			icon: copied ? Check : Link,
			action: handleCopyLink,
		},
	];

	return (
		<>
			<div className="flex gap-2">
				{imageUrl && (
					<Image
						src={imageUrl}
						alt=""
						className="md:h-16.75 h-18 w-auto rounded-lg"
					/>
				)}
				<p
					className={cn(
						'line-clamp-3 overflow-hidden text-ellipsis',
						// element height should be 3 * line-height
						'h-18 text-xl font-semibold leading-6',
						'md:h-16.75 md:text-base md:font-bold md:leading-[140%]',
					)}
				>
					{title}
				</p>
			</div>

			{subText && (
				<Text
					variant="Body.S"
					className="m-0 mt-2 leading-[115%] text-gray-600"
				>
					{subText}
				</Text>
			)}

			<hr className="my-4 border-[#e9e9e9]" />

			<div className="space-y-4" data-testid="share-options-container">
				{sharingOptions.map((option) => (
					<button
						key={option.key}
						onClick={option.action}
						className="flex h-6 w-full cursor-pointer items-center rounded-md hover:bg-gray-50"
						data-testid={`share-${option.key}-button`}
					>
						<div
							className="mr-[8px] flex h-6 w-6 items-center justify-center"
							data-testid={`share-${option.key}-icon`}
						>
							<option.icon className="w-6" />
						</div>
						<Text variant="Body.M" data-testid={`share-${option.key}-text`}>
							{option.name}
						</Text>
					</button>
				))}
			</div>
		</>
	);
};
