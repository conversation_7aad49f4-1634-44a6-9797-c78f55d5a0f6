import React, { useEffect, useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { ArticleShareButton } from './ArticleShareButton';
import { ShareButton } from './ShareButton';
import { ShareOptions } from './ShareOptions';
import { X } from 'lucide-react';
import {
	Popover,
	PopoverArrow,
	PopoverContent,
	PopoverTrigger,
} from '@repo/ui/components/Popover/Popover';
import Text from '@repo/ui/components/Text/Text';
import { User } from '@/payload-types';

const meta: Meta<typeof ArticleShareButton> = {
	title: 'Components / Article Header / Share Button',
	component: ArticleShareButton,
	parameters: {
		layout: 'padded',
		docs: {
			description: {
				component:
					'A responsive share button component that allows users to share content via various platforms. The component adapts its UI based on the device type (mobile or desktop).',
			},
		},
	},
};

type Story = StoryObj<typeof ArticleShareButton>;

// Story showing the share dialog open in mobile view
export const MobileView: Story = {
	parameters: {
		viewport: {
			defaultViewport: 'mobile2',
		},
	},
	args: {
		articleTitle: 'Check out this article! You might find it interesting.',
		articleImageUrl: 'https://s.w-x.co/util/image/w/gettyimages-172215539.jpg',
		authors: [
			{
				authorData: {
					firstName: 'John',
					lastName: 'Doe',
				},
			} as User,
			{
				authorData: {
					firstName: 'Jane',
					lastName: 'Smith',
				},
			} as User,
		],
	},
	decorators: [
		(Story) => {
			// Mock window.innerWidth and open the dialog
			useEffect(() => {
				const originalInnerWidth = window.innerWidth;
				Object.defineProperty(window, 'innerWidth', {
					writable: true,
					configurable: true,
					value: 375, // iPhone size
				});

				// Trigger resize event to update component state
				window.dispatchEvent(new Event('resize'));

				// Find and click the share button after a short delay
				const timer = setTimeout(() => {
					const shareButton = document.querySelector(
						'button.flex.cursor-pointer.items-center',
					);
					if (shareButton) {
						(shareButton as HTMLButtonElement).click();
					}
				}, 500);

				// Cleanup
				return () => {
					clearTimeout(timer);
					Object.defineProperty(window, 'innerWidth', {
						writable: true,
						configurable: true,
						value: originalInnerWidth,
					});
					window.dispatchEvent(new Event('resize'));
				};
			}, []);

			return <Story />;
		},
	],
};

// Wrapper component that forces the popover to be open
const OpenPopoverWrapper = () => {
	const [isOpen, setIsOpen] = useState(true);
	const [copied, setCopied] = useState(false);
	const [_, setIsMobile] = useState(false);

	// Check if the screen is desktop
	useEffect(() => {
		// Force desktop view
		setIsMobile(false);
	}, []);

	return (
		<div
			className="flex items-center justify-between"
			data-testid="article-share-container"
		>
			<Popover
				open={isOpen}
				onOpenChange={setIsOpen}
				data-testid="share-popover"
			>
				<PopoverTrigger>
					<ShareButton asChild />
				</PopoverTrigger>
				<PopoverContent
					arrowPadding={2}
					align="end"
					alignOffset={-2}
					className="drop-shadow-lg/25 relative z-10 w-[312px] rounded-2xl border-0 bg-white p-[16px]"
					data-testid="share-popover-content"
				>
					<PopoverArrow width={21} height={14} className="fill-white" />
					<div className="mb-[8px] flex items-center justify-between">
						<Text
							variant="Body.L"
							className="font-bold"
							data-testid="share-title"
						>
							Share this article
						</Text>
						<button
							onClick={() => setIsOpen(false)}
							className="cursor-pointer text-black hover:text-gray-700"
							data-testid="share-close-button"
						>
							<X className="h-6 w-6" />
						</button>
					</div>
					<ShareOptions
						setCopied={setCopied}
						setIsOpen={setIsOpen}
						copied={copied}
						title="Check out this article! You might find it interesting."
						subText="By John Doe and Jane Smith"
						imageUrl="https://s.w-x.co/util/image/w/gettyimages-172215539.jpg"
					/>
				</PopoverContent>
			</Popover>
		</div>
	);
};

// Story showing the share popover open in desktop view
export const DesktopView: Story = {
	parameters: {
		viewport: {
			defaultViewport: 'desktop',
		},
	},
	render: () => <OpenPopoverWrapper />,
};

export default meta;
