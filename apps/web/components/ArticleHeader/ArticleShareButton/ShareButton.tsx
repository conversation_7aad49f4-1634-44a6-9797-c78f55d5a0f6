import Text from '@repo/ui/components/Text/Text';
import { Share } from '@repo/icons/Technology';

interface ShareButtonProps {
	onClick?: () => void;
	asChild?: boolean;
}

export const ShareButton: React.FC<ShareButtonProps> = ({
	onClick = () => undefined,
	asChild = false,
}: ShareButtonProps) => {
	const commonProps = {
		className: 'flex cursor-pointer items-center text-sm text-gray-500',
		onClick,
		'data-testid': 'share-button',
	};

	const content = (
		<div
			className="align-center flex items-center"
			data-testid="article-share-button"
		>
			<Share
				size="sm"
				className="m-0 mr-[8px]"
				data-testid="share-icon-image"
			/>
			<Text
				variant="Body.S"
				color="brandDark"
				className="m-0 font-bold"
				data-testid="share-button-text"
			>
				Share
			</Text>
		</div>
	);

	return asChild ? (
		<div {...commonProps}>{content}</div>
	) : (
		<button {...commonProps}>{content}</button>
	);
};
