import { Text } from '@repo/ui/components/Text/Text';
import Link from '@repo/ui/components/Link/Link';
import type { Tag } from '@/payload-types';
import { cn } from '@repo/ui/lib/utils';
import AtmosphereLogo from '@/assets/article/atmosphereLogo.svg';
import Image from '@repo/ui/components/Image/Image';

interface ArticleCategoryLinkProps {
	category?: {
		relationTo: 'tags';
		value: string | Tag;
	};
}

export const ArticleCategoryLink: React.FC<ArticleCategoryLinkProps> = ({
	category,
}: ArticleCategoryLinkProps) => {
	if (typeof category?.value === 'string') {
		return null;
	}

	const isAtmosphere = category?.value?.name
		.toLowerCase()
		.includes('atmosphere');
	const hasUrl = Boolean(category?.value.fullPath);

	const displayComponent = isAtmosphere ? (
		<Image
			src={AtmosphereLogo.src}
			alt=""
			width={172}
			height={32}
			className="m-0 w-[172px]"
			rounded={false}
			data-testid="article-category-image"
		/>
	) : (
		<Text
			className={cn(
				'm-0 font-bold',
				hasUrl && 'underline',
				'flatplan_category',
			)}
			variant="Body.S"
			data-flatplan-id="flatplan_category"
			data-testid="article-category-text"
		>
			{category?.value?.name}
		</Text>
	);

	return hasUrl ? (
		<Link
			href={isAtmosphere ? '/atmosphere' : `/${category?.value.fullPath}`}
			data-testid="article-category-link"
			target="_blank"
		>
			{displayComponent}
		</Link>
	) : (
		<div data-testid="article-category-container">{displayComponent}</div>
	);
};
