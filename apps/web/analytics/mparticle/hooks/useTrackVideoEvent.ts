'use client';

import { useCallback } from 'react';
import mParticle from '@mparticle/web-sdk';
import type { SDKEventAttrs } from '@mparticle/web-sdk';
import { useEventAttributes } from './useEventAttributes';
import { usePathname } from 'next/navigation';
import {
	mapIabTagsToIabContent,
	parseArrayToString,
	stripLastPartOfSlug,
} from './utils';
import { JWPlayerInstance } from '@/components/JWPlayer/types/player';
import { PlaylistItem } from '@/components/JWPlayer/types/playlist';
import { useCMSAttributes } from './useCMSAttributes';
import { useEffectiveLocation } from './useEffectiveLocation';

interface OptionsEventAttributes {
	adtitle?: string;
	contentWatchedSeconds?: number;
	errorMessage?: string;
	playReason?: string;
	videoView?: number;
}

export interface TrackVideoEventOptions {
	eventName: string;
	currentVideo: PlaylistItem;
	jwPlayer: JWPlayerInstance;
	eventAttributes?: OptionsEventAttributes;
}

interface TrackVideoAttributes extends OptionsEventAttributes {
	adBlock?: boolean;
	adsMetricsUnitAndZone?: string;
	aspectRatio?: string;
	assetName?: string;
	author?: string;
	cookiesEnabled?: boolean;
	collection?: string;
	collectionId?: string;
	contentId?: string;
	iabContent?: string;
	id?: string;
	duration?: string;
	entitlements?: string;
	lastmodifieddate?: Date;
	locale?: string;
	muted?: boolean;
	pageId?: string;
	playReason?: string;
	premiumContent?: boolean;
	providerName?: string;
	publishdate?: Date;
	schemaVersion?: string;
	sessionId?: string;
	sessionStartTime?: number;
	tagsGeo?: string;
	tagsKeyword?: string;
	tagsStorm?: string;
	title?: string;
	teaserTitle?: string;
	url?: string;
	wlocCity?: string;
	wlocCountry?: string;
	wlocState?: string;
}

/**
 * Hook for tracking video custom events in mParticle
 * @returns A function to track custom events
 */
export const useTrackVideoEvent = () => {
	const effectiveLocation = useEffectiveLocation();
	const generalAttributes = useEventAttributes({
		effectiveLocation,
	});
	const cmsAttributes = useCMSAttributes();
	const pathname = usePathname();

	const trackVideoEvent = useCallback(
		({
			eventName,
			currentVideo,
			jwPlayer,
			eventAttributes = {},
		}: TrackVideoEventOptions) => {
			if (!mParticle.isInitialized()) {
				console.warn('MParticle is not initialized');
				return;
			}

			const eventInfoAttributes: TrackVideoAttributes = {
				adBlock: jwPlayer.getAdBlock(),
				adsMetricsUnitAndZone: currentVideo?.custom?.ctx?.adzone as string,
				aspectRatio: jwPlayer?.getConfig()?.aspectratio,
				assetName: stripLastPartOfSlug(
					(currentVideo?.custom?.ctx?.assetName as string) ?? '',
				),
				author: cmsAttributes?.author || '',
				collection: currentVideo?.custom?.ctx?.pcollid as string,
				collectionId: currentVideo?.custom?.ctx?.pcollid as string, // Same as collection?
				contentId:
					cmsAttributes?.contentId || (currentVideo?.custom?.ctx?.id as string),
				entitlements: parseArrayToString(
					(currentVideo?.custom?.ctx?.entitlements as []) ?? [],
				),
				iabContent: mapIabTagsToIabContent(
					currentVideo?.custom?.ctx?.iab || {},
				),
				id: currentVideo?.custom?.ctx?.id as string,
				duration: currentVideo?.custom?.ctx?.duration as string,
				lastmodifieddate: currentVideo?.custom?.ctx?.lastmodifieddate,
				muted: jwPlayer.getMute(),
				premiumContent: currentVideo?.custom?.ctx?.premium,
				publishdate: currentVideo?.custom?.ctx?.publishdate,
				providerName: currentVideo?.custom?.ctx?.providername as string,
				tagsGeo: parseArrayToString(currentVideo?.custom?.ctx?.tagsGeo as []),
				tagsKeyword: parseArrayToString(
					currentVideo?.custom?.ctx?.tagsKeyword as [],
				),
				tagsStorm: parseArrayToString(
					currentVideo?.custom?.ctx?.tagsStorm as [],
				),
				teaserTitle: '', // Intentially left empty per requirements.
				title: '', // Intentially left empty per requirements.
				...generalAttributes,
				...eventAttributes,
				url: pathname ? stripLastPartOfSlug(pathname) : '',
			};

			try {
				mParticle.logEvent(
					eventName,
					mParticle.EventType.Other,
					eventInfoAttributes as SDKEventAttrs,
				);
				console.debug(
					`MParticle: Video Event tracked - "${eventName}"`,
					eventInfoAttributes,
				);
			} catch (error) {
				console.error(
					`MParticle: Failed to track Video Event - "${eventName}"`,
					error,
				);
			}
		},
		[
			cmsAttributes?.author,
			cmsAttributes?.contentId,
			generalAttributes,
			pathname,
		],
	);

	return trackVideoEvent;
};
