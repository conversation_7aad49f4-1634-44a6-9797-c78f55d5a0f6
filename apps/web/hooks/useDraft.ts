'use client';

import { useCallback } from 'react';
import { useAtom } from 'jotai';
import useSWR from 'swr';
import {
	draftModeAtom,
	draftModeLoadingAtom,
	draftModeErrorAtom,
} from '@/atoms/draft';

type DraftApiResponse = {
	data: DraftMode;
	error: string | null;
};
/**
 * Type for the draft mode hook return value
 */
export type DraftMode = {
	isEnabled: boolean;
	loading: boolean;
	error: Error | null;
	enable: () => Promise<void>;
	disable: () => Promise<void>;
	refresh: () => Promise<void>;
};

/**
 * Fetcher function for draft status
 * @param forceRefresh Whether to force a fresh fetch (bypass cache)
 */
const fetchDraftStatus = async (url: string, forceRefresh = false) => {
	const res = await fetch(url, {
		credentials: 'include',
		headers: {
			'Content-Type': 'application/json',
		},
		// Add cache busting for force refresh
		cache: forceRefresh ? 'no-store' : 'default',
	});

	if (!res.ok) {
		const errorData = await res.json();
		throw new Error(errorData.error || 'Failed to fetch draft status');
	}

	const json = (await res.json()) as DraftApiResponse;
	return json.data;
};

/**
 * Custom hook for managing Next.js draft mode
 * Provides methods to check, enable, and disable draft mode
 * Uses Jotai atoms for global state management and SWR for data fetching
 */
export const useDraft = (): DraftMode => {
	const [isEnabled, setIsEnabled] = useAtom(draftModeAtom);
	const [, setLoading] = useAtom(draftModeLoadingAtom);
	const [, setError] = useAtom(draftModeErrorAtom);

	// Use SWR for fetching draft status
	const {
		data,
		error,
		isLoading,
		mutate: mutateDraftStatus,
	} = useSWR('/api/payload/v1/draft', fetchDraftStatus, {
		onSuccess: (data) => {
			setIsEnabled(data?.isEnabled || false);
			setLoading(false);
		},
		onError: (err) => {
			setError(
				err instanceof Error ? err : new Error('Failed to fetch draft status'),
			);
			setIsEnabled(false);
			setLoading(false);
		},
		revalidateOnFocus: true,
		dedupingInterval: 5000, // 5 seconds
	});

	/**
	 * Enables draft mode
	 */
	const enable = useCallback(async () => {
		try {
			setLoading(true);
			const res = await fetch('/api/payload/v1/draft', {
				method: 'POST',
				credentials: 'include',
				headers: {
					'Content-Type': 'application/json',
				},
			});

			if (res.ok) {
				const json = (await res.json()) as DraftApiResponse;
				// Update atom directly for immediate UI update
				setIsEnabled(json.data?.isEnabled || false);
				// Revalidate SWR cache
				await mutateDraftStatus();
			} else {
				const errorData = await res.json();
				throw new Error(errorData.error || 'Failed to enable draft mode');
			}
		} catch (err) {
			setError(
				err instanceof Error ? err : new Error('Failed to enable draft mode'),
			);
		} finally {
			setLoading(false);
		}
	}, [setIsEnabled, setLoading, setError, mutateDraftStatus]);

	/**
	 * Disables draft mode
	 */
	const disable = useCallback(async () => {
		try {
			setLoading(true);
			const res = await fetch('/api/payload/v1/draft', {
				method: 'DELETE',
				credentials: 'include',
				headers: {
					'Content-Type': 'application/json',
				},
			});

			if (res.ok) {
				const json = (await res.json()) as DraftApiResponse;
				// Update atom directly for immediate UI update
				setIsEnabled(json.data?.isEnabled || false);
				// Revalidate SWR cache
				await mutateDraftStatus();
			} else {
				const errorData = await res.json();
				throw new Error(errorData.error || 'Failed to disable draft mode');
			}
		} catch (err) {
			setError(
				err instanceof Error ? err : new Error('Failed to disable draft mode'),
			);
		} finally {
			setLoading(false);
		}
	}, [setIsEnabled, setLoading, setError, mutateDraftStatus]);

	// Update the atoms based on SWR state
	const draftError = error
		? error instanceof Error
			? error
			: new Error(String(error))
		: null;

	return {
		isEnabled: data?.isEnabled || isEnabled,
		loading: isLoading,
		error: draftError,
		enable,
		disable,
		refresh: async () => {
			await mutateDraftStatus();
			return;
		},
	};
};
