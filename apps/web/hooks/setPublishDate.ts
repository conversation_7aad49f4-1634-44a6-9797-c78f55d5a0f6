import { isPublishEvent } from '@/utils/isPublishEvent';
import type { FieldHook } from 'payload';

export const setPublishDateHook =
	(fallback: string): FieldHook =>
	({ value, data, operation, originalDoc }) => {
		const publishEvent = isPublishEvent(
			operation,
			data?._status,
			originalDoc?._status,
		);
		if (!data?.publishDate && !publishEvent) return value;
		if (!data?.publishDate && !value) {
			// NOTE: This is a design decision. Do we want to preserve the original pub date forever?
			// another option would be to create a hidden field and store the originalPubDate.
			return new Date().toISOString();
		}

		if (operation === 'create' || !data?.publishDate) {
			const fallbackData = data?.[fallback] || data?.[fallback];

			if (fallbackData && typeof fallbackData === 'string') {
				return fallbackData;
			}
		}

		return value;
	};
