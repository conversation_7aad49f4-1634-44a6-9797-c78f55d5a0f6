import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

/**
 * Simple hook to fix PayloadCMS form population issues
 * 
 * This hook detects when form data isn't properly loaded and
 * forces a rehydration by adding a URL parameter that triggers
 * PayloadCMS to reload the document data.
 */
export const useFormRehydration = () => {
  const router = useRouter();

  useEffect(() => {
    // Only run in PayloadCMS admin
    if (!window.location.pathname.includes('/payload/admin')) {
      return;
    }

    // Only run on edit pages
    if (!window.location.pathname.includes('/edit/')) {
      return;
    }

    // Check if we need to force rehydration
    const checkAndRehydrate = () => {
      // Wait for form to potentially load
      setTimeout(() => {
        const form = document.querySelector('form');
        if (!form) return;

        // Check if form appears empty
        const inputs = form.querySelectorAll('input[name], textarea[name], select[name]');
        let hasContent = false;

        inputs.forEach((input) => {
          const element = input as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
          if (element.name && !element.name.startsWith('_') && element.value.trim()) {
            hasContent = true;
          }
        });

        // If form is empty and we haven't already tried rehydration
        if (!hasContent && !window.location.search.includes('_rehydrate')) {
          const url = new URL(window.location.href);
          url.searchParams.set('_rehydrate', Date.now().toString());
          
          // Use router.replace to avoid adding to history
          router.replace(url.toString());
        }
      }, 1000);
    };

    // Check on page visibility (when returning from other tabs/pages)
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        checkAndRehydrate();
      }
    };

    // Initial check
    checkAndRehydrate();

    // Listen for visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [router]);
};
