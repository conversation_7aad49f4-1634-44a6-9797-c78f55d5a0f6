import type { Article } from '@/payload-types';

export interface ArticleDataProps {
	categoryId: string;
	topicId: string;
	authorId: string;
	tenantId: string;
	featuredImageId: string;
}

export const createArticleData = ({
	categoryId,
	topicId,
	authorId,
	tenantId,
	featuredImageId,
}: ArticleDataProps) => {
	// Use a more specific type that ensures required fields are present
	const articles: Array<
		Omit<Article, 'createdAt' | 'updatedAt' | 'id' | 'sizes'>
	> = [
		{
			title: 'Major Storm System Approaching',
			headerLayout: 'default',
			// assetName is removed as it will be generated by the hook
			slug: 'major-storm-system',
			partnerByline: {
				type: 'text',
				text: '',
			},
			publishDate: new Date().toISOString(),
			tenant: tenantId,
			featuredImage: featuredImageId,
			category: {
				relationTo: 'tags',
				value: categoryId,
			},
			topic: {
				relationTo: 'tags',
				value: topicId,
			},
			authors: [authorId],
			content: {
				body: {
					root: {
						children: [
							{
								children: [],
								direction: null,
								format: '',
								indent: 0,
								type: 'paragraph',
								version: 1,
								textFormat: 0,
								textStyle: '',
							},
							{
								children: [
									{
										detail: 0,
										format: 0,
										mode: 'normal',
										style: '',
										text: 'A major storm system is approaching the Southeast region. Residents should prepare for heavy rainfall and potential flooding.',
										type: 'text',
										version: 1,
									},
								],
								direction: 'ltr',
								format: '',
								indent: 0,
								type: 'paragraph',
								version: 1,
								textFormat: 0,
								textStyle: '',
							},
						],
						direction: null,
						format: '',
						indent: 0,
						type: 'root',
						version: 1,
					},
				},
			},
			// Add coreMetadata field based on the hooks in the files
			coreMetadata: {
				createdBy: '<EMAIL>',
				updatedBy: '<EMAIL>',
			},
			// Add SEO metadata based on the fields in Articles collection
			seo: {
				title: 'Major Storm System Approaching | Weather News',
				description:
					'A major storm system is approaching the Southeast region. Residents should prepare for heavy rainfall and potential flooding.',
			},
			_status: 'published',
		},
		{
			title: 'Hurricane Season Outlook',
			headerLayout: 'default',
			// assetName is removed as it will be generated by the hook
			slug: 'hurricane-season-outlook',
			partnerByline: {
				type: 'text',
				text: '',
			},
			publishDate: new Date().toISOString(),
			tenant: tenantId,
			featuredImage: featuredImageId,
			category: {
				relationTo: 'tags',
				value: categoryId,
			},
			topic: {
				relationTo: 'tags',
				value: topicId,
			},
			authors: [authorId],
			content: {
				body: {
					root: {
						children: [
							{
								children: [],
								direction: null,
								format: '',
								indent: 0,
								type: 'paragraph',
								version: 1,
								textFormat: 0,
								textStyle: '',
							},
							{
								children: [
									{
										detail: 0,
										format: 0,
										mode: 'normal',
										style: '',
										text: "Meteorologists predict an above-average hurricane season this year. Here's what you need to know to prepare.",
										type: 'text',
										version: 1,
									},
								],
								direction: 'ltr',
								format: '',
								indent: 0,
								type: 'paragraph',
								version: 1,
								textFormat: 0,
								textStyle: '',
							},
						],
						direction: null,
						format: '',
						indent: 0,
						type: 'root',
						version: 1,
					},
				},
			},
			// Add coreMetadata field based on the hooks in the files
			coreMetadata: {
				createdBy: '<EMAIL>',
				updatedBy: '<EMAIL>',
			},
			// Add SEO metadata based on the fields in Articles collection
			seo: {
				title: 'Hurricane Season Outlook | Weather News',
				description:
					"Meteorologists predict an above-average hurricane season this year. Here's what you need to know to prepare.",
			},
			_status: 'published',
		},
	];

	return articles;
};
