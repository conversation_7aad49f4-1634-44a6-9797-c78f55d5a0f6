import type { CollectionSlug, GlobalSlug } from 'payload';
import { MigrateDownArgs, MigrateUpArgs } from '@payloadcms/db-mongodb';

// Import data modules
import { tenants } from './data/tenants';
import { createUserData } from './data/users';
import { baseTags, childTags } from './data/tags';
import { createArticleData, type ArticleDataProps } from './data/articles';
import { image1 } from './data/image-1';
import { dctMap1, dctMap2 } from './data/dctMaps';

const collections: CollectionSlug[] = [
	'tags',
	'articles',
	'images',
	'pages',
	'users',
	'context-parameters',
	'content-queries',
	'tenants',
];
const globals: GlobalSlug[] = ['site-mode'];

export async function up({ payload, req }: MigrateUpArgs): Promise<void> {
	// Create tenants
	payload.logger.warn(`🔐 AUTHENTICATION NOTICE
========================

If BYPASS_SSO is false, you can login using:

  OAuth Login:
  • Use your @weather.com email address
  • Login through Okta authentication

  Local Development Login:
  • Email: <EMAIL>
  • Password: admin

========================`);
	payload.logger.info(`— Seeding tenants...`);
	if (!tenants || tenants.length < 2) {
		throw new Error('Not enough tenants provided');
	}

	const [tenant1, tenant2] = await Promise.all([
		payload.create({
			collection: 'tenants',
			data: tenants[0]!,
			req,
		}),
		payload.create({
			collection: 'tenants',
			data: tenants[1]!,
			req,
		}),
	]);

	// Create users
	payload.logger.info(`— Seeding users...`);
	const userData = createUserData(tenant1.id, tenant2.id);
	const demoUsers = await Promise.all(
		userData.map((user) =>
			payload.create({
				collection: 'users',
				data: user,
				req,
			}),
		),
	);

	// Create tags - first create base tags (without parents)
	payload.logger.info(`— Seeding base tags...`);
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const baseTagsMap = new Map<string, any>(); // Map to store tags by slug for parent resolution

	// Create base tags first
	const demoBaseTags = await Promise.all(
		baseTags.map(async (tag) => {
			const createdTag = await payload.create({
				collection: 'tags',
				data: tag,
				req,
			});
			// Store in map for parent resolution
			if (tag.slug) {
				baseTagsMap.set(tag.slug, createdTag);
			}
			return createdTag;
		}),
	);

	// Then create child tags with resolved parent references
	payload.logger.info(`— Seeding child tags...`);
	const demoChildTags = await Promise.all(
		childTags.map(async ({ parentSlug, ...tag }) => {
			// Look up parent by slug
			const parentTag = baseTagsMap.get(parentSlug);
			if (!parentTag) {
				payload.logger.warn(
					`Parent tag with slug "${parentSlug}" not found for child tag "${tag.name}"`,
				);
				return null;
			}

			// Create child tag with resolved parent reference
			const createdTag = await payload.create({
				collection: 'tags',
				data: {
					...tag,
					parent: parentTag.id, // Set parent ID reference
				},
				req,
			});
			return createdTag;
		}),
	);

	// Combine all tags for use in the rest of the migration
	const demoTags = [...demoBaseTags, ...demoChildTags.filter(Boolean)];

	// Create images - temporarily disabled due to S3 credentials requirement
	payload.logger.info(`— Skipping image seeding (S3 credentials not configured)...`);
	const demoImages: any[] = [
		{ id: 'placeholder-1' },
		{ id: 'placeholder-2' },
		{ id: 'placeholder-3' },
	];
	// const demoImages = await Promise.all([
	// 	payload.create({
	// 		collection: 'images',
	// 		data: image1(tenant1.id),
	// 		req,
	// 	}),
	// 	payload.create({
	// 		collection: 'images',
	// 		data: dctMap1(tenant1.id),
	// 		req,
	// 	}),
	// 	payload.create({
	// 		collection: 'images',
	// 		data: dctMap2(tenant1.id),
	// 		req,
	// 	}),
	// ]);

	// Update the editor users with profile pictures - temporarily disabled
	payload.logger.info(`— Skipping profile picture updates (no real images)...`);
	// await Promise.all([
	// 	// Update TWC editor (index 1 in demoUsers array)
	// 	payload.update({
	// 		collection: 'users',
	// 		id: demoUsers[1]?.id || '',
	// 		data: {
	// 			authorData: {
	// 				...demoUsers[1]?.authorData,
	// 				profilePicture: demoImages[0]?.id || '',
	// 			},
	// 		},
	// 		req,
	// 	}),
	// 	// Update Burda editor (index 2 in demoUsers array)
	// 	payload.update({
	// 		collection: 'users',
	// 		id: demoUsers[2]?.id || '',
	// 		data: {
	// 			authorData: {
	// 				...demoUsers[2]?.authorData,
	// 				profilePicture: demoImages[1]?.id || '',
	// 			},
	// 		},
	// 		req,
	// 	}),
	// ]);

	// Create articles
	payload.logger.info(`— Seeding articles...`);

	// Find the News and Storms tags by slug
	const newsTag = demoTags.find((tag) => tag?.slug === 'news');
	const stormsTag = demoTags.find((tag) => tag?.slug === 'storms');

	if (!newsTag || !stormsTag) {
		payload.logger.error('Required tags not found: News or Storms');
		throw new Error('Required tags not found: News or Storms');
	}

	// Use the TWC editor as author, News tag as category, Storms tag as topic (no featured image for now)
	const articleInputs: ArticleDataProps = {
		categoryId: newsTag.id || '', // Category tag (News)
		topicId: stormsTag.id || '', // Topic tag (Storms)
		authorId: demoUsers[1]?.id || '', // TWC editor as author
		tenantId: tenant1.id, // TWC tenant
		featuredImageId: '', // No featured image for now
	};

	const articleData = createArticleData(articleInputs);

	// Create the articles with context to disable revalidation
	const demoArticles = await Promise.all(
		articleData.map((article) =>
			payload.create({
				collection: 'articles',
				data: article,
				context: { disableRevalidate: true }, // Add context to disable revalidation
				req,
			}),
		),
	);

	// Log success
	payload.logger.info(`Created ${demoArticles.length} articles`);

	// Explicitly create context parameters for pages
	payload.logger.info(`— Creating context parameters for pages...`);

	// Instead of directly creating context parameters, update each page to trigger the afterChangeHook
	payload.logger.info(
		`— Triggering context parameter creation via page updates...`,
	);
}

export async function down({ payload, req }: MigrateDownArgs): Promise<void> {
	// Drop all collections to clean up when migration fails
	payload.logger.info('Rolling back seed data by dropping collections...');

	try {
		// Delete all documents from each collection in reverse order to handle dependencies
		const collectionsInReverseOrder = [...collections].reverse();

		for (const collection of collectionsInReverseOrder) {
			payload.logger.info(
				`— Dropping all documents from collection: ${collection}`,
			);

			try {
				// Find all documents in the collection
				const result = await payload.find({
					req,
					collection: collection,
					limit: 1000, // Set a high limit to get all documents
				});

				// Delete each document
				if (result.docs && result.docs.length > 0) {
					await Promise.all(
						result.docs.map((doc) =>
							payload.delete({
								req,
								collection: collection,
								id: doc.id,
							}),
						),
					);
					payload.logger.info(
						`  Deleted ${result.docs.length} documents from ${collection}`,
					);
				} else {
					payload.logger.info(`  No documents found in ${collection}`);
				}
			} catch (error) {
				payload.logger.error(
					`  Error dropping documents from ${collection}: ${(error as Error).message}`,
				);
			}
		}

		// Reset globals if needed
		for (const global of globals) {
			payload.logger.info(`— Resetting global: ${global}`);
			try {
				await payload.updateGlobal({
					req,
					slug: global,
					data: {},
				});
				payload.logger.info(`  Reset global ${global}`);
			} catch (error) {
				payload.logger.error(
					`  Error resetting global ${global}: ${(error as Error).message}`,
				);
			}
		}

		payload.logger.info('Migration rollback completed successfully');
	} catch (error) {
		payload.logger.error(
			`Error during migration rollback: ${(error as Error).message}`,
		);
		throw error;
	}
}
