// storage-adapter-import-placeholder
import { mongooseAdapter } from '@payloadcms/db-mongodb';
import path from 'path';
import { buildConfig } from 'payload';
import sharp from 'sharp';
import { fileURLToPath } from 'url';
import { locales } from '@/configs/locales';

import { defaultLexical } from '@/fields/defaultLexical';
import { plugins } from '@/plugins';

/** Edited automatically by scaffolder */
import { Articles } from '@/collections/Articles';
import { Images } from '@/collections/Images';
import { Pages } from '@/collections/Pages';
import { Users } from '@/collections/Users';
import { Liveblogs } from '@/collections/Liveblogs';
import { Tags } from '@/collections/Tags';
import { Tenants } from '@/collections/Tenants';
import { ContentQueries } from '@/collections/ContentQueries';
import { ContextParameters } from '@/collections/ContextParameters';
import { AdSlots } from '@/collections/AdSlots';
import { ThirdPartyConfig } from '@/collections/ThirdPartyConfig';
import { Videos } from '@/collections/Videos';
import { Locations } from '@/collections/Locations';

/**  End of collection imports */

import { SiteMode } from '@/configs/global/SiteMode';
import { email } from 'plugins/email/nodeMailer';
/**  End of global imports */

/**
 * Task/Jobs/Workflows/Queues Imports
 * */
import { canRunCron } from './configs/access';
import { getCMAssetByID } from './jobs/contentmedia/config';
import { getContentByTags } from './jobs/contentbytags/config';
import { getContentByIds } from './jobs/contentbyids/config';
import { refreshContentQueries } from './jobs/refreshcontentqueries/config';
import { updateAdMetrics } from './jobs/updateAdMetrics/config';
import { createMongooseAdapterConfig } from './configs/db/payload-db-manager';

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);

// Create a function that builds the configuration
export default buildConfig({
	debug: process.env.VERCEL_TARGET_ENV !== 'production',
	logger: {
		options: {
			level: process.env.LOG_LEVEL || 'error',
		},
	},
	routes: {
		admin: '/payload/admin',
		api: '/api/payload',
	},
	admin: {
		user: Users.slug,
		importMap: {
			baseDir: path.resolve(dirname),
		},
		components: {
			header: ['components/Payload/Header#WxNextLink'],
			graphics: {
				Icon: 'components/Payload/Graphics#Icon',
				Logo: 'components/Payload/Graphics#Logo',
			},
			views: {
				queue: {
					Component: 'components/Payload/Views/Queue#Queue',
					path: '/queue',
				},
			},
		},
	},
	indexSortableFields: false, // Disable automatic index creation which can be slow
	upload: {
		safeFileNames: true,
	},
	localization: {
		locales,
		defaultLocale: 'en-US',
		fallback: true,
		defaultLocalePublishOption: 'active',
	},
	email,
	globals: [
		/**  global inclusion */
		SiteMode,
	],
	jobs: {
		access: {
			run: canRunCron,
		},
		tasks: [
			getCMAssetByID,
			getContentByTags,
			getContentByIds,
			refreshContentQueries,
			updateAdMetrics,
		],
	},
	collections: [
		/**  collection inclusion */
		Locations,
		ContentQueries,
		Tenants,
		Tags,
		Users,
		Images,
		Pages,
		Articles,
		Videos,
		Liveblogs,
		ContextParameters,
		AdSlots,
		ThirdPartyConfig,
	],
	editor: defaultLexical,
	secret: process.env.PAYLOAD_SECRET || '',
	typescript: {
		outputFile: path.resolve(dirname, 'payload-types.ts'),
	},
	db: mongooseAdapter(createMongooseAdapterConfig()),
	sharp,
	plugins,
});
