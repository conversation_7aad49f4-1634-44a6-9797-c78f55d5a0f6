import type { Payload } from 'payload';
import type { ContentQuery, Tag } from '@/payload-types';
import type {
	CollectionAfterChangeHook,
	PayloadRequest,
	Job,
	ValueWithRelation,
} from 'payload';
import { hasLength } from '@/utils/hasLength';
import { getUserLocale } from '@/utils/getUserLocale';

/**
 * Revalidate a path using various Next.js revalidation methods
 * This function attempts to use the appropriate method based on the environment
 */
async function revalidatePathCompat(path: string, payload: Payload) {
	try {
		// First try to use next/cache methods if available (App Router)
		try {
			const { revalidatePath } = await import('next/cache');
			payload.logger.info(`Revalidating path using App Router method: ${path}`);
			revalidatePath(path);
			return true;
		} catch (_error) {
			// Fall back to using the revalidation API
			payload.logger.info(
				`Falling back to API route revalidation for path: ${path}`,
			);

			// Construct the revalidation URL
			const baseUrl =
				process.env.NEXT_PUBLIC_SERVER_URL || 'https://localhost:3001';
			const revalidationUrl = `${baseUrl}/api/payload/v1/revalidate?path=${encodeURIComponent(path)}&secret=${process.env.PAYLOAD_SECRET}`;

			try {
				// Use fetch API to call the revalidate endpoint with POST method
				const response = await fetch(revalidationUrl, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
				});
				if (!response.ok) {
					payload.logger.warn(`Revalidation API failed for path: ${path}`);
					return false;
				}
				return true;
			} catch (fetchError) {
				payload.logger.error(
					`Failed to call revalidation API for path: ${path}`,
					fetchError,
				);
				return false;
			}
		}
	} catch (mainError) {
		payload.logger.error(
			`Error during revalidation for path: ${path}`,
			mainError,
		);
		return false;
	}
}

// Define interfaces for the query objects that match the ContentQuery structure
interface QueryBase {
	id?: string;
	syncStatus?: 'pending' | 'in-progress' | 'completed' | 'failed' | null;
	_locked?: boolean;
	_lockedByTaskID?: string;
	lastSynced?: string;
}

interface KalliopeQuery extends QueryBase {
	queryType: 'kalliope';
	kalliopeCMQSID?: string;
	tags?: (string | Tag)[] | null;
	category?: (string | Tag)[] | null;
	topic?: (string | Tag)[] | null;
	contentId?: (string | { id: string })[] | null;
}

interface DataQuery extends QueryBase {
	queryType: 'data';
	tags?: (string | ValueWithRelation)[] | null;
	category?: (string | ValueWithRelation)[] | null;
	topic?: (string | ValueWithRelation)[] | null;
	tagsOperator?: 'AND' | 'OR';
	categoryOperator?: 'AND' | 'OR';
	topicOperator?: 'AND' | 'OR';
	groupsOperator?: 'AND' | 'OR';
	kalliopeCMQSID?: string;
	contentId?: (string | { id: string })[] | null;
}

interface IdQuery extends QueryBase {
	queryType: 'id';
	tags?: (string | Tag)[] | null;
	category?: (string | Tag)[] | null;
	topic?: (string | Tag)[] | null;
	kalliopeCMQSID?: string;
	contentId?: (string | { id: string })[] | null;
}

/**
 * Hook that checks if content media assets need to be fetched and queues jobs accordingly
 * This runs after a document is created or updated, ensuring we have a valid document ID
 */
export const queueContentQueryJobsAfterChange: CollectionAfterChangeHook<
	ContentQuery
> = async ({ doc, previousDoc, req, operation, context }) => {
	// Skip if this update was triggered by a job (check context)
	if (context?.skipQueueJobs === true) {
		req.payload.logger.debug(
			`Skipping job queue for content query ID: ${doc.id} (context flag set)`,
		);
		return doc;
	}

	// Skip if no queries to process
	if (!hasLength(doc.queries)) return;

	// Get the document ID - this is guaranteed to exist in afterChange
	const contentQueryId = doc.id;

	req.payload.logger.debug(
		`Queueing jobs after ${operation} for content query ID: ${contentQueryId}`,
	);

	// For create operations, queue jobs for all queries
	// For update operations, identify new or modified queries
	let queriesToProcess = doc.queries;

	if (operation === 'update' && previousDoc && hasLength(previousDoc.queries)) {
		// Create a map of previous queries by ID for quick lookup
		const previousQueriesMap = new Map();
		previousDoc.queries.forEach((query) => {
			if (query.id) {
				previousQueriesMap.set(query.id, query);
			}
		});

		// Filter to include queries that are:
		// 1. New (not in previousQueriesMap)
		// 2. Have a pending status
		// 3. Have been modified (different content IDs, tags, etc.)
		queriesToProcess = doc.queries.filter((query) => {
			if (!query.id) return true; // New query without ID

			const previousQuery = previousQueriesMap.get(query.id);
			if (!previousQuery) return true; // New query with ID

			// Queue if status is pending
			if (query.syncStatus === 'pending') return true;

			// Queue if status is failed (retry)
			if (query.syncStatus === 'failed') return true;

			// For different query types, check different fields
			if (query.queryType === 'id') {
				// Check if contentId has changed
				const currentContentIds = JSON.stringify(query.contentId);
				const previousContentIds = JSON.stringify(previousQuery.contentId);
				return currentContentIds !== previousContentIds;
			}

			if (query.queryType === 'kalliope') {
				// Check if kalliopeCMQSID has changed
				return query.kalliopeCMQSID !== previousQuery.kalliopeCMQSID;
			}

			if (query.queryType === 'data') {
				// Check if tags, category, or topic have changed
				const currentTags = JSON.stringify(query.tags);
				const previousTags = JSON.stringify(previousQuery.tags);

				const currentCategory = JSON.stringify(query.category);
				const previousCategory = JSON.stringify(previousQuery.category);

				const currentTopic = JSON.stringify(query.topic);
				const previousTopic = JSON.stringify(previousQuery.topic);

				return (
					currentTags !== previousTags ||
					currentCategory !== previousCategory ||
					currentTopic !== previousTopic
				);
			}

			return false; // Don't queue by default
		});

		req.payload.logger.debug(
			`Found ${queriesToProcess.length} queries to process (new or modified)`,
		);
	}

	// Process queries and queue jobs
	let jobsQueued = false;

	// Process Kalliope queries
	const kalliopeQueries = queriesToProcess.filter(
		(query) => query.queryType === 'kalliope',
	) as KalliopeQuery[];
	if (hasLength(kalliopeQueries)) {
		const kalliopeJobsQueued = await processKalliopeQueries(
			kalliopeQueries,
			contentQueryId,
			req,
		);
		jobsQueued = jobsQueued || kalliopeJobsQueued;
	}

	// Process data queries
	const dataQueries = queriesToProcess.filter(
		(query) => query.queryType === 'data',
	) as DataQuery[];

	if (hasLength(dataQueries)) {
		const dataJobsQueued = await processDataQueries(
			dataQueries,
			contentQueryId,
			req,
		);
		jobsQueued = jobsQueued || dataJobsQueued;
	}

	// Process ID queries
	const idQueries = queriesToProcess.filter(
		(query) => query.queryType === 'id',
	) as IdQuery[];

	if (hasLength(idQueries)) {
		const idJobsQueued = await processIdQueries(idQueries, contentQueryId, req);
		jobsQueued = jobsQueued || idJobsQueued;
	}

	// Only revalidate if at least one job was queued successfully
	if (jobsQueued) {
		await revalidatePathCompat(
			`/admin/collections/content-queries/${contentQueryId}`,
			req.payload,
		);
	}

	// No need to return doc since we're not modifying it
};

/**
 * Process Kalliope queries
 */
async function processKalliopeQueries(
	queries: KalliopeQuery[],
	contentQueryId: string,
	req: PayloadRequest,
): Promise<boolean> {
	// Process all queries in parallel for better performance
	const jobPromises = queries.map(async (query) => {
		const {
			syncStatus,
			_locked,
			kalliopeCMQSID,
			id: queryId,
			lastSynced,
		} = query;

		if (!kalliopeCMQSID || !queryId) return false;

		// if a job is already running, don't queue another one
		if ((syncStatus && syncStatus === 'in-progress') || _locked) return false;

		try {
			const jobExists = await req.payload.find({
				collection: 'payload-jobs',
				req,
				where: {
					'input.kalliopeCMQSID': {
						equals: kalliopeCMQSID,
					},
				},
			});

			// Ensure job exists and infer its type
			const existingJob = jobExists?.docs?.[0] as Job<ContentQuery> | undefined;

			// If a job exists, delete it and requeue a new one
			if (existingJob && !existingJob?.processing) {
				await req.payload.delete({
					collection: 'payload-jobs',
					req,
					id: existingJob.id,
				});
			}

			// Queue the job to fetch content media assets
			await req.payload.jobs.queue({
				task: 'getCMAssetByID',
				req,
				input: {
					kalliopeCMQID: kalliopeCMQSID,
					queryDocID: contentQueryId,
					lastSynced: lastSynced || '',
					// @ts-expect-error this is properly types by the getUserLocale function
					locale: getUserLocale(req), // Add user locale to the job input
				},
				queue: 'content-queries',
			});

			return true; // Job was queued successfully
		} catch (error) {
			req.payload.logger.error(
				'[processKalliopeQueries] Error queueing job:',
				error,
			);
			return false;
		}
	});

	// Wait for all job operations to complete
	const results = await Promise.allSettled(jobPromises);

	// Return true if at least one job was queued successfully
	return results.some(
		(result) => result.status === 'fulfilled' && result.value === true,
	);
}

/**
 * Process data queries (tags, categories, topics)
 */
async function processDataQueries(
	queries: DataQuery[],
	contentQueryId: string,
	req: PayloadRequest,
): Promise<boolean> {
	// Get the user's locale from the request
	const userLocale = getUserLocale(req);

	// Process all queries in parallel for better performance
	const jobPromises = queries.map(async (query) => {
		const {
			syncStatus,
			_locked,
			id: queryId,
			lastSynced,
			tags,
			category,
			topic,
		} = query;

		if (!queryId) return false;

		// if a job is already running, don't queue another one
		if ((syncStatus && syncStatus === 'in-progress') || _locked) return false;

		// Skip if no tags, categories, or topics are specified
		if (!hasLength(tags) && !hasLength(category) && !hasLength(topic)) {
			req.payload.logger.warn(
				`[processDataQueries] Query ${queryId} has no tags, categories, or topics specified.`,
			);
			return false;
		}

		try {
			const jobExists = await req.payload.find({
				collection: 'payload-jobs',
				req,
				where: {
					'input.queryId': {
						equals: queryId,
					},
					'input.queryType': {
						equals: 'data',
					},
				},
			});

			// Ensure job exists and infer its type
			const existingJob = jobExists?.docs?.[0] as Job<ContentQuery> | undefined;

			// If a job exists, delete it and requeue a new one
			if (existingJob && !existingJob?.processing) {
				await req.payload.delete({
					collection: 'payload-jobs',
					req,
					id: existingJob.id,
				});
			}

			// Process the relationship fields to extract IDs
			const processedTags =
				tags?.map((tag) => (typeof tag === 'string' ? tag : tag.value)) || [];
			const processedCategories =
				category?.map((cat) => (typeof cat === 'string' ? cat : cat.value)) ||
				[];
			const processedTopics =
				topic?.map((top) => (typeof top === 'string' ? top : top.value)) || [];

			// Queue the job to fetch content by tags/categories/topics
			await req.payload.jobs.queue({
				task: 'getContentByTags',
				req,
				input: {
					queryId,
					queryDocID: contentQueryId,
					tags: processedTags,
					tagsOperator: query.tagsOperator || 'OR',
					category: processedCategories,
					categoryOperator: query.categoryOperator || 'OR',
					topic: processedTopics,
					topicOperator: query.topicOperator || 'OR',
					groupsOperator: query.groupsOperator || 'AND',
					lastSynced: lastSynced || '',
					// @ts-expect-error this is properly types by the getUserLocale function
					locale: userLocale, // Add the user's locale to the job input
				},
				queue: 'content-queries',
			});

			return true; // Job was queued successfully
		} catch (error) {
			req.payload.logger.error(
				'[processDataQueries] Error queueing job:',
				error,
			);
			return false;
		}
	});

	// Wait for all job operations to complete
	const results = await Promise.allSettled(jobPromises);

	// Return true if at least one job was queued successfully
	return results.some(
		(result) => result.status === 'fulfilled' && result.value === true,
	);
}

/**
 * Process ID queries
 */
async function processIdQueries(
	queries: IdQuery[],
	contentQueryId: string,
	req: PayloadRequest,
): Promise<boolean> {
	// Get the user's locale from the request
	const userLocale = getUserLocale(req);

	// Process all queries in parallel for better performance
	const jobPromises = queries.map(async (query) => {
		const { syncStatus, _locked, id: queryId, lastSynced, contentId } = query;

		if (!queryId) return false;

		// if a job is already running, don't queue another one
		if ((syncStatus && syncStatus === 'in-progress') || _locked) return false;

		// Skip if no content IDs are specified
		if (!hasLength(contentId)) {
			req.payload.logger.warn(
				`[processIdQueries] Query ${queryId} has no content IDs specified.`,
			);
			return false;
		}

		try {
			const jobExists = await req.payload.find({
				collection: 'payload-jobs',
				req,
				where: {
					'input.queryId': {
						equals: queryId,
					},
					'input.queryType': {
						equals: 'id',
					},
				},
			});

			// Ensure job exists and infer its type
			const existingJob = jobExists?.docs?.[0] as Job<ContentQuery> | undefined;

			// If a job exists, delete it and requeue a new one
			if (existingJob && !existingJob?.processing) {
				await req.payload.delete({
					collection: 'payload-jobs',
					req,
					id: existingJob.id,
				});
			}

			// Process the relationship fields to extract IDs as strings
			const processedContentIds =
				contentId?.map((cid) => {
					// Extract just the ID string, not the object
					return typeof cid === 'string' ? cid : cid.id;
				}) || [];

			req.payload.logger.debug(
				`Processed content IDs: ${JSON.stringify(processedContentIds)}`,
			);

			// Queue the job to fetch content by IDs
			await req.payload.jobs.queue({
				task: 'getContentByIds',
				req,
				input: {
					queryId: queryId,
					queryDocID: contentQueryId,
					contentIds: processedContentIds,
					lastSynced: lastSynced || '',
					// @ts-expect-error this is properly types by the getUserLocale function
					locale: userLocale, // Add the user's locale to the job input
				},
				queue: 'content-queries',
			});

			return true; // Job was queued successfully
		} catch (error) {
			req.payload.logger.error('[processIdQueries] Error queueing job:', error);
			return false;
		}
	});

	// Wait for all job operations to complete
	const results = await Promise.allSettled(jobPromises);

	// Return true if at least one job was queued successfully
	return results.some(
		(result) => result.status === 'fulfilled' && result.value === true,
	);
}
