import type { CollectionConfig } from 'payload';
import { name, type, parent, fullPath } from './fields';
import { slugField } from '@/fields/slug';
import { adMetricField } from './fields/adMetric';
import { versionsField } from './fields/versions';
import { iabTagsField } from './fields/iabTags';
import { queueAdMetricUpdateJobs } from './hooks/queueAdMetricUpdateJobs';

export const Tags: CollectionConfig = {
	slug: 'tags',
	defaultPopulate: {
		slug: true,
		name: true,
		fullPath: true,
	},
	labels: {
		singular: 'Tags',
		plural: 'Tags',
	},
	admin: {
		useAsTitle: 'name',
		defaultColumns: ['name', 'type', 'parent', 'fullPath', 'id'],
	},
	defaultSort: '-name',
	versions: false,
	access: {
		read: () => true,
	},
	fields: [
		name,
		...slugField('name'),
		type,
		fullPath,
		parent,
		adMetricField,
		versionsField,
		iabTagsField,
	],
	hooks: {
		afterChange: [queueAdMetricUpdateJobs],
	},
};
