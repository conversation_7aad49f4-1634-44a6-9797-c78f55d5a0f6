/* eslint-disable @typescript-eslint/no-explicit-any */
import { RelationshipField, SelectField, TextField } from 'payload';

export const name: TextField = {
	name: 'name',
	label: 'Name',
	localized: false,
	type: 'text',
	required: true,
};

// This is effectively our Vocabulary
// Content Tags, Category Tags, Location Tags, Ad Zones, Ad Metrics
export const type: SelectField = {
	name: 'type',
	type: 'select',
	localized: false,
	index: true,
	required: true,
	defaultValue: 'contentTags',
	options: [
		{ label: 'Ad Metrics', value: 'adMetrics' },
		{ label: 'Ad Zones', value: 'adZones' },
		{ label: 'Analytics Tag', value: 'analyticsTag' },
		{ label: 'Category Tags', value: 'categoryTags' },
		{ label: 'Channel', value: 'channel' },
		{ label: 'Content Tags', value: 'contentTags' },
		{ label: 'Entitlement', value: 'entitlement' },
		{ label: 'IAB Tags', value: 'iabTags' },
		{ label: 'Location Tags', value: 'locationTags' },
		{ label: 'Region', value: 'region' },
		{ label: 'State', value: 'state' },
	],
};

export const fullPath: TextField = {
	name: 'fullPath',
	label: 'Full Path',
	type: 'text',
	localized: false,
	admin: {
		readOnly: true,
		description: 'Full hierarchical path (auto-generated)',
	},
	hooks: {
		beforeChange: [
			async ({ data, req }: any) => {
				// Handle case where data might be undefined
				if (!data) return '';

				// If there's no name, return empty string
				if (!data.name) return '';

				// If there's no parent, the path is just the name
				if (!data.parent) {
					return data.name;
				}

				try {
					// Extract the parent ID from the relationship value
					let parentId: any = data.parent;
					if (typeof data.parent === 'object' && data.parent !== null) {
						if ('id' in data.parent) {
							parentId = data.parent.id;
						} else if ('value' in data.parent) {
							parentId = data.parent.value;
						}
					}

					// Get the parent tag
					const parentTag = await req.payload.findByID({
						collection: 'tags',
						id: parentId,
					});

					// If parent has a fullPath, use it to build the hierarchical path
					if (parentTag?.fullPath) {
						return `${parentTag.fullPath} > ${data.name}`;
					}

					// If parent doesn't have a fullPath yet, just use parent name + current name
					if (parentTag?.name) {
						return `${parentTag.name} > ${data.name}`;
					}
				} catch (error) {
					console.error('Error generating fullPath:', error);
				}

				// Fallback to just the name if we can't build a path
				return data.name;
			},
		],
	},
};

export const parent: RelationshipField = {
	name: 'parent',
	type: 'relationship',
	relationTo: 'tags',
	index: true,
	hasMany: false,
	required: false,
	admin: {
		position: 'sidebar',
		condition: (data) => {
			// Only show parent field for types that can have parents
			return [
				'adMetrics',
				'categoryTags',
				'contentTags',
				'locationTags',
			].includes(data?.type);
		},
	},
	filterOptions: ({ data }) => {
		// Define which parent types are valid for each child type
		const validParentTypes: Record<string, string[]> = {
			adMetrics: ['adZones'],
			categoryTags: ['categoryTags'],
			contentTags: ['contentTags'],
			locationTags: ['locationTags'],
		};

		// Return a filter that only shows valid parent options
		const tagType = data?.type as string;
		if (tagType && validParentTypes[tagType]) {
			// Return a proper Where object
			return {
				and: [
					{
						type: {
							in: validParentTypes[tagType],
						},
					},
				],
			};
		}

		// If no type is selected yet, don't show any parent options
		// Return a Where object that will match no documents
		return {
			and: [
				{
					id: {
						equals: 'no-results',
					},
				},
			],
		};
	},
	validate: async (value: any, { id, data, req }: any) => {
		// Skip validation if no parent is selected
		if (!value) return true;

		// Extract the ID from the relationship value
		// Use type assertion to handle complex relationship value types
		let parentId: string | number = value;
		if (typeof value === 'object' && value !== null) {
			// Handle relationship object format
			if ('id' in value) {
				parentId = value.id;
			} else if ('value' in value) {
				parentId = value.value;
			}
		}

		// Direct self-reference check (existing validation)
		if (parentId === id) {
			return 'A tag cannot be its own parent!';
		}

		try {
			// Get the parent tag
			const parentTag = await req.payload.findByID({
				collection: 'tags',
				id: parentId,
			});

			// Define valid parent-child type relationships
			const validParentChildTypes: Record<string, string[]> = {
				adMetrics: ['adZones'], // Ad Metrics can only have Ad Zones as parents
				adZones: [], // Ad Zones cannot have parents
				analyticsTag: [], // Analytics can not have parents
				categoryTags: ['categoryTags'], // Categories can only have categories as parents
				channel: [], // Channel tags cannot have parents
				contentTags: ['contentTags'], // Content tags can only have content tags as parents
				entitlement: [], // Entitlements cannot have parents
				iabTags: [], // IAB Tags cannot have parents
				locationTags: ['locationTags'], // Location tags can only have location tags as parents
				region: [], // Regions cannot have parents
				state: [], // States cannot have parents
			};

			// Check if this parent-child relationship is valid
			const childType = data.type as string;
			const parentType = parentTag.type as string;

			if (
				childType &&
				parentType &&
				(!validParentChildTypes[childType] ||
					!validParentChildTypes[childType].includes(parentType))
			) {
				const validTypes =
					validParentChildTypes[childType]?.join(', ') || 'none';
				return `A tag of type "${childType}" cannot have a parent of type "${parentType}". Valid parent types are: ${validTypes}.`;
			}

			// Check for circular references
			// Start with the immediate parent
			let currentParentId: any = parentId;
			// Keep track of visited parents to detect cycles
			const visitedParents = new Set([id]);

			// Follow the parent chain up to a reasonable depth limit
			const MAX_DEPTH = 100; // Prevent infinite loops in case of DB inconsistency
			let depth = 0;

			while (currentParentId && depth < MAX_DEPTH) {
				// If we've seen this parent before, we have a cycle
				if (visitedParents.has(currentParentId)) {
					return 'Circular parent reference detected! This would create an infinite loop.';
				}

				// Mark this parent as visited
				visitedParents.add(currentParentId);

				// Get the parent's data
				const parentTag = await req.payload.findByID({
					collection: 'tags',
					id: currentParentId,
				});

				// Extract the next parent ID, handling potential relationship object format
				const nextParent = parentTag?.parent;
				if (!nextParent) {
					currentParentId = null;
				} else if (
					typeof nextParent === 'object' &&
					nextParent !== null &&
					'id' in nextParent
				) {
					currentParentId = (nextParent as any).id;
				} else {
					currentParentId = nextParent;
				}

				depth++;
			}

			return true;
		} catch (error) {
			console.error('Error validating parent reference:', error);
			return 'Error validating parent reference. Please try again.';
		}
	},
};
/* eslint-enable @typescript-eslint/no-explicit-any */
