import type { Tab } from 'payload';
import {
	seoOverview,
	seoMeta,
	seoMetaDescription,
	seoMetaImage,
	canonicalUrl,
	defaultFormat,
	duration,
	aspectRatio,
	videoFormatUrls,
	channelVideos,
	keyFrameImage,
	transcript,
	playlists,
	channelTitles,
} from '@/collections/Videos/fields';
import { entitlementsField } from '@/fields/entitlements';
import { createdBy } from '@/fields/createdBy';
import { updatedBy } from '@/fields/updatedBy';
import { adMetricFields } from '@/collections/Articles/fields/adMetric';

export const contentTab: Tab = {
	name: 'content',
	label: 'Content',
	fields: [
		videoFormatUrls,
		defaultFormat,
		duration,
		aspectRatio,
		channelVideos,
		keyFrameImage,
		transcript,
		playlists,
	],
};

export const seoTab: Tab = {
	name: 'seo',
	label: 'SEO',
	fields: [
		canonicalUrl,
		seoOverview,
		seoMeta,
		seoMetaDescription,
		seoMetaImage,
	],
};

export const coreMetadataTab: Tab = {
	name: 'coreMetadata',
	label: 'Core Metadata',
	fields: [entitlementsField, ...adMetricFields(), createdBy, updatedBy],
};

export const channelOverridesTab: Tab = {
	name: 'channelOverrides',
	label: 'Channel Overrides',
	fields: [channelTitles],
};
