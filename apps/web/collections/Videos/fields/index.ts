import { TextField, RichTextField, SelectField } from 'payload';
import type { <PERSON>rrayField, Field, TextFieldSingleValidation } from 'payload';
import {
	MetaDescriptionField,
	MetaImageField,
	MetaTitleField,
	OverviewField,
} from '@payloadcms/plugin-seo/fields';
import {
	BlocksFeature,
	FixedToolbarFeature,
	HeadingFeature,
	HorizontalRuleFeature,
	LinkFeature,
	InlineToolbarFeature,
	lexicalEditor,
	type LinkFields,
	OrderedListFeature,
} from '@payloadcms/richtext-lexical';
import { allowedBlocks } from '@/collections/Articles/fields/blocks';
import { isValidUrl } from '@/utils/isValidUrl';
import { type Video } from '@/payload-types';

// Title field (renamed from headline)
export const title: TextField = {
	name: 'title',
	label: 'Title',
	localized: true,
	type: 'text',
	required: true,
};

// Description field (new rich text field)
export const description: RichTextField = {
	name: 'description',
	label: 'Description',
	localized: true,
	type: 'richText',
	editor: lexicalEditor({
		features: ({ rootFeatures }) => {
			return [
				...rootFeatures,
				HeadingFeature({
					enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'],
				}),
				BlocksFeature({
					blocks: allowedBlocks ?? [],
				}),
				FixedToolbarFeature(),
				InlineToolbarFeature(),
				OrderedListFeature(),
				HorizontalRuleFeature(),
				LinkFeature({
					enabledCollections: ['pages', 'articles'],
					fields: ({ defaultFields }) => {
						const defaultFieldsWithoutUrl = defaultFields.filter((field) => {
							if ('name' in field && field.name === 'url') return false;
							return true;
						});

						return [
							...defaultFieldsWithoutUrl,
							{
								name: 'url',
								type: 'text',
								admin: {
									condition: (_data, siblingData) =>
										siblingData?.linkType !== 'internal',
								},
								label: ({ t }) => t('fields:enterURL'),
								required: true,
								validate: ((value, options) => {
									if (
										(options?.siblingData as LinkFields)?.linkType ===
										'internal'
									) {
										return true; // no validation needed, as no url should exist for internal links
									}
									return value ? true : 'URL is required';
								}) as TextFieldSingleValidation,
							},
						];
					},
				}),
			];
		},
	}),
	required: false,
};

// Default Format field (replacing videoUrl)
export const defaultFormat: Field = {
	name: 'defaultFormat',
	label: 'Default Format',
	type: 'group',
	admin: {
		description: 'Select the default video format to use',
		condition: (data) => {
			// Only show this field if videoFormatUrls has at least one entry
			return (
				data?.content?.videoFormatUrls &&
				data.content.videoFormatUrls?.length > 0
			);
		},
	},
	fields: [
		{
			name: 'format',
			label: 'Format',
			type: 'select',
			required: true,
			options: [
				{
					label: 'M3U8',
					value: 'm3u8',
				},
				{
					label: 'MP4',
					value: 'mp4',
				},
			],
		},
		{
			name: 'url',
			label: 'URL',
			type: 'text',
			admin: {
				readOnly: true,
				hidden: true,
				description:
					'This field is populated automatically based on the selected format',
			},
		},
	],
};

// Duration field
export const duration: TextField = {
	name: 'duration',
	label: 'Duration',
	type: 'text',
	required: false,
	admin: {
		description:
			'Enter the duration in the format xx:xx:xx (hours:minutes:seconds)',
	},
	validate: (value: string | undefined | null) => {
		if (!value) return true; // Optional field
		const durationRegex = /^(\d{1,2}:)?[0-5]?\d:[0-5]\d$/;
		if (durationRegex.test(value)) return true;
		return 'Please enter a valid duration in the format xx:xx:xx (hours:minutes:seconds)';
	},
};

// Aspect Ratio field
export const aspectRatio: SelectField = {
	name: 'aspectRatio',
	label: 'Aspect Ratio',
	type: 'select',
	options: [
		{
			label: '- None -',
			value: 'none',
		},
		{
			label: 'Horizontal',
			value: 'horizontal',
		},
		{
			label: 'Vertical',
			value: 'vertical',
		},
	],
	defaultValue: 'none',
	required: false,
};

// Video Format URLs field
export const videoFormatUrls: ArrayField = {
	name: 'videoFormatUrls',
	label: 'Video Format URLs',
	type: 'array',
	required: true, // Make it required
	admin: {
		description: 'Add URLs for different video formats (at least one required)',
	},
	validate: (value) => {
		// Require at least one entry
		if (!value || value?.length === 0)
			return 'At least one video format URL is required';

		// Check for duplicate formats
		type VideoFormatUrl = Video['content']['videoFormatUrls'][number];
		const formats = (value as VideoFormatUrl[]).map((item) => item?.format);
		const uniqueFormats = new Set(formats);
		if (formats.length !== uniqueFormats.size) {
			return `${formats.join(', ')} Duplicate formats are not allowed. You can only have one entry per format type.`;
		}

		return true;
	},
	fields: [
		{
			name: 'format',
			label: 'Format',
			type: 'select',
			options: [
				{
					label: 'M3U8',
					value: 'm3u8',
				},
				{
					label: 'MP4',
					value: 'mp4',
				},
			],
			required: true,
		},
		{
			name: 'url',
			label: 'URL',
			type: 'text',
			required: true,
			validate: (value: string | undefined | null) => {
				if (!value) return 'URL is required';
				if (isValidUrl(value)) return true;
				return 'Please enter a valid URL including the protocol (e.g., https://example.com/video.mp4)';
			},
		},
	],
};

// Channel Videos field
export const channelVideos: ArrayField = {
	name: 'channelVideos',
	label: 'Channel Videos',
	type: 'array',
	admin: {
		description: 'Channel-specific video format URLs',
	},
	fields: [
		{
			name: 'channel',
			label: 'Channel',
			type: 'relationship',
			relationTo: 'tags',
			required: true,
			hasMany: false,
			filterOptions: {
				type: {
					equals: 'channel',
				},
			},
		},
		{
			name: 'format',
			label: 'Format',
			type: 'select',
			required: true,
			options: [
				{
					label: 'M3U8',
					value: 'm3u8',
				},
				{
					label: 'MP4',
					value: 'mp4',
				},
			],
		},
		{
			name: 'url',
			label: 'URL',
			type: 'text',
			admin: {
				readOnly: true,
				hidden: true,
				description:
					'This field is populated automatically based on the selected format',
			},
		},
	],
};

// Key Frame Image field
export const keyFrameImage: TextField = {
	name: 'keyFrameImage',
	label: 'Key Frame Image',
	type: 'text',
	required: false,
	admin: {
		description: 'URL for the key frame image',
	},
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	validate: ((value, options) => {
		if (!value) return true; // Optional field
		if (isValidUrl(value)) return true;
		return 'Please enter a valid URL including the protocol (e.g., https://example.com/image.jpg)';
	}) as TextFieldSingleValidation,
};

// Transcript field
export const transcript: RichTextField = {
	name: 'transcript',
	label: 'Transcript',
	localized: true,
	type: 'richText',
	editor: lexicalEditor({
		features: ({ rootFeatures }) => {
			return [
				...rootFeatures,
				HeadingFeature({
					enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'],
				}),
				BlocksFeature({
					blocks: allowedBlocks ?? [],
				}),
				FixedToolbarFeature(),
				InlineToolbarFeature(),
				OrderedListFeature(),
				HorizontalRuleFeature(),
				LinkFeature({
					enabledCollections: ['pages', 'articles'],
					fields: ({ defaultFields }) => {
						const defaultFieldsWithoutUrl = defaultFields.filter((field) => {
							if ('name' in field && field.name === 'url') return false;
							return true;
						});

						return [
							...defaultFieldsWithoutUrl,
							{
								name: 'url',
								type: 'text',
								admin: {
									condition: (_data, siblingData) =>
										siblingData?.linkType !== 'internal',
								},
								label: ({ t }) => t('fields:enterURL'),
								required: true,
								validate: ((value, options) => {
									if (
										(options?.siblingData as LinkFields)?.linkType ===
										'internal'
									) {
										return true; // no validation needed, as no url should exist for internal links
									}
									return value ? true : 'URL is required';
								}) as TextFieldSingleValidation,
							},
						];
					},
				}),
			];
		},
	}),
	required: false,
};

// Playlists field
export const playlists: ArrayField = {
	name: 'playlists',
	label: 'Playlists',
	type: 'array',
	admin: {
		description: 'Add content queries as playlists',
	},
	fields: [
		{
			name: 'contentQuery',
			label: 'Content Query',
			type: 'relationship',
			relationTo: 'content-queries',
			required: true,
			hasMany: false,
		},
	],
};

// Channel Titles field (renamed from channelHeadlines)
export const channelTitles: ArrayField = {
	name: 'channelTitles',
	label: 'Channel Titles',
	type: 'array',
	admin: {
		description: 'Channel titles override the standard video title',
	},
	fields: [
		{
			name: 'channel',
			label: 'Channel',
			type: 'relationship',
			relationTo: 'tags',
			required: true,
			hasMany: false,
			filterOptions: {
				type: {
					equals: 'channel',
				},
			},
		},
		{
			name: 'title',
			label: 'Title',
			type: 'text',
			required: true,
			localized: true,
		},
	],
};

// SEO fields (same as Articles)
export const canonicalUrl: TextField = {
	name: 'canonicalUrl',
	label: 'Canonical URL',
	type: 'text',
	admin: {
		description:
			'Used by partners to point to their external URL (e.g. https://example.com)',
	},
	validate: (value: string | undefined | null) => {
		if (!value) return true; // Field is optional
		if (isValidUrl(value)) return true;

		return 'Please enter a valid URL including the protocol (e.g., https://example.com)';
	},
};

export const seoOverview = OverviewField({
	titlePath: 'meta.title',
	descriptionPath: 'meta.description',
	imagePath: 'meta.image',
});

export const seoMeta = MetaTitleField({
	hasGenerateFn: true,
});

export const seoMetaImage = MetaImageField({
	relationTo: 'images',
});

export const seoMetaDescription = MetaDescriptionField({});

// External Authors field (same as Articles)
export const externalAuthors: <AUTHORS>
	name: 'externalAuthors',
	label: 'External Authors',
	type: 'text',
	required: false,
};
