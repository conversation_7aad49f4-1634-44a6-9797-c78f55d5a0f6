import type { CollectionConfig } from 'payload';

import { slugField } from '@/fields/slug';
import { publishDateField } from '@/fields/publishDate';
import { categoryTag, defaultContentTags, topicTag } from '@/fields/tags';
import { authors } from '@/fields/authors';
import { revalidateArticle } from '@/collections/Articles/hooks/revalidateArticle';

import {
	title,
	description,
	externalAuthors,
} from '@/collections/Videos/fields';
import {
	seoTab,
	contentTab,
	coreMetadataTab,
	channelOverridesTab,
} from '@/collections/Videos/fields/tabs';
import { createTabs, createCollapsible } from '@/fields/utility';
import { setCreatedBy } from '@/fields/createdBy/hooks/setCreatedBy';
import { setUpdatedBy } from '@/fields/updatedBy/hooks/setUpdatedBy';
import { populateVideoFormats } from '@/collections/Videos/hooks/populateVideoFormats';
import { generatePreviewPath } from '@/utils/generatePreviewPath';

const videoTabs = [contentTab, seoTab, coreMetadataTab, channelOverridesTab];

export const Videos: CollectionConfig = {
	slug: 'videos',
	access: {
		read: () => true,
	},
	defaultPopulate: {
		authors: true,
		title: true,
		category: true,
		topic: true,
		publishDate: true,
		updatedAt: true,
		createdBy: true,
		seo: {
			title: true,
			description: true,
		},
	},
	admin: {
		useAsTitle: 'title',
		defaultColumns: ['title', 'slug', 'status', 'publishDate', 'updatedAt'],
		livePreview: {
			url: ({ data, locale }) =>
				generatePreviewPath({
					data,
					locale: locale.code,
					collection: 'videos',
					isLivePreview: true,
				}),
		},
		preview: (data, { locale }: { locale: string }) =>
			generatePreviewPath({ data, locale, collection: 'videos' }),
	},
	fields: [
		createCollapsible(
			[title, description, categoryTag, topicTag, authors, externalAuthors],
			'Header',
			{
				admin: { initCollapsed: false },
			},
		),
		createTabs(videoTabs),
		defaultContentTags,
		...publishDateField(),
		...slugField(),
	],
	hooks: {
		afterChange: [revalidateArticle], // Reusing the article revalidation hook
		beforeChange: [setCreatedBy, setUpdatedBy, populateVideoFormats],
	},
	versions: {
		drafts: {
			autosave: {
				interval: 1000, // We set this interval for optimal live preview
			},
			schedulePublish: true,
		},
		maxPerDoc: 50,
	},
};
