import { describe, it, expect } from 'vitest';
import type { Location } from '@/payload-types';
import { generateCanonicalIDHook, generateCanonicalID } from './index';

// Mock context for hook calls
const mockContext = {
	req: {
		locale: 'en-US',
	} as any,
	collection: {
		slug: 'locations',
	} as any,
};

describe('Location hooks', () => {
	describe('generateCanonicalID', () => {
		describe('basic functionality', () => {
			it('should generate canonical ID with all components', () => {
				const data: Partial<Location> = {
					displayName: 'New York City',
					countryCode: 'US',
					adminDistrictCode: 'NY',
					locationType: 'poi',
					city: 'New York',
				};

				const result = generateCanonicalID(data, 'en');
				expect(result).toBe('en/us/ny/poi/new-york/new-york-city');
			});

			it('should handle missing optional fields', () => {
				const data: Partial<Location> = {
					displayName: 'Paris',
				};

				const result = generateCanonicalID(data, 'fr');
				expect(result).toBe('fr/paris/paris');
			});

			it('should use adminDistrict when adminDistrictCode is missing', () => {
				const data: Partial<Location> = {
					displayName: 'Los Angeles',
					countryCode: 'US',
					adminDistrict: 'California',
					locationType: 'poi',
					city: 'Los Angeles',
				};

				const result = generateCanonicalID(data, 'en');
				expect(result).toBe('en/us/california/poi/los-angeles/los-angeles');
			});

			it('should use displayName as city when city is missing', () => {
				const data: Partial<Location> = {
					displayName: 'Tokyo',
					countryCode: 'JP',
					adminDistrictCode: 'TK',
					locationType: 'poi',
				};

				const result = generateCanonicalID(data, 'ja');
				expect(result).toBe('ja/jp/tk/poi/tokyo/tokyo');
			});
		});

		describe('edge cases', () => {
			it('should handle empty strings', () => {
				const data: Partial<Location> = {
					displayName: '',
					countryCode: '',
					adminDistrictCode: '',
					locationType: undefined,
					city: '',
				};

				const result = generateCanonicalID(data, 'en');
				expect(result).toBe('en');
			});

			it('should handle undefined values', () => {
				const data: Partial<Location> = {
					displayName: undefined,
					countryCode: undefined,
					adminDistrictCode: undefined,
					locationType: undefined,
					city: undefined,
				};

				const result = generateCanonicalID(data, 'en');
				expect(result).toBe('en');
			});

			it('should handle null values', () => {
				const data: Partial<Location> = {
					displayName: null as any,
					countryCode: null as any,
					adminDistrictCode: null as any,
					locationType: null as any,
					city: null as any,
				};

				const result = generateCanonicalID(data, 'en');
				expect(result).toBe('en');
			});

			it('should handle special characters in names', () => {
				const data: Partial<Location> = {
					displayName: 'São Paulo',
					countryCode: 'BR',
					adminDistrictCode: 'SP',
					locationType: 'poi',
					city: 'São Paulo',
				};

				const result = generateCanonicalID(data, 'pt');
				expect(result).toBe('pt/br/sp/poi/so-paulo/so-paulo');
			});

			it('should handle spaces and special characters', () => {
				const data: Partial<Location> = {
					displayName: 'New York City!',
					countryCode: 'U.S.',
					adminDistrictCode: 'N.Y.',
					locationType: 'poi' as const,
					city: 'New York',
				};

				const result = generateCanonicalID(data, 'en');
				expect(result).toBe('en/us/ny/poi/new-york/new-york-city');
			});
		});

		describe('locale handling', () => {
			it('should extract first two letters from locale', () => {
				const data: Partial<Location> = {
					displayName: 'London',
				};

				const result = generateCanonicalID(data, 'en');
				expect(result).toBe('en/london/london');
			});

			it('should handle empty locale', () => {
				const data: Partial<Location> = {
					displayName: 'Berlin',
				};

				const result = generateCanonicalID(data, '');
				expect(result).toBe('en/berlin/berlin');
			});

			it('should handle single character locale', () => {
				const data: Partial<Location> = {
					displayName: 'Rome',
				};

				const result = generateCanonicalID(data, 'i');
				expect(result).toBe('i/rome/rome');
			});
		});

		describe('real data examples', () => {
			const realDataLocations = [
				{
					name: 'Canada Olympic Park bobsleigh, luge, and skeleton track',
					data: {
						displayName:
							'Canada Olympic Park bobsleigh, luge, and skeleton track',
						city: 'Calgary',
						adminDistrictCode: 'AB',
						adminDistrict: 'Alberta',
						countryCode: 'CA',
						locationType: 'poi' as const,
						locationData: {
							poi: {
								poiType: 'sports venue' as const,
							},
						},
					},
					locale: 'en',
					expected:
						'en/ca/ab/poi/calgary/canada-olympic-park-bobsleigh-luge-and-skeleton-track',
				},
				{
					name: 'Parc de la Terrasse-du-Bon-Air',
					data: {
						displayName: 'Parc de la Terrasse-du-Bon-Air',
						city: 'Quebec City',
						adminDistrictCode: 'QC',
						adminDistrict: 'Quebec',
						countryCode: 'CA',
						locationType: 'poi' as const,
						locationData: {
							poi: {
								poiType: 'urban park' as const,
							},
						},
					},
					locale: 'en',
					expected: 'en/ca/qc/poi/quebec-city/parc-de-la-terrasse-du-bon-air',
				},
				{
					name: 'Hartsfield-Jackson Atlanta Intl Airport',
					data: {
						displayName: 'Hartsfield-Jackson Atlanta Intl Airport',
						city: 'Atlanta',
						adminDistrictCode: 'GA',
						adminDistrict: 'Georgia',
						countryCode: 'US',
						locationType: 'airport' as const,
					},
					locale: 'en',
					expected:
						'en/us/ga/airport/atlanta/hartsfield-jackson-atlanta-intl-airport',
				},
				{
					name: 'H. L. "Hub" Hollis Field',
					data: {
						displayName: 'H. L. "Hub" Hollis Field',
						city: 'Paris',
						adminDistrictCode: 'TX',
						adminDistrict: 'Texas',
						countryCode: 'US',
						locationType: 'poi' as const,
						locationData: {
							poi: {
								poiType: 'stadium' as const,
							},
						},
					},
					locale: 'en',
					expected: 'en/us/tx/poi/paris/h-l-hub-hollis-field',
				},
			];

			it.each(realDataLocations)(
				'should generate correct canonical ID for $name',
				({ data, locale, expected }) => {
					const result = generateCanonicalID(data, locale);
					expect(result).toBe(expected);
				},
			);

			// Test with locale codes that need transformation
			it('should handle locale transformation with real data', () => {
				const data = {
					displayName:
						'Canada Olympic Park bobsleigh, luge, and skeleton track',
					city: 'Calgary',
					adminDistrictCode: 'AB',
					adminDistrict: 'Alberta',
					countryCode: 'CA',
					locationType: 'poi' as const,
				};

				// Test with en-US locale (should transform to 'en')
				const result = generateCanonicalID(data, 'en-US');
				expect(result).toBe(
					'en/ca/ab/poi/calgary/canada-olympic-park-bobsleigh-luge-and-skeleton-track',
				);
			});

			it('should handle POI type variations with real data', () => {
				const poiData = {
					displayName: 'Parc de la Terrasse-du-Bon-Air',
					city: 'Quebec City',
					adminDistrictCode: 'QC',
					countryCode: 'CA',
					locationType: 'poi' as const,
					locationData: {
						poi: {
							poiType: 'urban park' as const,
						},
					},
				};

				const result = generateCanonicalID(poiData, 'en');
				expect(result).toBe(
					'en/ca/qc/poi/quebec-city/parc-de-la-terrasse-du-bon-air',
				);
			});
		});

		describe('locale transformation', () => {
			it('should transform en-US to en', () => {
				const data: Partial<Location> = {
					displayName: 'Kangiqsualujjuaq (Georges River) Airport',
					city: 'Kangiqsualujjuaq',
					adminDistrictCode: 'QC',
					adminDistrict: 'Quebec / Quebec',
					countryCode: 'CA',
					locationType: 'airport' as const,
				};

				const result = generateCanonicalID(data, 'en-US');
				expect(result).toBe(
					'en/ca/qc/airport/kangiqsualujjuaq/kangiqsualujjuaq-georges-river-airport',
				);
			});

			it('should handle other locale formats', () => {
				const data: Partial<Location> = {
					displayName:
						'Canada Olympic Park bobsleigh, luge, and skeleton track',
					city: 'Calgary',
					adminDistrictCode: 'AB',
					countryCode: 'CA',
					locationType: 'poi' as const,
				};

				const result = generateCanonicalID(data, 'en-CA');
				expect(result).toBe(
					'en/ca/ab/poi/calgary/canada-olympic-park-bobsleigh-luge-and-skeleton-track',
				);
			});
		});

		describe('location type variations', () => {
			it('should handle different location types correctly', () => {
				const locationTypes = [
					{ type: 'airport', expected: 'airport' },
					{ type: 'poi', expected: 'poi' },
				];

				locationTypes.forEach(({ type, expected }) => {
					const data: Partial<Location> = {
						displayName: 'Test Location',
						countryCode: 'US',
						adminDistrictCode: 'CA',
						locationType: type as any,
						city: 'Test City',
					};

					const result = generateCanonicalID(data, 'en');
					expect(result).toBe(`en/us/ca/${expected}/test-city/test-location`);
				});
			});

			it('should handle POI with different poi types', () => {
				const poiTypes = [
					'stadium',
					'sports venue',
					'urban park',
					'amusement park',
					'beach',
					'campground',
				];

				poiTypes.forEach((poiType) => {
					const data: Partial<Location> = {
						displayName: 'Test Location',
						countryCode: 'US',
						adminDistrictCode: 'CA',
						locationType: 'poi',
						city: 'Test City',
						locationData: {
							poi: {
								poiType: poiType as any,
							},
						},
					};

					const result = generateCanonicalID(data, 'en');
					expect(result).toBe('en/us/ca/poi/test-city/test-location');
				});
			});

			it('should handle missing location type', () => {
				const data: Partial<Location> = {
					displayName: 'Test Location',
					countryCode: 'US',
					adminDistrictCode: 'CA',
					city: 'Test City',
					// locationType is undefined
				};

				const result = generateCanonicalID(data, 'en');
				expect(result).toBe('en/us/ca/test-city/test-location');
			});
		});

		describe('special character handling in real data', () => {
			it('should handle quotes and special characters in display names', () => {
				const data: Partial<Location> = {
					displayName: 'H. L. "Hub" Hollis Field',
					countryCode: 'US',
					adminDistrictCode: 'TX',
					locationType: 'poi',
					city: 'Paris',
					locationData: {
						poi: {
							poiType: 'stadium',
						},
					},
				};

				const result = generateCanonicalID(data, 'en');
				expect(result).toBe('en/us/tx/poi/paris/h-l-hub-hollis-field');
			});

			it('should handle hyphens and accents in display names', () => {
				const data: Partial<Location> = {
					displayName: 'Parc de la Terrasse-du-Bon-Air',
					countryCode: 'CA',
					adminDistrictCode: 'QC',
					locationType: 'poi',
					city: 'Quebec City',
					locationData: {
						poi: {
							poiType: 'urban park',
						},
					},
				};

				const result = generateCanonicalID(data, 'en');
				expect(result).toBe(
					'en/ca/qc/poi/quebec-city/parc-de-la-terrasse-du-bon-air',
				);
			});

			it('should handle long descriptive names', () => {
				const data: Partial<Location> = {
					displayName:
						'Canada Olympic Park bobsleigh, luge, and skeleton track',
					countryCode: 'CA',
					adminDistrictCode: 'AB',
					locationType: 'poi',
					city: 'Calgary',
					locationData: {
						poi: {
							poiType: 'sports venue',
						},
					},
				};

				const result = generateCanonicalID(data, 'en');
				expect(result).toBe(
					'en/ca/ab/poi/calgary/canada-olympic-park-bobsleigh-luge-and-skeleton-track',
				);
			});
		});
	});

	describe('generateCanonicalIDHook', () => {
		describe('hook execution conditions', () => {
			it('should execute for create operation on locations collection', async () => {
				const data: Partial<Location> = {
					displayName: 'Kangiqsualujjuaq (Georges River) Airport',
					city: 'Kangiqsualujjuaq',
					adminDistrictCode: 'QC',
					countryCode: 'CA',
					locationType: 'airport',
				};

				const result = await generateCanonicalIDHook({
					data,
					context: mockContext,
					operation: 'create',
					collection: mockContext.collection,
					req: mockContext.req,
				});

				expect(result.canonicalID).toBe(
					'en/ca/qc/airport/kangiqsualujjuaq/kangiqsualujjuaq-georges-river-airport',
				);
			});

			it('should execute for update operation on locations collection', async () => {
				const data: Partial<Location> = {
					displayName:
						'Canada Olympic Park bobsleigh, luge, and skeleton track',
					city: 'Calgary',
					adminDistrictCode: 'AB',
					countryCode: 'CA',
					locationType: 'poi',
				};

				const result = await generateCanonicalIDHook({
					data,
					context: mockContext,
					operation: 'update',
					collection: mockContext.collection,
					req: mockContext.req,
				});

				expect(result.canonicalID).toBe(
					'en/ca/ab/poi/calgary/canada-olympic-park-bobsleigh-luge-and-skeleton-track',
				);
			});

			it('should not execute for wrong collection', async () => {
				const data: Partial<Location> = {
					displayName: 'Hartsfield-Jackson Atlanta Intl Airport',
					city: 'Atlanta',
					adminDistrictCode: 'GA',
					countryCode: 'US',
					locationType: 'airport',
				};

				const wrongCollection = {
					slug: 'articles',
				} as any;

				const result = await generateCanonicalIDHook({
					data,
					context: mockContext,
					operation: 'create',
					collection: wrongCollection,
					req: mockContext.req,
				});

				expect(result).toBe(data);
				expect(result.canonicalID).toBeUndefined();
			});
		});

		describe('displayName requirement', () => {
			it('should generate canonicalID when displayName is present', async () => {
				const data: Partial<Location> = {
					displayName: 'H. L. "Hub" Hollis Field',
					city: 'Paris',
					adminDistrictCode: 'TX',
					countryCode: 'US',
					locationType: 'poi',
				};

				const result = await generateCanonicalIDHook({
					data,
					context: mockContext,
					operation: 'create',
					collection: mockContext.collection,
					req: mockContext.req,
				});

				expect(result.canonicalID).toBe(
					'en/us/tx/poi/paris/h-l-hub-hollis-field',
				);
			});

			it('should not generate canonicalID when displayName is missing', async () => {
				const data: Partial<Location> = {
					city: 'Calgary',
					adminDistrictCode: 'AB',
					countryCode: 'CA',
					locationType: 'poi',
				};

				const result = await generateCanonicalIDHook({
					data,
					context: mockContext,
					operation: 'create',
					collection: mockContext.collection,
					req: mockContext.req,
				});

				expect(result).toBe(data);
				expect(result.canonicalID).toBeUndefined();
			});

			it('should not generate canonicalID when displayName is empty', async () => {
				const data: Partial<Location> = {
					displayName: '',
					city: 'Quebec City',
					adminDistrictCode: 'QC',
					countryCode: 'CA',
					locationType: 'poi',
				};

				const result = await generateCanonicalIDHook({
					data,
					context: mockContext,
					operation: 'create',
					collection: mockContext.collection,
					req: mockContext.req,
				});

				expect(result).toBe(data);
				expect(result.canonicalID).toBeUndefined();
			});
		});

		describe('locale handling', () => {
			it('should use request locale and transform en-US to en', async () => {
				const data: Partial<Location> = {
					displayName: 'Parc de la Terrasse-du-Bon-Air',
					city: 'Quebec City',
					adminDistrictCode: 'QC',
					countryCode: 'CA',
					locationType: 'poi',
				};

				const result = await generateCanonicalIDHook({
					data,
					context: mockContext,
					operation: 'create',
					collection: mockContext.collection,
					req: mockContext.req,
				});

				expect(result.canonicalID).toBe(
					'en/ca/qc/poi/quebec-city/parc-de-la-terrasse-du-bon-air',
				);
			});

			it('should handle missing locale', async () => {
				const data: Partial<Location> = {
					displayName: 'Hartsfield-Jackson Atlanta Intl Airport',
					city: 'Atlanta',
					adminDistrictCode: 'GA',
					countryCode: 'US',
					locationType: 'airport',
				};

				const noLocaleContext = {
					...mockContext,
					req: {} as any,
				};

				const result = await generateCanonicalIDHook({
					data,
					context: noLocaleContext,
					operation: 'create',
					collection: mockContext.collection,
					req: noLocaleContext.req,
				});

				expect(result.canonicalID).toBe(
					'en/us/ga/airport/atlanta/hartsfield-jackson-atlanta-intl-airport',
				);
			});
		});

		describe('data preservation', () => {
			it('should preserve all original data', async () => {
				const data: Partial<Location> = {
					displayName:
						'Canada Olympic Park bobsleigh, luge, and skeleton track',
					city: 'Calgary',
					adminDistrictCode: 'AB',
					countryCode: 'CA',
					locationType: 'poi',
					locationData: {
						poi: {
							poiType: 'sports venue',
						},
					},
					existingField: 'should be preserved',
				} as any;

				const result = await generateCanonicalIDHook({
					data,
					context: mockContext,
					operation: 'create',
					collection: mockContext.collection,
					req: mockContext.req,
				});

				expect(result.displayName).toBe(
					'Canada Olympic Park bobsleigh, luge, and skeleton track',
				);
				expect(result.countryCode).toBe('CA');
				expect(result.adminDistrictCode).toBe('AB');
				expect(result.locationType).toBe('poi');
				expect(result.city).toBe('Calgary');
				expect(result.locationData?.poi?.poiType).toBe('sports venue');
				expect((result as any).existingField).toBe('should be preserved');
				expect(result.canonicalID).toBe(
					'en/ca/ab/poi/calgary/canada-olympic-park-bobsleigh-luge-and-skeleton-track',
				);
			});

			it('should overwrite existing canonicalID', async () => {
				const data: Partial<Location> = {
					displayName: 'Kangiqsualujjuaq (Georges River) Airport',
					city: 'Kangiqsualujjuaq',
					adminDistrictCode: 'QC',
					countryCode: 'CA',
					locationType: 'airport',
					canonicalID: 'old/canonical/id',
				};

				const result = await generateCanonicalIDHook({
					data,
					context: mockContext,
					operation: 'update',
					collection: mockContext.collection,
					req: mockContext.req,
				});

				expect(result.canonicalID).toBe(
					'en/ca/qc/airport/kangiqsualujjuaq/kangiqsualujjuaq-georges-river-airport',
				);
				expect(result.canonicalID).not.toBe('old/canonical/id');
			});
		});
	});
});
