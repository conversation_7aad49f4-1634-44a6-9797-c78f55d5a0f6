'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { useField } from '@payloadcms/ui';
import type { PointFieldClientComponent } from 'payload';

/* eslint-disable react/prop-types */

// Type for the point field value - can be either GeoJSON format or simple array
type PointValue =
	| {
			type: 'Point';
			coordinates: [number, number]; // [longitude, latitude]
	  }
	| [number, number]
	| null; // Simple array format: [longitude, latitude]

// Validation function for coordinates
const validateCoordinates = (lat: number, lng: number): string | null => {
	if (lat < -90 || lat > 90) {
		return 'Latitude must be between -90 and 90 degrees';
	}
	if (lng < -180 || lng > 180) {
		return 'Longitude must be between -180 and 180 degrees';
	}
	return null;
};

// Parse comma-separated coordinate string
const parseCoordinateString = (
	input: string,
): { lat: number; lng: number; error?: string } => {
	const trimmed = input.trim();
	if (!trimmed) {
		return { lat: 0, lng: 0, error: 'Coordinates are required' };
	}

	const parts = trimmed.split(',').map((part) => part.trim());
	if (parts.length !== 2) {
		return {
			lat: 0,
			lng: 0,
			error: 'Please enter coordinates as: latitude,longitude',
		};
	}

	const lat = parseFloat(parts[0] || '0');
	const lng = parseFloat(parts[1] || '0');

	if (isNaN(lat) || isNaN(lng)) {
		return { lat: 0, lng: 0, error: 'Please enter valid numeric coordinates' };
	}

	const validationError = validateCoordinates(lat, lng);
	if (validationError) {
		return { lat, lng, error: validationError };
	}

	return { lat, lng };
};

// Convert point value to display string
const pointToString = (point: PointValue): string => {
	if (!point) {
		return '';
	}

	let coordinates: [number, number] | undefined;

	// Handle GeoJSON format
	if (typeof point === 'object' && 'type' in point && 'coordinates' in point) {
		coordinates = point.coordinates;
	}
	// Handle simple array format
	else if (Array.isArray(point)) {
		coordinates = point;
	}

	if (!coordinates || !Array.isArray(coordinates) || coordinates.length !== 2) {
		return '';
	}

	const [lng, lat] = coordinates;
	if (typeof lng !== 'number' || typeof lat !== 'number') {
		return '';
	}
	return `${lat},${lng}`;
};

// Convert coordinate values to point object
const coordinatesToPoint = (lat: number, lng: number): PointValue => ({
	type: 'Point',
	coordinates: [lng, lat], // GeoJSON format: [longitude, latitude]
});

export const CoordinateInput: PointFieldClientComponent = (props) => {
	const { field, path, readOnly } = props;
	const { value, setValue } = useField<PointValue>({ path });
	const [inputValue, setInputValue] = useState('');
	const [error, setError] = useState<string | null>(null);
	const [touched, setTouched] = useState(false);

	// Initialize input value from field value
	useEffect(() => {
		try {
			if (value) {
				const displayValue = pointToString(value);
				setInputValue(displayValue);
			}
		} catch (err) {
			console.error('Error initializing coordinate input:', err);
			setInputValue('');
			setError('Error loading coordinate data');
		}
	}, [value]);

	// Handle input change
	const handleInputChange = useCallback(
		(e: React.ChangeEvent<HTMLInputElement>) => {
			const newValue = e.target.value;
			setInputValue(newValue);
			setTouched(true);

			if (!newValue.trim()) {
				setError(null);
				setValue(null);
				return;
			}

			const parsed = parseCoordinateString(newValue);
			if (parsed.error) {
				setError(parsed.error);
				return;
			}

			setError(null);
			const pointValue = coordinatesToPoint(parsed.lat, parsed.lng);
			setValue(pointValue);
		},
		[setValue],
	);

	// Handle blur to show validation errors
	const handleBlur = useCallback(() => {
		setTouched(true);
	}, []);

	const showError = touched && error;
	const fieldName = field?.name || 'coordinates';
	const fieldLabel =
		typeof field?.label === 'string' ? field.label : 'Coordinates';

	return (
		<div className="field-type point">
			<label className="field-label" htmlFor={fieldName}>
				{fieldLabel}
				{field?.required && <span className="required">*</span>}
			</label>

			<div className="input-wrapper">
				<input
					id={fieldName}
					name={fieldName}
					type="text"
					value={inputValue}
					onChange={handleInputChange}
					onBlur={handleBlur}
					placeholder="33.8203,-85.7664 (latitude,longitude)"
					disabled={readOnly}
					className={`field-input ${showError ? 'error' : ''}`}
					autoComplete="off"
				/>
			</div>

			{showError && <div className="field-error">{error}</div>}

			{field?.admin?.description && (
				<div className="field-description">
					{typeof field.admin.description === 'string'
						? field.admin.description
						: 'Enter coordinates as latitude,longitude (decimal degrees)'}
				</div>
			)}

			{value &&
				!error &&
				(() => {
					let coordinates: [number, number] | undefined;

					// Handle GeoJSON format
					if (
						typeof value === 'object' &&
						'type' in value &&
						'coordinates' in value
					) {
						coordinates = value.coordinates;
					}
					// Handle simple array format
					else if (Array.isArray(value)) {
						coordinates = value;
					}

					return coordinates && coordinates.length === 2 ? (
						<div className="field-description">
							<small>
								Stored as: [{coordinates[1]}, {coordinates[0]}] (lat, lng)
							</small>
						</div>
					) : null;
				})()}
		</div>
	);
};

/* eslint-enable react/prop-types */

export default CoordinateInput;
