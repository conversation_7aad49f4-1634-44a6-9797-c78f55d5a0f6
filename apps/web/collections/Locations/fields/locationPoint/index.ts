import { PointField } from 'payload';

type Overrides = {
	locationPointOverrides?: Partial<PointField>;
};

// Type for point value - can be either GeoJSON format or simple array
type PointValue =
	| {
			type: 'Point';
			coordinates: [number, number];
	  }
	| [number, number];

// Type guard for point value
const isPointValue = (value: unknown): value is PointValue => {
	// Handle simple array format [lng, lat]
	if (Array.isArray(value) && value.length === 2) {
		return typeof value[0] === 'number' && typeof value[1] === 'number';
	}

	// Handle GeoJSON format
	if (typeof value === 'object' && value !== null) {
		const obj = value as Record<string, unknown>;
		return (
			'type' in obj &&
			'coordinates' in obj &&
			obj.type === 'Point' &&
			Array.isArray(obj.coordinates) &&
			obj.coordinates.length === 2
		);
	}

	return false;
};

// Validation function for point field
const validatePoint = (value: unknown) => {
	if (!value) {
		return 'Location point is required';
	}

	if (!isPointValue(value)) {
		return 'Invalid location point format';
	}

	let coordinates: [number, number];

	// Handle simple array format
	if (Array.isArray(value)) {
		coordinates = value;
	}
	// Handle GeoJSON format
	else {
		coordinates = (value as { coordinates: [number, number] }).coordinates;
	}

	const [lng, lat] = coordinates;

	if (typeof lng !== 'number' || typeof lat !== 'number') {
		return 'Coordinates must be numbers';
	}

	if (isNaN(lng) || isNaN(lat)) {
		return 'Coordinates must be valid numbers';
	}

	if (lat < -90 || lat > 90) {
		return 'Latitude must be between -90 and 90 degrees';
	}

	if (lng < -180 || lng > 180) {
		return 'Longitude must be between -180 and 180 degrees';
	}

	return true;
};

export const locationPointField = (overrides: Overrides = {}): PointField => {
	const { locationPointOverrides } = overrides;

	const locationPointField = {
		name: 'locationPoint',
		type: 'point',
		label: 'Location Point',
		required: true,
		validate: validatePoint,
		admin: {
			description: 'Geographic coordinates (latitude, longitude)',
			...(locationPointOverrides?.admin || {}),
			components: {
				Field: {
					path: '@/collections/Locations/fields/locationPoint/components/CoordinateInput',
					exportName: 'CoordinateInput',
				},
			},
		},
		...(locationPointOverrides || {}),
	} as PointField;

	return locationPointField;
};
