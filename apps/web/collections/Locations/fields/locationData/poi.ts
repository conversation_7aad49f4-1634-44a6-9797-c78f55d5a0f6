import { createGroup } from '@/fields/utility';
import type { SelectField } from 'payload';

const poiType: SelectField = {
	name: 'poiType',
	type: 'select',
	label: 'POI Type',
	options: [
		{ label: 'Amusement Park', value: 'amusement park' },
		{ label: 'Beach', value: 'beach' },
		{ label: 'Campground', value: 'campground' },
		{ label: 'Fair Ground', value: 'fair ground' },
		{ label: 'Hiking Trail', value: 'hiking trail' },
		{ label: 'Motorsport Racing Track', value: 'motorsport racing track' },
		{ label: 'National Park', value: 'national park' },
		{ label: 'Ski', value: 'ski' },
		{ label: 'Sports Venue', value: 'sports venue' },
		{ label: 'Stadium', value: 'stadium' },
		{ label: 'State Park', value: 'state park' },
		{ label: 'Theme Park', value: 'theme park' },
		{ label: 'Urban Park', value: 'urban park' },
	],
};

export const poi = createGroup([poiType], 'poi', 'POI', {
	admin: {
		condition: (data, _siblingData) => data.locationType === 'poi',
	},
});
