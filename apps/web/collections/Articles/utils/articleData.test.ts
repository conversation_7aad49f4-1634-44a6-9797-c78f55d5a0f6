import { describe, it, expect, vi } from 'vitest';
import { getArticlePageDebugData, getArticleDetails } from './articleData';
import { Article, VideoBlockConfig } from '@/payload-types';

// Mock modules
vi.mock('@/utils/getURL', () => ({
	getServerSideURL: vi.fn().mockReturnValue('https://weather.com'),
}));

// We'll mock getArticleDetails only for the buildArticlePageMetadata test

describe('articleData', () => {
	describe('getArticlePageDebugData', () => {
		it('should extract debug data from an article', () => {
			const mockArticle = {
				id: 'article-123',
				title: 'Test Article',
				seo: {
					title: 'Test Article',
					description: 'Test description',
				},
				assetName: '/test-article',
				_status: 'published',
				createdAt: '2025-04-25T10:00:00Z',
				updatedAt: '2025-04-25T11:00:00Z',
				publishDate: '2025-04-25T12:00:00Z',
				tenant: 'tenant-123',
			} as Article;

			const result = getArticlePageDebugData(mockArticle);

			expect(result).toEqual({
				title: 'Test Article',
				description: 'Test description',
				assetName: '/test-article',
				id: 'article-123',
				status: 'published',
				collection: 'pages',
				ogTitle: 'Test Article',
				ogDescription: 'Test description',
				ogImage: undefined,
				createdAt: '2025-04-25T10:00:00Z',
				updatedAt: '2025-04-25T11:00:00Z',
				publishedAt: '2025-04-25T12:00:00Z',
				tenant: 'tenant-123',
			});
		});

		it('should handle missing optional fields', () => {
			const mockArticle = {
				id: 'article-123',
				title: 'Test Article',
				seo: {},
				createdAt: '2025-04-25T10:00:00Z',
				updatedAt: '2025-04-25T11:00:00Z',
			} as Article;

			const result = getArticlePageDebugData(mockArticle);

			expect(result).toEqual({
				title: 'Test Article',
				description: '',
				assetName: '',
				id: 'article-123',
				ogTitle: '',
				ogDescription: '',
				ogImage: undefined,
				status: 'published',
				collection: 'pages',
				createdAt: '2025-04-25T10:00:00Z',
				updatedAt: '2025-04-25T11:00:00Z',
				publishedAt: undefined,
				tenant: undefined,
			});
		});
	});

	describe('getArticleDetails', () => {
		it('should extract article details with all fields populated', () => {
			const mockArticle = {
				title: 'Test Article',
				seo: {
					title: 'SEO Title',
					description: 'SEO Description',
				},
				assetName: '/test-article',
				publishDate: '2025-04-25T12:00:00Z',
				createdAt: '2025-04-25T10:00:00Z',
				updatedAt: '2025-04-25T14:00:00Z',
				authors: [
					{
						authorData: {
							firstName: 'John',
							lastName: 'Doe',
						},
					},
					{
						authorData: {
							firstName: 'Jane',
							lastName: 'Smith',
						},
					},
				],
				category: {
					value: {
						name: 'Weather',
					},
				},
				topic: {
					value: {
						name: 'Forecasts',
					},
				},
				tags: [{ name: 'storm' }, { name: 'hurricane' }],
				featuredImage: {
					url: 'https://example.com/image.jpg',
					seo: {
						altText: 'Weather image',
					},
				},
				content: {
					body: {
						root: {
							children: [],
							type: 'root',
							direction: null,
							format: '',
							indent: 0,
							version: 1,
						},
					},
				},
			} as unknown as Article;

			const result = getArticleDetails(mockArticle);

			expect(result).toEqual({
				title: 'Test Article',
				seoTitle: 'SEO Title',
				seoDescription: 'SEO Description',
				canonicalUrl: '',
				url: 'https://weather.com/test-article',
				images: [
					{
						url: 'https://example.com/image.jpg',
						alt: 'Weather image',
					},
				],
				createdAt: '2025-04-25T10:00:00Z',
				datePublished: '2025-04-25T12:00:00Z',
				dateModified: '2025-04-25T14:00:00Z',
				authors: ['John Doe', 'Jane Smith'],
				tagNames: ['storm', 'hurricane'],
				topicName: 'Forecasts',
				keywords: ['Weather', 'Forecasts'],
				video: undefined,
			});
		});

		it('should handle string authors', () => {
			const mockArticle = {
				title: 'Test Article',
				seo: {},
				assetName: '/test-article',
				createdAt: '2025-04-25T10:00:00Z',
				updatedAt: '2025-04-25T14:00:00Z',
				authors: ['John Doe', 'Jane Smith'],
				category: {
					value: 'Weather',
				},
				topic: {
					value: 'Forecasts',
				},
				tags: ['storm', 'hurricane'],
				featuredImage: 'https://example.com/image.jpg',
				content: {
					body: {
						root: {
							children: [],
							type: 'root',
							direction: null,
							format: '',
							indent: 0,
							version: 1,
						},
					},
				},
			} as unknown as Article;

			const result = getArticleDetails(mockArticle);

			expect(result.authors).toEqual(['John Doe', 'Jane Smith']);
			expect(result.tagNames).toEqual(['storm', 'hurricane']);
			expect(result.images).toEqual([{ url: 'https://example.com/image.jpg' }]);
		});

		it('should handle video content in the first block', () => {
			const mockArticle = {
				title: 'Test Article',
				seo: {},
				assetName: '/test-article',
				createdAt: '2025-04-25T10:00:00Z',
				updatedAt: '2025-04-25T14:00:00Z',
				category: {
					value: 'Weather',
				},
				featuredImage: 'https://example.com/image.jpg',
				content: {
					body: {
						root: {
							children: [
								{
									type: 'block',
									fields: {
										blockType: 'Video',
										title: 'Weather Video',
										description: 'A video about weather',
										image: 'https://example.com/thumbnail.jpg',
									} as VideoBlockConfig,
								},
							],
							type: 'root',
							direction: null,
							format: '',
							indent: 0,
							version: 1,
						},
					},
				},
			} as unknown as Article;

			const result = getArticleDetails(mockArticle);

			expect(result.video).toEqual({
				blockType: 'Video',
				title: 'Weather Video',
				description: 'A video about weather',
				image: 'https://example.com/thumbnail.jpg',
			});
		});
	});
});
