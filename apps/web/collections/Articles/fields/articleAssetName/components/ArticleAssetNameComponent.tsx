'use client';
import React, {
	useCallback,
	useEffect,
	useState,
	useRef,
	useMemo,
} from 'react';
import { TextFieldClientProps } from 'payload';
import {
	useField,
	Button,
	TextInput,
	FieldLabel,
	useFormFields,
	useForm,
	usePayloadAPI,
	useLocale,
} from '@payloadcms/ui';
import { formatDate } from '../utils/formatDate';

// Define the structure of relation field values
type RelationFieldValue = {
	relationTo: string;
	value: string;
};

type ArticleAssetNameComponentProps = {
	checkboxFieldPath: string;
	categorySlug: string;
	topicSlug: string;
	formattedDate: string;
	initialValue: string;
	sampleAssetName: string;
} & TextFieldClientProps;

export const ArticleAssetNameComponent: React.FC<
	ArticleAssetNameComponentProps
> = ({
	field,
	checkboxFieldPath,
	path,
	readOnly: readOnlyFromProps,
	categorySlug: initialCategorySlug,
	topicSlug: initialTopicSlug,
	formattedDate,
	initialValue,
	sampleAssetName,
}) => {
	const { label } = field;
	const locale = useLocale();

	// State to track fetched slugs
	const [categorySlug, setCategorySlug] = useState(initialCategorySlug);
	const [topicSlug, setTopicSlug] = useState(initialTopicSlug);

	// Refs to track previous values
	const prevCategoryIdRef = useRef<string | null>(null);
	const prevTopicIdRef = useRef<string | null>(null);

	const { value, setValue } = useField<string>({ path: path || field.name });
	const { dispatchFields } = useForm();

	// The value of the checkbox - when true, asset name is auto-generated
	const checkboxValue = useFormFields(([fields]) => {
		return fields[checkboxFieldPath]?.value as boolean;
	});

	// Watch for changes in relevant fields
	const slugValue = useFormFields(([fields]) => {
		// Make sure we're getting a string value
		const rawSlugValue = fields.slug?.value;
		return typeof rawSlugValue === 'string' ? rawSlugValue : '';
	});

	// Watch for changes in category field with proper typing
	const categoryValue = useFormFields(([fields]) => {
		if (!fields.category || !fields.category.value) return '';

		// Type assertion to help TypeScript understand the structure
		const categoryField = fields.category.value as RelationFieldValue;
		return categoryField.value;
	});

	// Watch for changes in topic field with proper typing
	const topicValue = useFormFields(([fields]) => {
		if (!fields.topic || !fields.topic.value) return '';

		// Type assertion to help TypeScript understand the structure
		const topicField = fields.topic.value as RelationFieldValue;
		return topicField.value;
	});

	// Watch for changes in publishDate field
	const publishDateValue = useFormFields(([fields]) => {
		return fields.publishDate?.value as string;
	});

	// Use Payload API for category data - always provide a valid URL
	const [{ data: categoryData }, { setParams: setCategoryParams }] =
		usePayloadAPI(`/api/payload/tags/${categoryValue || 'placeholder'}`);

	// Use Payload API for topic data - always provide a valid URL
	const [{ data: topicData }, { setParams: setTopicParams }] = usePayloadAPI(
		`/api/payload/tags/${topicValue || 'placeholder'}`,
	);

	// Update category slug when category data changes
	useEffect(() => {
		// Only process data if we have a valid categoryValue and data
		if (categoryValue && categoryData && categoryData.slug) {
			const newSlug =
				typeof categoryData.slug === 'object'
					? categoryData.slug['en-US'] || Object.values(categoryData.slug)[0]
					: categoryData.slug;

			setCategorySlug(newSlug);
		}
	}, [categoryData, categoryValue]);

	// Update topic slug when topic data changes
	useEffect(() => {
		// Only process data if we have a valid topicValue and data
		if (topicValue && topicData && topicData.slug) {
			const newSlug =
				typeof topicData.slug === 'object'
					? topicData.slug['en-US'] || Object.values(topicData.slug)[0]
					: topicData.slug;

			setTopicSlug(newSlug);
		}
	}, [topicData, topicValue]);

	// Trigger refetch when category value changes
	useEffect(() => {
		if (categoryValue && categoryValue !== prevCategoryIdRef.current) {
			setCategoryParams({ id: categoryValue });
			prevCategoryIdRef.current = categoryValue;
		}
	}, [categoryValue, setCategoryParams]);

	// Trigger refetch when topic value changes
	useEffect(() => {
		if (topicValue && topicValue !== prevTopicIdRef.current) {
			setTopicParams({ id: topicValue });
			prevTopicIdRef.current = topicValue;
		}
	}, [topicValue, setTopicParams]);

	// Memoize the current formatted date (client-side or fallback to server-side)
	const currentFormattedDate = useMemo(() => {
		// If we have a publishDate from the form, format it client-side
		if (publishDateValue) {
			try {
				// Validate the date string first
				const dateObj = new Date(publishDateValue);

				// Check if the date is valid
				if (isNaN(dateObj.getTime())) {
					console.warn('Invalid publishDate value:', publishDateValue);
					return formattedDate;
				}

				const formatted = formatDate(publishDateValue);

				// Check if the formatted result contains NaN
				if (formatted.includes('NaN')) {
					console.warn('Formatted date contains NaN:', formatted);
					return formattedDate;
				}

				return formatted;
			} catch (error) {
				console.warn('Error formatting publishDate client-side:', error);
				// Fall back to server-provided formattedDate
				return formattedDate;
			}
		}
		// Fall back to server-provided formattedDate
		return formattedDate;
	}, [publishDateValue, formattedDate]);

	// Memoize the path parts calculation
	const pathParts = useMemo(() => {
		if (!slugValue || !categorySlug || !currentFormattedDate) {
			return null;
		}

		const parts = [currentFormattedDate, categorySlug];
		if (locale.code !== 'en-US') parts.unshift(locale.code);
		if (topicSlug) parts.push(topicSlug);
		parts.push(slugValue);

		return parts;
	}, [locale.code, currentFormattedDate, categorySlug, topicSlug, slugValue]);

	// Memoize the full asset name
	const fullAssetName = useMemo(() => {
		if (!pathParts) {
			if (!sampleAssetName) return '';

			// If we don't have all the required fields, set a placeholder
			const placeholderAssetName = sampleAssetName.replace(
				'[slug]',
				slugValue || '[slug]',
			);
			// Ensure we have exactly one leading slash and no trailing slash
			return placeholderAssetName.replace(/\/+/g, '/');
		}

		// Ensure we have exactly one leading slash and no trailing slash
		return `/${pathParts.join('/')}`.replace(/\/+/g, '/');
	}, [pathParts, sampleAssetName, slugValue]);

	// Generate the asset name when relevant fields change
	useEffect(() => {
		if (checkboxValue && fullAssetName) {
			setValue(fullAssetName);
		}
	}, [checkboxValue, fullAssetName, setValue]);

	const handleLock = useCallback(
		(e: React.MouseEvent<Element>) => {
			e.preventDefault();

			dispatchFields({
				type: 'UPDATE',
				path: checkboxFieldPath,
				value: !checkboxValue,
			});
		},
		[checkboxValue, checkboxFieldPath, dispatchFields],
	);

	// Memoize the readOnly value
	const readOnly = useMemo(
		() => readOnlyFromProps || checkboxValue,
		[readOnlyFromProps, checkboxValue],
	);

	return (
		<div className="field-type">
			<div className="flex items-center justify-between">
				<FieldLabel htmlFor={`field-${path}`} label={label} />

				<Button
					className="m-0 pb-[0.3125rem]"
					buttonStyle="none"
					onClick={handleLock}
				>
					{checkboxValue ? 'Specify Manually' : 'Generate from Fields'}
				</Button>
			</div>

			<TextInput
				value={value || initialValue || ''}
				onChange={setValue}
				path={path || field.name}
				readOnly={Boolean(readOnly)}
			/>
		</div>
	);
};
