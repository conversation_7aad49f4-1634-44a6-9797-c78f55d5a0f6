import type { CollectionConfig } from 'payload';

import { slugField } from '@/fields/slug';
import { publishDateField } from '@/fields/publishDate';
import { categoryTag, defaultContentTags, topicTag } from '@/fields/tags';
import { authors } from '@/fields/authors';
import { revalidateArticle } from './hooks/revalidateArticle';
import { createAssetNameChangeHook } from '@/plugins/redirects';

import {
	headerLayout,
	partnerByline,
	subHeadline,
	title,
	externalAuthors,
} from '@/collections/Articles/fields';
import {
	seoTab,
	contentTab,
	coreMetadataTab,
	channelOverridesTab,
} from '@/collections/Articles/fields/tabs';
import { createTabs, createCollapsible } from '@/fields/utility';
import { featuredImage } from '@/fields/featuredImage';
import { articleAssetNameFields } from '@/collections/Articles/fields/articleAssetName';
import { setCreatedBy } from '@/fields/createdBy/hooks/setCreatedBy';
import { setUpdatedBy } from '@/fields/updatedBy/hooks/setUpdatedBy';
import { generatePreviewPath } from '@/utils/generatePreviewPath';
import { createFormEnhancerField } from '@/fields/formEnhancer';

const articleTabs = [contentTab, seoTab, coreMetadataTab, channelOverridesTab];

export const Articles: CollectionConfig = {
	slug: 'articles',
	access: {
		read: () => true,
	},
	defaultPopulate: {
		authors: true,
		title: true,
		category: true,
		assetName: true,
		topic: true,
		publishDate: true,
		updatedAt: true,
		createdBy: true,
		featuredImage: true,
		seo: {
			title: true,
			description: true,
		},
	},
	admin: {
		useAsTitle: 'title',
		defaultColumns: ['title', 'slug', 'status', 'publishDate', 'updatedAt'],
		livePreview: {
			url: ({ data, locale }) =>
				generatePreviewPath({
					data,
					locale: locale.code,
					collection: 'articles',
					isLivePreview: true,
				}),
		},
		preview: (data, { locale }: { locale: string }) =>
			generatePreviewPath({ data, locale, collection: 'articles' }),
	},
	fields: [
		createFormEnhancerField('articles'),
		createCollapsible(
			[
				title,
				subHeadline,
				categoryTag,
				authors,
				externalAuthors,
				partnerByline,
				headerLayout,
			],
			'Article Header',
			{
				admin: { initCollapsed: false },
			},
		),
		createCollapsible([featuredImage, topicTag], 'Article Details', {
			admin: { initCollapsed: true },
		}),
		createTabs(articleTabs),
		defaultContentTags,
		...articleAssetNameFields(),
		...publishDateField(),
		...slugField(),
	],
	hooks: {
		afterChange: [
			revalidateArticle,
			createAssetNameChangeHook({ isLocalized: true }),
		],
		beforeChange: [setCreatedBy, setUpdatedBy],
	},
	versions: {
		drafts: {
			autosave: {
				interval: 1000, // We set this interval for optimal live preview
			},
			schedulePublish: true,
		},
		maxPerDoc: 50,
	},
};
