import { PlopTypes } from '@turbo/gen';
import getMonorepoFilePath from './getMonorepoFilePath';
import type {
	AppendActionWithFormat,
	PrettifyAction,
	ExtendedActionType,
} from './types';

export const addCollection: PlopTypes.PlopGeneratorConfig = {
	description: 'Creates a new Payload Collection',
	prompts: [
		{
			type: 'input',
			name: 'collectionName',
			message: 'What is the name of the Collection? (PascalCase please)',
			default: 'MyCollection',
			validate: (input: string) =>
				/^[A-Z][a-zA-Z0-9]*$/.test(input) ||
				'Collection name must be in PascalCase (e.g., MyCollection)',
		},
	],
	actions: (data): ExtendedActionType[] => {
		const collectionName = (data?.collectionName as string) || 'MyCollection';

		const collectionPath = `collections/${collectionName}`;

		const addTemplateFiles: Array<{
			path: string;
			templateFile: string;
		}> = [
			{
				path: 'index.ts',
				templateFile: './templates/addCollection/collection.ts.hbs',
			},
			{
				path: 'fields/index.ts',
				templateFile: './templates/addCollection/field.ts.hbs',
			},
		];

		/**
		 * We may not want to append the blocks, maybe just point users at a README
		 * */
		const appendTemplateFiles: Array<{
			path: string;
			pattern: RegExp;
			template: string;
			format: boolean;
		}> = [
			{
				path: 'payload.config.ts',
				pattern: /(?=\/\*\* {2}End of collection imports \*\/)/,
				template: `import { {{collectionName}} } from "@/collections/{{collectionName}}";\n`,
				format: true,
			},
			{
				path: 'payload.config.ts',
				pattern: /(\s*\/\*\* {2}collection inclusion \*\/)/,
				template: `\n\t\t{{collectionName}},`,
				format: true,
			},
		];

		const addActions: PlopTypes.AddActionConfig[] = addTemplateFiles.map(
			({ path, templateFile }) => ({
				type: 'add',
				path: getMonorepoFilePath(collectionPath, path),
				templateFile: templateFile,
			}),
		);

		const appendActions: AppendActionWithFormat[] = appendTemplateFiles.map(
			({ path, pattern, template, format }) => ({
				type: 'append' as const,
				path: getMonorepoFilePath(path),
				separator: '\n',
				unique: true,
				pattern,
				template,
				format,
			}),
		);

		// Flatten all actions into a single array
		const actions = [...addActions, ...appendActions];

		// Create a Set to track unique paths for formatting and remove dups
		const formattedPaths = new Set<string>();

		/**
		 * Because of the modify we are doing, we could let the user run prettier on the files
		 * Or we could do it for them. This is a good example of how we can add custom actions
		 * */
		const prettifyActions: PrettifyAction[] = actions
			.filter(
				(action): action is AppendActionWithFormat =>
					'format' in action &&
					action.format === true &&
					!formattedPaths.has(action.path),
			)
			.map(({ path }) => {
				formattedPaths.add(path);
				return {
					type: 'prettify' as const,
					path,
				};
			});

		return [
			...actions,
			...prettifyActions,
			{ type: 'generateTypes' as const },
		] as ExtendedActionType[];
	},
};
