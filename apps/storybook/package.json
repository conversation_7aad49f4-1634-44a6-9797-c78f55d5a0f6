{"name": "storybook", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "storybook dev -p 6006", "build:storybook": "storybook build --webpack-stats-json", "clean": "rimraf storybook-static .turbo node_modules/.cache", "vercel:link": "pnpx vercel link", "vercel:env:pull": "pnpx vercel env pull --environment=development", "chromatic": "chromatic --build-script-name=build"}, "peerDependencies": {"class-variance-authority": "catalog:web", "next": "catalog:web", "react": "catalog:web", "react-dom": "catalog:web"}, "dependencies": {"@repo/dal": "workspace:*", "@repo/ui": "workspace:*", "@repo/icons": "workspace:*", "@repo/logger": "workspace:*", "@repo/storybook-utils": "workspace:*", "@twc/upsx-sdk": "workspace:*", "jotai": "catalog:web", "react": "catalog:web", "react-dom": "catalog:web", "swr": "catalog:web"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@storybook/addon-a11y": "catalog:storybook", "@storybook/addon-essentials": "catalog:storybook", "@storybook/addon-interactions": "catalog:storybook", "@storybook/addon-links": "catalog:storybook", "@storybook/blocks": "catalog:storybook", "@storybook/nextjs": "catalog:storybook", "@storybook/react": "catalog:storybook", "@storybook/types": "catalog:storybook", "@storybook/test": "catalog:storybook", "@svgr/webpack": "^8.1.0", "@types/node": "catalog:node", "@types/react": "catalog:web", "@types/react-dom": "catalog:web", "rimraf": "catalog:dev", "chromatic": "^13.0.0", "eslint": "catalog:dev", "storybook": "catalog:storybook", "typescript": "catalog:dev"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}