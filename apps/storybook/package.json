{"name": "storybook", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "storybook dev -p 6006", "build": "storybook build --webpack-stats-json", "clean": "rimraf storybook-static .turbo node_modules/.cache", "vercel:link": "pnpx vercel link", "vercel:env:pull": "pnpx vercel env pull --environment=development", "chromatic": "chromatic --build-script-name=build"}, "dependencies": {"@repo/dal": "workspace:*", "@repo/ui": "workspace:*", "@repo/icons": "workspace:*", "@repo/logger": "workspace:*", "@repo/storybook-utils": "workspace:*", "@twc/upsx-sdk": "workspace:*", "@svgr/webpack": "^8.1.0", "jotai": "^2.12.2", "react": "^19.1.0", "react-dom": "^19.1.0", "swr": "^2.3.3"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@storybook/addon-a11y": "^8.6.11", "@storybook/addon-essentials": "^8.6.11", "@storybook/addon-interactions": "^8.6.11", "@storybook/addon-links": "^8.6.11", "@storybook/blocks": "^8.6.11", "@storybook/nextjs": "^8.6.11", "@storybook/react": "^8.6.11", "@storybook/types": "^8.6.11", "@storybook/test": "^8.6.11", "@svgr/webpack": "^8.1.0", "@types/node": "^22.13.8", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "rimraf": "^6.0.1", "chromatic": "^13.0.0", "eslint": "^9.21.0", "storybook": "^8.6.11", "typescript": "~5.8.3"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}