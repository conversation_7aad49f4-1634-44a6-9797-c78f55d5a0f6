import type { StorybookConfig } from '@storybook/nextjs';
import * as path from 'path';

const config: StorybookConfig = {
	stories: [
		// Co-located stories
		'../../web/components/**/*.stories.@(ts|tsx)',
		'../../web/blocks/**/*.stories.@(ts|tsx)',
		'../../../packages/ui/src/**/*.stories.@(ts|tsx)',
		'../../../packages/ui/src/**/*.mdx',
		'../../../packages/icons/src/**/*.stories.@(ts|tsx)',
		'../../../packages/icons/src/**/*.mdx',
	],
	addons: [
		'@storybook/addon-links',
		'@storybook/addon-essentials',
		'@storybook/addon-interactions',
		'@storybook/addon-a11y',
	],
	framework: {
		name: '@storybook/nextjs',
		options: {
			nextConfigPath: path.resolve(
				__dirname,
				'../../../apps/web/next.config.js',
			),
			builder: {
				useSWC: true,
			},
		},
	},
	docs: {
		autodocs: 'tag',
	},
	staticDirs: ['../../web/public'],
	typescript: {
		reactDocgen: 'react-docgen-typescript',
		reactDocgenTypescriptOptions: {
			compilerOptions: {
				allowSyntheticDefaultImports: true,
				esModuleInterop: true,
			},
			propFilter: (prop) => {
				return prop.parent ? !/node_modules/.test(prop.parent.fileName) : true;
			},
		},
	},
	webpackFinal: async (config) => {
		// Resolve @/ imports from the web app
		if (config.resolve) {
			config.resolve.alias = {
				...config.resolve.alias,
				// TODO: Once we decouple components and stories from web, storybook build will
				// have package dependencies and this pattern wont be needed
				// We have this pattern because we have stories in web app
				'@': path.resolve(__dirname, '../../../apps/web'),
			};
		}
		return config;
	},
};

export default config;
