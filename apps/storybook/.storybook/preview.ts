import type { Preview } from '@storybook/react';
import { withFonts } from './decorators/withFonts';
// Import global styles with TailwindCSS
import '@repo/ui/globals.css';

const preview: Preview = {
	decorators: [withFonts],
	parameters: {
		actions: { argTypesRegex: '^on[A-Z].*' },
		controls: {
			matchers: {
				color: /(background|color)$/i,
				date: /Date$/,
			},
		},
		nextjs: {
			appDirectory: true,
			navigation: {
				pathname: '/',
			},
		},
		backgrounds: {
			default: 'light',
			values: [
				{
					name: 'light',
					value: '#ffffff',
				},
				{
					name: 'dark',
					value: '#1a202c',
				},
			],
		},
		layout: 'padded',
	},
};

export default preview;
