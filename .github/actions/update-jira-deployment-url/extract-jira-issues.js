module.exports = async ({ github, context, core, require }) => {
	try {
		const { createOpenAI } = require('@ai-sdk/openai');
		const { generateObject } = require('ai');
		const { z } = require('zod');

		// Zod schema for JIRA issue extraction
		const JiraExtractionSchema = z.object({
			issues: z.array(
				z.object({
					key: z
						.string()
						.regex(/^[A-Z]+-\d+$/, 'Must be valid JIRA format (e.g., ABC-123)'),
					source: z.enum(['pr_title', 'branch_name', 'both']),
					confidence: z.number().min(0).max(1),
				}),
			),
			reasoning: z.string().optional(),
		});

		const prTitle = process.env.PR_TITLE;
		const branchName = process.env.BRANCH_NAME;
		const aiApiKey = process.env.AI_API_KEY;
		const aiBaseUrl = process.env.AI_BASE_URL;

		console.log(`Analyzing PR Title: "${prTitle}"`);
		console.log(`Analyzing Branch Name: "${branchName}"`);

		if (!prTitle && !branchName) {
			console.log('No PR title or branch name provided');
			core.setOutput('jira_issues', '[]');
			return;
		}

		const prompt = `
Analyze the following Pull Request title and branch name to extract JIRA issue keys.

PR Title: "${prTitle}"
Branch Name: "${branchName}"

Look for JIRA issue patterns in the format: [A-Z]+-[0-9]+ (e.g., CW-2664, ABC-123, PROJ-456)

Rules:
1. Extract all valid JIRA issue keys from both the PR title and branch name
2. JIRA keys are typically in format: PROJECT-NUMBER (e.g., CW-2664, ABC-123)
3. Be case-sensitive - JIRA keys are always uppercase
4. Indicate the source of each issue (pr_title, branch_name, or both if found in both)
5. Provide a confidence score (0.0 to 1.0) for each extraction
6. Only include issues with confidence >= 0.8
7. Remove duplicates - if the same issue appears in both title and branch, mark source as "both"

Examples:
- "CW-2664: Flags Part 2" → CW-2664 from pr_title
- "refactor/glaskawiec/CW-2590/move-popover-to-ui" → CW-2590 from branch_name
- "Fix CW-123 and resolve ABC-456 issues" → CW-123 and ABC-456 from pr_title
`;

		// Create custom OpenAI provider instance for LiteLLM
		const customOpenAI = createOpenAI({
			apiKey: aiApiKey,
			baseURL: aiBaseUrl,
			compatibility: 'compatible', // Use compatible mode for 3rd party providers like LiteLLM
		});

		const result = await generateObject({
			model: customOpenAI('anthropic.claude-3-sonnet-20240229-v1:0'),
			schemaName: 'jira_extraction',
			schemaDescription:
				'Extract JIRA issue keys from PR title and branch name',
			schema: JiraExtractionSchema,
			prompt: prompt,
		});

		console.log('AI Analysis Result:', JSON.stringify(result.object, null, 2));

		// Filter issues with confidence >= 0.8
		const highConfidenceIssues = result.object.issues.filter(
			(issue) => issue.confidence >= 0.8,
		);

		console.log(
			`Found ${highConfidenceIssues.length} high-confidence JIRA issues`,
		);

		if (highConfidenceIssues.length > 0) {
			highConfidenceIssues.forEach((issue) => {
				console.log(
					`- ${issue.key} (source: ${issue.source}, confidence: ${issue.confidence})`,
				);
			});
		}

		// Output the issues as JSON string
		core.setOutput('jira_issues', JSON.stringify(highConfidenceIssues));
	} catch (error) {
		console.error('Error extracting JIRA issues:', error);

		// Fail silently as requested - output empty array
		core.setOutput('jira_issues', '[]');

		// Log the error but don't fail the action
		console.log('Continuing with empty JIRA issues list due to error');
	}
};
