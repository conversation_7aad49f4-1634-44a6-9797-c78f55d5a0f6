name: CI

on:
  pull_request:
    types: [opened, synchronize, ready_for_review]
    paths:
      - 'apps/storybook/**'
      - 'apps/web/components/**'
      - 'apps/web/blocks/**'
      - 'packages/ui/**'

env:
  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
  CI: true
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
  TURBO_TEAM: ${{ vars.TURBO_TEAM }}
  TURBO_CACHE: remote:rw
  HUSKY: 0

jobs:
  chromatic:
    name: Chromatic Visual Testing
    # Skip if PR is in draft state
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Required for Chromatic to track changes correctly

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - name: Setup pnpm
        uses: pnpm/action-setup@v4

      - name: Setup NPM configuration
        run: |
          # Make script executable
          chmod +x ./scripts/npmrc-manager.sh

          # Setup .npmrc with NPM_TOKEN
          ./scripts/npmrc-manager.sh setup

          # Verify .npmrc setup
          ./scripts/npmrc-manager.sh test

          # Verify NPM token access
          ./scripts/npmrc-manager.sh verify
        continue-on-error: true  # Continue even if token doesn't have access to private packages

      - name: Install dependencies
        run: pnpm install

      - name: Build
        run: pnpm turbo build:storybook

      - name: Publish to Chromatic
        uses: chromaui/action@v13
        with:
          projectToken: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
          workingDir: apps/storybook
          storybookBuildDir: storybook-static
          onlyChanged: true # 👈 Required option to enable TurboSnap
          zip: true
