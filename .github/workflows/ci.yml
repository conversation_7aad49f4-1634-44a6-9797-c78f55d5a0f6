name: CI

on:
  push:
    branches: ["main"]
  pull_request:
    types: [opened, synchronize]

env:
  TURBO_SCM_BASE: ${{ github.event_name == 'pull_request' && github.event.pull_request.base.sha || github.event.before }}
  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
  CI: true
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
  TURBO_TEAM: ${{ vars.TURBO_TEAM }}
  TURBO_REMOTE_ONLY: true
  HUSKY: 0

jobs:
  test:
    name: Lint and Test
    timeout-minutes: 15
    runs-on: ubuntu-latest
    # To use Remote Caching, uncomment the next lines and follow the steps below
    concurrency:
      group: ${{ github.workflow }}-${{ github.ref }}
      cancel-in-progress: true
    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          # set to zero for turbo to compare affected files
          fetch-depth: 0

      - uses: pnpm/action-setup@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "pnpm"

      - name: Setup NPM configuration
        run: |
          # Make script executable
          chmod +x ./scripts/npmrc-manager.sh

          # Setup .npmrc with NPM_TOKEN
          ./scripts/npmrc-manager.sh setup

          # Verify .npmrc setup
          ./scripts/npmrc-manager.sh test

          # Verify NPM token access
          ./scripts/npmrc-manager.sh verify
        continue-on-error: true  # Continue even if token doesn't have access to private packages

      - name: Install dependencies
        run: pnpm install

      - name: Check Format
        run: pnpm check-format

      - name: Lint, Check Types, Test
        run: pnpm turbo run lint check-types test --affected
