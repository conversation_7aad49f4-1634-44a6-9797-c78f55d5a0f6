name: E2E Tests

on:
  repository_dispatch:
    types:
      - vercel.deployment.success

env:
  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
  CI: true
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
  TURBO_TEAM: ${{ vars.TURBO_TEAM }}
  TURBO_CACHE: remote:rw
  HUSKY: 0
  RP_API_URL: "https://eks-rp-automation-us-east-1.weather.com/api/v1/wx-next-web"
  RP_UI_URL: "https://eks-rp-automation-us-east-1.weather.com/ui/#wx-next-web/launches/all"
  APOLLO_SUITES_CONFIG_PATH: .github/config/apollo-suites.json

jobs:
  determine-project:
    runs-on: ubuntu-latest
    outputs:
      project: ${{ steps.extract-deployment-info.outputs.project }}
      pr_number: ${{ steps.extract-deployment-info.outputs.pr_number }}
      sha: ${{ steps.extract-deployment-info.outputs.sha }}
      url: ${{ steps.extract-deployment-info.outputs.url }}
      is_main_branch: ${{ steps.extract-deployment-info.outputs.is_main }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Extract deployment information
        id: extract-deployment-info
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const script = require('./.github/workflows/scripts/extract-deployment-info.js')
            await script({github, context, core})

  run_playwright_tests:
    needs: determine-project
    if: needs.determine-project.outputs.is_main_branch != 'true' && needs.determine-project.outputs.pr_number != '' && needs.determine-project.outputs.sha != ''
    runs-on: ubuntu-latest
    container:
      image: mcr.microsoft.com/playwright:v1.54.1-noble
      options: --user 1001
    strategy:
      fail-fast: false
      matrix:
        shard_index: [1, 2, 3, 4]
    outputs:
      conclusion: ${{ steps.playwright-shard-execution.outputs.conclusion }}
      summary_text: ${{ steps.playwright-shard-execution.outputs.summary_text }}
      report_artifact_name: ${{ steps.playwright-shard-execution.outputs.report_artifact_name }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: pnpm/action-setup@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "pnpm"
      - name: Setup NPM configuration
        run: |
          # Make script executable
          chmod +x ./scripts/npmrc-manager.sh

          # Setup .npmrc with NPM_TOKEN
          ./scripts/npmrc-manager.sh setup

          # Verify .npmrc setup
          ./scripts/npmrc-manager.sh test

          # Verify NPM token access
          ./scripts/npmrc-manager.sh verify
        continue-on-error: true  # Continue even if token doesn't have access to private packages

      - name: Install dependencies
        run: pnpm install

      - name: Execute Playwright Shard ${{ matrix.shard_index }}
        id: playwright-shard-execution
        uses: ./.github/actions/e2e-tests/run-playwright-tests
        with:
          project: ${{ needs.determine-project.outputs.project }}
          deployment_url: ${{ needs.determine-project.outputs.url }}
          commit_sha: ${{ needs.determine-project.outputs.sha }}
          shard_index: ${{ matrix.shard_index }}
          total_shards: 4
          vercel_automation_bypass_secret: ${{ secrets.VERCEL_AUTOMATION_BYPASS_SECRET }}

  merge_playwright_reports:
    needs:
      - determine-project
      - run_playwright_tests
    if: ${{ !cancelled() && needs.determine-project.outputs.is_main_branch != 'true' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: pnpm/action-setup@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "pnpm"

      - name: Setup NPM configuration
        run: |
          # Make script executable
          chmod +x ./scripts/npmrc-manager.sh

          # Setup .npmrc with NPM_TOKEN
          ./scripts/npmrc-manager.sh setup

          # Verify .npmrc setup
          ./scripts/npmrc-manager.sh test

          # Verify NPM token access
          ./scripts/npmrc-manager.sh verify
        continue-on-error: true  # Continue even if token doesn't have access to private packages

      - name: Install dependencies
        run: pnpm install

      - name: Download Playwright Blob reports from GitHub Actions Artifacts
        uses: actions/download-artifact@v4
        continue-on-error: true # Allow to continue if no artifacts are found
        id: download_playwright_reports # Add an ID to check outcome
        with:
          path: all-blob-reports
          pattern: playwright-blob-report-*
          merge-multiple: true

      - name: Check for Playwright Reports and Prepare Dummy if Absent
        id: check_playwright_reports
        shell: bash
        run: |
          # Check if download step itself indicated failure or if the directory is empty/missing blobs
          if [[ "${{ steps.download_playwright_reports.outcome }}" == "failure" ]] || [ ! -d "all-blob-reports" ] || [ -z "$(find all-blob-reports -name '*.zip' -print -quit)" ]; then
            echo "No Playwright blob reports found or download failed. Creating dummy XML report."
            mkdir -p ./all-blob-reports # Ensure directory exists
            echo '<?xml version="1.0" encoding="UTF-8"?>' > ./all-blob-reports/report-merged.xml
            echo '<testsuites name="Playwright Test Results" tests="0" failures="0" skipped="0" errors="0" time="0">' >> ./all-blob-reports/report-merged.xml
            echo '</testsuites>' >> ./all-blob-reports/report-merged.xml
            echo "playwright_reports_found=false" >> $GITHUB_OUTPUT
            echo "Dummy report-merged.xml created:"
            ls -lha ./all-blob-reports/report-merged.xml
            cat ./all-blob-reports/report-merged.xml
          else
            echo "Playwright blob reports found."
            echo "playwright_reports_found=true" >> $GITHUB_OUTPUT
          fi

      - name: DEBUG
        shell: bash
        run: |
          echo "Current directory listing:"
          ls -lha .
          echo "Listing of all-blob-reports directory:"
          ls -lha ./all-blob-reports
          echo "Playwright reports found status: ${{ steps.check_playwright_reports.outputs.playwright_reports_found }}"
          if [ "${{ steps.check_playwright_reports.outputs.playwright_reports_found }}" == "false" ] && [ -f ./all-blob-reports/report-merged.xml ]; then
            echo "Contents of dummy report-merged.xml:"
            cat ./all-blob-reports/report-merged.xml
          elif [ "${{ steps.check_playwright_reports.outputs.playwright_reports_found }}" == "true" ] && [ -f ./all-blob-reports/report-merged.xml ]; then
            echo "Merged report-merged.xml exists (contents not shown for brevity if large)."
          fi

      - name: Merge Playwright Sharded Reports
        if: steps.check_playwright_reports.outputs.playwright_reports_found == 'true'
        run: pnpx playwright merge-reports --reporter junit ./all-blob-reports > ./all-blob-reports/report-merged.xml

      - name: Playwright Test Report
        id: playwright_reporter # Added id
        uses: dorny/test-reporter@v2
        if: ${{ !cancelled() }}       # run this step even if previous step failed
        with:
          name: Playwright Tests
          path: ./all-blob-reports/report-merged.xml
          reporter: jest-junit
          fail-on-error: false
          fail-on-empty: false
          # report-title: Playwright Tests
    outputs: # Added outputs for the job
      playwright_conclusion: ${{ steps.playwright_reporter.outputs.conclusion }}
      playwright_passed: ${{ steps.playwright_reporter.outputs.passed }}
      playwright_failed: ${{ steps.playwright_reporter.outputs.failed }}
      playwright_skipped: ${{ steps.playwright_reporter.outputs.skipped }}
      playwright_time: ${{ steps.playwright_reporter.outputs.time }}

  run_apollo_tests:
    needs: determine-project
    if: needs.determine-project.outputs.is_main_branch != 'true' && needs.determine-project.outputs.pr_number != '' && needs.determine-project.outputs.sha != ''
    runs-on: ubuntu-latest
    outputs:
      summary_text: ${{ steps.apollo-execution.outputs.summary_text }}
      conclusion: ${{ steps.apollo-execution.outputs.conclusion }}
      report_url_markdown: ${{ steps.apollo-execution.outputs.report_url_markdown }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: pnpm/action-setup@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "pnpm"
      - name: Setup NPM configuration
        run: |
          # Make script executable
          chmod +x ./scripts/npmrc-manager.sh

          # Setup .npmrc with NPM_TOKEN
          ./scripts/npmrc-manager.sh setup

          # Verify .npmrc setup
          ./scripts/npmrc-manager.sh test

          # Verify NPM token access
          ./scripts/npmrc-manager.sh verify
        continue-on-error: true  # Continue even if token doesn't have access to private packages

      - name: Install dependencies
        run: pnpm install

      - name: Execute Apollo Tests
        id: apollo-execution
        uses: ./.github/actions/e2e-tests/run-apollo-project
        with:
          project_name: ${{ needs.determine-project.outputs.project }}
          pr_number: ${{ needs.determine-project.outputs.pr_number }}
          deployment_url: ${{ needs.determine-project.outputs.url }}
          commit_sha: ${{ needs.determine-project.outputs.sha }}
          rp_api_url: ${{ env.RP_API_URL }}
          rp_ui_url: ${{ env.RP_UI_URL }} # Added this line
          rp_token: ${{ secrets.RP_TOKEN }}
          automation_dispatch_token: ${{ secrets.AUTOMATION_DISPATCH_TOKEN }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          apollo_suites_config_path: ${{ env.APOLLO_SUITES_CONFIG_PATH }}

      - name: Upload Apollo Aggregated Test Details
        uses: actions/upload-artifact@v4
        if: always() # Always run to upload results even if previous steps fail
        with:
          name: apollo-aggregated-test-details
          path: apollo_aggregated_test_details.json
          retention-days: 7

  convert_apollo_results_to_mocha:
    needs:
      - determine-project
      - run_apollo_tests
    if: always() && !cancelled() && needs.determine-project.outputs.is_main_branch != 'true'
    runs-on: ubuntu-latest
    outputs:
      apollo_conclusion: ${{ steps.test-reporter.outputs.conclusion }}
      apollo_summary_text: ${{ steps.test-reporter.outputs.summary }} # Or derive from mocha.json
      apollo_passed: ${{ steps.test-reporter.outputs.passed }}
      apollo_failed: ${{ steps.test-reporter.outputs.failed }}
      apollo_skipped: ${{ steps.test-reporter.outputs.skipped }}
      apollo_time: ${{ steps.test-reporter.outputs.time }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: pnpm/action-setup@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install

      - name: Download Apollo Aggregated Test Details
        uses: actions/download-artifact@v4
        id: download_apollo_report # Add an ID to check outcome
        continue-on-error: true # Allow to continue if no artifact is found
        with:
          name: apollo-aggregated-test-details
          path: . # Download to current directory

      - name: Check for Apollo Report and Prepare Dummy Mocha if Absent
        id: check_apollo_report
        shell: bash
        run: |
          # Check if download step itself indicated failure or if the file is missing/empty
          if [[ "${{ steps.download_apollo_report.outcome }}" == "failure" ]] || [ ! -s apollo_aggregated_test_details.json ]; then
            echo "Apollo aggregated test details not found, empty, or download failed. Creating dummy Mocha JSON report."
            echo '{ "stats": { "suites": 0, "tests": 0, "passes": 0, "pending": 0, "failures": 0, "start": "1970-01-01T00:00:00.000Z", "end": "1970-01-01T00:00:00.000Z", "duration": 0 }, "tests": [], "pending": [], "failures": [], "passes": [] }' > apollo-mocha-results.json
            echo "apollo_report_found=false" >> $GITHUB_OUTPUT
            echo "Dummy apollo-mocha-results.json created:"
            ls -lha apollo-mocha-results.json
            cat apollo-mocha-results.json
          else
            echo "Apollo aggregated test details found."
            echo "apollo_report_found=true" >> $GITHUB_OUTPUT
          fi

      - name: Convert Apollo JSON to Mocha JSON
        if: steps.check_apollo_report.outputs.apollo_report_found == 'true'
        id: convert-apollo-to-mocha
        uses: actions/github-script@v7
        with:
          script: |
            const script = require('./.github/workflows/scripts/convert-apollo-to-mocha.js');
            await script({github, context, core, fs: require('fs'), path: require('path')});
        env:
          APOLLO_JSON_INPUT_PATH: apollo_aggregated_test_details.json
          MOCHA_JSON_OUTPUT_PATH: apollo-mocha-results.json

      - name: Upload Mocha Formatted Apollo Results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: apollo-mocha-results
          path: apollo-mocha-results.json
          retention-days: 7

      - name: Apollo Test Report (Mocha Format)
        id: test-reporter
        uses: dorny/test-reporter@v2
        if: ${{ !cancelled() }}
        with:
          name: Apollo Tests (Processed)
          path: apollo-mocha-results.json # Path to the mocha-json file
          reporter: mocha-json # Specify the reporter type
          fail-on-error: false # Decide if this step should fail the workflow
          fail-on-empty: false

  update_consolidated_e2e_check:
    name: Update Consolidated E2E Check
    runs-on: ubuntu-latest
    if: always() && needs.determine-project.outputs.is_main_branch != 'true'
    needs:
      - determine-project
      - run_playwright_tests
      - merge_playwright_reports
      - run_apollo_tests
      - convert_apollo_results_to_mocha
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Update GitHub Check Run
        uses: actions/github-script@v7
        env:
          PROJECT_NAME: ${{ needs.determine-project.outputs.project }}
          COMMIT_SHA: ${{ needs.determine-project.outputs.sha }}
          PLAYWRIGHT_CONCLUSION: ${{ needs.merge_playwright_reports.outputs.playwright_conclusion }}
          APOLLO_PROCESSED_CONCLUSION: ${{ needs.convert_apollo_results_to_mocha.outputs.apollo_conclusion }}
          APOLLO_RAW_CONCLUSION: ${{ needs.run_apollo_tests.outputs.conclusion }}
          APOLLO_REPORT_LINK_MARKDOWN: ${{ needs.run_apollo_tests.outputs.report_url_markdown }}
          WORKFLOW_RUN_URL: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const script = require('./.github/workflows/scripts/update-e2e-check-run.js');
            await script({github, context, core});

  prepare_comment_data:
    name: Prepare E2E Summary Comment Data
    runs-on: ubuntu-latest
    if: always() && needs.determine-project.outputs.pr_number != '' # Only run if there's a PR
    needs:
      - determine-project
      - merge_playwright_reports
      - convert_apollo_results_to_mocha
      - run_apollo_tests # For raw conclusion if processed is skipped
    outputs:
      comment_json_payload: ${{ steps.construct-json.outputs.json_payload }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4 # Needed for potential scripts if dorny/test-reporter outputs are insufficient

      - name: Construct JSON Payload for Comment
        id: construct-json
        uses: actions/github-script@v7
        env:
          PROJECT_NAME: ${{ needs.determine-project.outputs.project }}
          COMMIT_SHA: ${{ needs.determine-project.outputs.sha }}
          PR_NUMBER: ${{ needs.determine-project.outputs.pr_number }}
          # Playwright results
          PLAYWRIGHT_CONCLUSION: ${{ needs.merge_playwright_reports.outputs.playwright_conclusion || 'skipped' }}
          PLAYWRIGHT_PASSED: ${{ needs.merge_playwright_reports.outputs.playwright_passed || 0 }}
          PLAYWRIGHT_FAILED: ${{ needs.merge_playwright_reports.outputs.playwright_failed || 0 }}
          PLAYWRIGHT_SKIPPED: ${{ needs.merge_playwright_reports.outputs.playwright_skipped || 0 }}
          PLAYWRIGHT_TIME: ${{ needs.merge_playwright_reports.outputs.playwright_time || 0 }}
          # Apollo results (from processed mocha)
          APOLLO_CONCLUSION: ${{ needs.convert_apollo_results_to_mocha.outputs.apollo_conclusion || 'skipped' }}
          APOLLO_PASSED: ${{ needs.convert_apollo_results_to_mocha.outputs.apollo_passed || 0 }}
          APOLLO_FAILED: ${{ needs.convert_apollo_results_to_mocha.outputs.apollo_failed || 0 }}
          APOLLO_SKIPPED: ${{ needs.convert_apollo_results_to_mocha.outputs.apollo_skipped || 0 }}
          APOLLO_TIME: ${{ needs.convert_apollo_results_to_mocha.outputs.apollo_time || 0 }}
          # Apollo raw conclusion (fallback if processed is skipped but raw indicates failure/success)
          APOLLO_RAW_CONCLUSION: ${{ needs.run_apollo_tests.outputs.conclusion || 'skipped' }}
          WORKFLOW_RUN_URL: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}
        with:
          script: |
            const {
              PROJECT_NAME, COMMIT_SHA, PR_NUMBER,
              PLAYWRIGHT_CONCLUSION, PLAYWRIGHT_PASSED, PLAYWRIGHT_FAILED, PLAYWRIGHT_SKIPPED, PLAYWRIGHT_TIME,
              APOLLO_CONCLUSION, APOLLO_PASSED, APOLLO_FAILED, APOLLO_SKIPPED, APOLLO_TIME,
              APOLLO_RAW_CONCLUSION,
              WORKFLOW_RUN_URL
            } = process.env;

            if (!PR_NUMBER) {
              core.info('No PR number, skipping JSON payload generation for comment.');
              core.setOutput('json_payload', '{}'); // Output empty JSON to avoid downstream errors
              return;
            }

            let finalApolloConclusion = APOLLO_CONCLUSION;
            // If Apollo processed conclusion is skipped but raw conclusion is more definitive
            if (APOLLO_CONCLUSION === 'skipped' && (APOLLO_RAW_CONCLUSION === 'failure' || APOLLO_RAW_CONCLUSION === 'success' || APOLLO_RAW_CONCLUSION === 'cancelled')) {
              core.info(`Using Apollo Raw Conclusion '${APOLLO_RAW_CONCLUSION}' as Processed Conclusion was '${APOLLO_CONCLUSION}'.`);
              finalApolloConclusion = APOLLO_RAW_CONCLUSION;
            }

            let overallStatus = '✅ Success';
            if (PLAYWRIGHT_CONCLUSION === 'failure' || finalApolloConclusion === 'failure') {
              overallStatus = '❌ Failed';
            } else if (PLAYWRIGHT_CONCLUSION === 'cancelled' || finalApolloConclusion === 'cancelled') {
              overallStatus = '⚠️ Cancelled'; // Or some other indicator
            } else if (PLAYWRIGHT_CONCLUSION === 'skipped' && finalApolloConclusion === 'skipped') {
              overallStatus = '➖ Skipped';
            }
            // If one is success and other is skipped/neutral, it's success (covered by default)

            const payload = {
              sha1: COMMIT_SHA.substring(0, 7), // Short SHA
              status: overallStatus,
              playwright_results: {
                passed: parseInt(PLAYWRIGHT_PASSED) || 0,
                failed: parseInt(PLAYWRIGHT_FAILED) || 0,
                skipped: parseInt(PLAYWRIGHT_SKIPPED) || 0,
                time: parseInt(PLAYWRIGHT_TIME) || 0, // Assuming time is in ms from dorny/test-reporter
              },
              apollo_results: {
                passed: parseInt(APOLLO_PASSED) || 0,
                failed: parseInt(APOLLO_FAILED) || 0,
                skipped: parseInt(APOLLO_SKIPPED) || 0,
                time: parseInt(APOLLO_TIME) || 0, // Assuming time is in ms
              },
              workflow_run_url: WORKFLOW_RUN_URL,
            };
            core.setOutput('json_payload', JSON.stringify(payload));
            core.info('Generated JSON payload for comment:');
            core.info(JSON.stringify(payload, null, 2));

  post_pr_summary_comment:
    name: Post E2E Summary Comment
    runs-on: ubuntu-latest
    if: always() && needs.determine-project.outputs.pr_number != '' && needs.prepare_comment_data.outputs.comment_json_payload != '{}'
    needs:
      - determine-project
      - prepare_comment_data
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Post or Update PR Comment
        uses: ./.github/actions/e2e-tests/post-e2e-results-comment
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          pr_number: ${{ needs.determine-project.outputs.pr_number }}
          results_json: ${{ needs.prepare_comment_data.outputs.comment_json_payload }}
          commit_sha: ${{ needs.determine-project.outputs.sha }}
          project_name: ${{ needs.determine-project.outputs.project }}
          # The job_name input for post-e2e-results-comment action defaults to 'E2E Test Summary'
          # which is used to create the hidden identifier: <!-- E2E Test Summary-${projectName} -->
          # If a different identifier is needed, it can be passed here.
          # job_name: 'UniqueE2ESummaryIdentifier'
