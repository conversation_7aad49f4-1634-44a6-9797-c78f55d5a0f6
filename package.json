{"name": "wx-next", "private": true, "scripts": {"build": "turbo run build", "clean": "turbo run clean", "dev": "turbo run dev", "test": "turbo run test", "lint": "turbo run lint", "format": "prettier . --write", "check-format": "prettier . --check", "check-types": "turbo run check-types", "prepare": "[ \"$CI\" = \"true\" ] || [ \"$VERCEL\" = \"1\" ] || [ -n \"$VERCEL_ENV\" ] && echo 'Skipping husky in CI environment' || husky", "setup-hooks": "husky && chmod +x .husky/pre-commit", "check-all": "turbo run check-all", "precommit": "lint-staged"}, "devDependencies": {"husky": "^9.1.7", "lint-staged": "^16.0.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "turbo": "^2.4.4", "typescript": "~5.8.3"}, "packageManager": "pnpm@10.13.1", "engines": {"node": ">=22", "pnpm": ">=10"}, "lint-staged": {"*.{js,jsx,mjs,ts,tsx,mts,json,css}": ["pnpm run format"]}}