{"name": "wx-next", "private": true, "scripts": {"build": "turbo run build:storybook build", "clean": "turbo run clean", "dev": "turbo run dev", "test": "turbo run test", "lint": "turbo run lint", "format": "prettier . --write", "check-format": "prettier . --check", "check-types": "turbo run check-types", "prepare": "[ \"$CI\" = \"true\" ] || [ \"$VERCEL\" = \"1\" ] || [ -n \"$VERCEL_ENV\" ] && echo 'Skipping husky in CI environment' || husky", "setup-hooks": "husky && chmod +x .husky/pre-commit", "precommit": "lint-staged"}, "devDependencies": {"@ai-sdk/openai": "catalog:dev", "ai": "catalog:dev", "husky": "^9.1.7", "jira-client": "^8.2.2", "lint-staged": "^16.0.0", "prettier": "catalog:dev", "prettier-plugin-tailwindcss": "catalog:dev", "turbo": "^2.4.4", "@turbo/gen": "^2.4.4", "typescript": "catalog:dev", "zod": "catalog:dev"}, "packageManager": "pnpm@10.13.1", "engines": {"node": ">=22", "pnpm": ">=10"}, "lint-staged": {"*.{js,jsx,mjs,ts,tsx,mts,json,css}": ["pnpm run format"]}}