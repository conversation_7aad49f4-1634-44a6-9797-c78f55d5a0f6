{"name": "wx-next", "private": true, "scripts": {"build": "turbo run build", "clean": "turbo run clean", "dev": "turbo run dev", "test": "turbo run test", "lint": "turbo run lint", "lint:md": "markdownlint \"**/*.md\"", "lint:md:fix": "markdownlint --fix \"**/*.md\"", "format": "prettier . --write", "check-format": "prettier . --check", "check-types": "turbo run check-types", "prepare": "[ \"$CI\" = \"true\" ] || [ \"$VERCEL\" = \"1\" ] || [ -n \"$VERCEL_ENV\" ] && echo 'Skipping husky in CI environment' || husky", "setup-hooks": "husky && chmod +x .husky/pre-commit", "check-all": "turbo run check-all", "precommit": "lint-staged"}, "devDependencies": {"husky": "^9.1.7", "lint-staged": "^16.0.0", "markdownlint": "^0.38.0", "markdownlint-cli": "^0.45.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "turbo": "^2.4.4", "typescript": "~5.8.3"}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184", "engines": {"node": ">=22", "pnpm": ">=10"}, "lint-staged": {"*.{js,jsx,mjs,ts,tsx,mts,json,css}": ["pnpm run format"], "*.md": ["pnpm run lint:md", "pnpm run format"]}}