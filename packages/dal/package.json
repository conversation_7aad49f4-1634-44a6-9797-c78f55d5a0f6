{"name": "@repo/dal", "version": "0.0.1", "private": true, "scripts": {"lint": "eslint --max-warnings=0 .", "check-types": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest watch", "clean": "rimraf .turbo dist tsconfig.tsbuildinfo", "build": "tsc --build", "dev": "tsc --watch"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@repo/vitest-config": "workspace:*", "@types/node": "catalog:node", "eslint": "catalog:dev", "rimraf": "catalog:dev", "typescript": "catalog:dev", "vitest": "catalog:dev"}, "exports": {"./*": "./dist/*.js"}}