{"name": "@repo/logger", "version": "0.1.0", "description": "Debug logging utility for wx-next monorepo", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"debug": "^4.3.4"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/debug": "^4.1.12", "eslint": "catalog:dev", "typescript": "catalog:dev"}}