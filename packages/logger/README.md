# @repo/logger

Standardized logging utility for the wx-next monorepo using the industry-standard [debug](https://github.com/debug-js/debug) library. Provides consistent, environment-aware logging with zero runtime cost in production.

## Features

- **Zero-cost in production**: Logs are automatically disabled unless DEBUG is explicitly set
- **Namespace organization**: Clear component-based namespace hierarchy
- **Environment aware**: Different control methods for Node.js and browser environments
- **Type-safe**: Full TypeScript support with proper interfaces
- **Performance optimized**: Minimal runtime overhead when logging is disabled
- **Flexible filtering**: Granular control over log levels and components

## Installation

```bash
pnpm add @repo/logger
```

## Basic Usage

### Creating a Logger

```typescript
import { createLogger } from '@repo/logger';

// Create a component-specific logger
const logger = createLogger('WeatherComponent');

// Basic logging
logger('Component initialized');
logger.info('Fetching weather data', { geocode: '40.7,-74.0' });
logger.warn('API rate limit approaching');
logger.error('Failed to fetch data', error);
logger.lifecycle('Component mounted');
```

### One-off Logging

```typescript
import { log } from '@repo/logger';

// For quick, one-time logging without creating a logger instance
log('Utils', 'Processing data', data);
```

### Extended Namespaces

```typescript
const logger = createLogger('WeatherComponent');

// Create sub-loggers for specific functionality
const apiLogger = logger.extend('API');
const cacheLogger = logger.extend('Cache');

apiLogger.info('Making request to weather service');
cacheLogger.info('Cache hit for location data');
```

## Environment Control

### Node.js / Development Environment

Use the `DEBUG` environment variable to control logging output:

```bash
# Enable all wx-next logging
DEBUG=wx-next:* pnpm dev

# Enable all logging (including other libraries)
DEBUG=* pnpm dev

# Enable specific components
DEBUG=wx-next:WeatherComponent:* pnpm dev
DEBUG=wx-next:JWPlayer:* pnpm dev

# Enable specific log levels
DEBUG=wx-next:*:error pnpm dev
DEBUG=wx-next:*:warn pnpm dev
DEBUG=wx-next:*:lifecycle pnpm dev

# Combine multiple patterns
DEBUG=wx-next:*:error,wx-next:WeatherComponent:* pnpm dev
DEBUG=wx-next:WeatherComponent:*,wx-next:LocationService:* pnpm dev

# Exclude specific namespaces
DEBUG=wx-next:*,-wx-next:VerboseComponent:* pnpm dev
```

### Browser Environment

In the browser, use `localStorage.debug` to control logging:

```javascript
// Enable all wx-next logging
localStorage.debug = 'wx-next:*';

// Enable all logging (including other libraries)
localStorage.debug = '*';

// Enable specific components
localStorage.debug = 'wx-next:WeatherComponent:*';
localStorage.debug = 'wx-next:JWPlayer:*';

// Enable specific log levels
localStorage.debug = 'wx-next:*:error';
localStorage.debug = 'wx-next:*:warn';
localStorage.debug = 'wx-next:*:lifecycle';

// Combine multiple patterns
localStorage.debug = 'wx-next:*:error,wx-next:WeatherComponent:*';
localStorage.debug = 'wx-next:WeatherComponent:*,wx-next:LocationService:*';

// Exclude specific namespaces
localStorage.debug = 'wx-next:*,-wx-next:VerboseComponent:*';

// Clear all debug logging
localStorage.removeItem('debug');
// or
localStorage.debug = '';
```

After setting `localStorage.debug`, refresh the page to see the logs in the browser's DevTools console.

### Browser DevTools Integration

The debug library integrates seamlessly with browser DevTools:

- **Console filtering**: Use the browser's console filter to further refine log output
- **Colored output**: Different namespaces are automatically colored for easy identification
- **Timestamps**: Enable timestamps by setting `localStorage.debug` to include timing info
- **Performance**: Logs are only processed when the namespace is enabled

#### Browser Debugging Examples

```javascript
// Show all logs for debugging a specific feature
localStorage.debug = 'wx-next:*';

// Debug only video-related components
localStorage.debug = 'wx-next:VideoBlock:*,wx-next:JWPlayer:*';

// Show only errors across all components
localStorage.debug = 'wx-next:*:error';

// Debug a specific component with all its sub-namespaces
localStorage.debug = 'wx-next:WeatherComponent:*';

// Combine error logs with specific component debugging
localStorage.debug = 'wx-next:*:error,wx-next:LocationService:*';
```

## Log Levels

The logger provides different methods for different types of messages:

### `logger()` / `logger.info()`
General information and data flow:
```typescript
logger.info('Fetching weather data', { geocode: location.geocode });
logger.info('Weather data received', { temperature: data.temperature });
```

### `logger.error()`
Actual errors and failures:
```typescript
logger.error('Failed to fetch weather data', {
  error: error.message,
  geocode: location.geocode,
});
```

### `logger.warn()`
Non-critical issues and fallback scenarios:
```typescript
logger.warn('API rate limit approaching', { remaining: rateLimitInfo.remaining });
logger.warn('Using cached data due to network error');
```

### `logger.lifecycle()`
Component mounting, unmounting, and major state changes:
```typescript
logger.lifecycle('Component mounted', { location: location?.displayName });
logger.lifecycle('Component unmounting');
logger.lifecycle('User authentication state changed', { isLoggedIn: true });
```

## Complete Example

```typescript
import { createLogger } from '@repo/logger';
import { getCurrentObservations } from '@repo/dal/weather';

const logger = createLogger('WeatherComponent');

export const WeatherComponent: React.FC<WeatherProps> = ({ location }) => {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    logger.lifecycle('Component mounting', {
      location: location?.displayName
    });

    const fetchData = async () => {
      logger.info('Fetching weather data', {
        geocode: location.geocode
      });

      try {
        const result = await getCurrentObservations({
          geocode: location.geocode,
          units: Units.IMPERIAL,
        });

        logger.info('Weather data received', {
          temperature: result.temperature,
          condition: result.condition,
        });

        setData(result);
      } catch (err) {
        logger.error('Failed to fetch weather data', {
          error: err.message,
          geocode: location.geocode,
        });
        setError(err);
      }
    };

    fetchData();

    return () => {
      logger.lifecycle('Component unmounting', {
        location: location?.displayName
      });
    };
  }, [location]);

  if (error) {
    logger.warn('Rendering error state', { error: error.message });
    return <ErrorDisplay error={error} />;
  }

  if (!data) {
    logger.info('Rendering loading state');
    return <LoadingSpinner />;
  }

  logger.info('Rendering weather data', { temperature: data.temperature });
  return <WeatherDisplay data={data} />;
};
```

## Best Practices

### Namespace Naming
- Use **PascalCase** for component names: `WeatherComponent`, `LocationService`
- Use descriptive names that match your component or module names
- Create sub-namespaces for complex components: `WeatherComponent:API`, `WeatherComponent:Cache`

### Log Level Usage
- **`info`**: General information, data flow, successful operations
- **`warn`**: Non-critical issues, fallbacks, deprecated usage
- **`error`**: Actual errors, failures, exceptions
- **`lifecycle`**: Component lifecycle events, major state changes

### Data Logging
Always include relevant context data with log messages:
```typescript
// ✅ Good: Include context
logger.info('API request completed', {
  endpoint: '/weather/current',
  duration: 150,
  status: 200
});

// ❌ Bad: No context
logger.info('API request completed');
```

### Performance Considerations
- Logging has zero cost when disabled (production default)
- Avoid expensive operations in log arguments when possible
- Use object destructuring for cleaner log output

## Migration from Existing Debug Utilities

If you're migrating from an existing debug utility:

```typescript
// Old approach
import { debugLogger } from '../utils/debugLogger';
debugLogger.info('ComponentName', 'Message', data);

// New approach
import { createLogger } from '@repo/logger';
const logger = createLogger('ComponentName');
logger.info('Message', data);
```

## Production Behavior

- **Automatic disabling**: All logs are automatically disabled in production unless `DEBUG` is explicitly set
- **Zero runtime cost**: When disabled, log calls are no-ops with minimal overhead
- **Security**: No sensitive data is logged unless explicitly enabled
- **Bundle size**: The debug library is optimized for minimal bundle impact

## TypeScript Support

The logger is fully typed with TypeScript:

```typescript
interface Logger {
  (message: string, ...args: unknown[]): void;
  info(message: string, ...args: unknown[]): void;
  error(message: string, ...args: unknown[]): void;
  warn(message: string, ...args: unknown[]): void;
  lifecycle(message: string, ...args: unknown[]): void;
  extend(namespace: string): Logger;
}
```

## Troubleshooting

### Logs Not Appearing

1. **Check DEBUG setting**:
   - Node.js: Ensure `DEBUG` environment variable is set
   - Browser: Check `localStorage.debug` value

2. **Verify namespace pattern**:
   ```javascript
   // Make sure your pattern matches the logger namespace
   localStorage.debug = 'wx-next:YourComponentName:*';
   ```

3. **Refresh the page** (browser only) after changing `localStorage.debug`

### Common Patterns

```javascript
// Debug everything during development
localStorage.debug = '*';

// Debug only your application
localStorage.debug = 'wx-next:*';

// Debug specific feature area
localStorage.debug = 'wx-next:Weather*:*,wx-next:Location*:*';

// Debug only errors
localStorage.debug = 'wx-next:*:error';
```

## Related Documentation

- [Debug Logger Pattern](.clinerules/05-system-patterns/debug-logger.md) - Implementation patterns and guidelines
- [debug-js Documentation](https://github.com/debug-js/debug) - Underlying library documentation
