import debug from 'debug';

// Base namespace for all wx-next logging
const BASE_NAMESPACE = 'wx-next';

// Logger interface - keep it simple and inline
interface Logger {
	(message: string, ...args: unknown[]): void;
	info(message: string, ...args: unknown[]): void;
	error(message: string, ...args: unknown[]): void;
	warn(message: string, ...args: unknown[]): void;
	lifecycle(message: string, ...args: unknown[]): void;
	extend(namespace: string): Logger;
}

/**
 * Factory function to create component-specific loggers
 *
 * @param namespace - The namespace for this logger (e.g., 'JWPlayer', 'FrontendAdminHeader')
 * @returns Logger instance with debug, error, warn, and lifecycle methods
 *
 * @example
 * ```typescript
 * import { createLogger } from '@repo/logger';
 *
 * const logger = createLogger('JWPlayer');
 * logger('Player initialized', { config });
 * logger.info('Playing video');
 * logger.error('Setup failed', error);
 * logger.warn('Deprecated option used', option);
 * logger.lifecycle('Component mounted');
 * ```
 */
export function createLogger(namespace: string): Logger {
	const baseDebug = debug(`${BASE_NAMESPACE}:${namespace}`);
	const errorDebug = debug(`${BASE_NAMESPACE}:${namespace}:error`);
	const warnDebug = debug(`${BASE_NAMESPACE}:${namespace}:warn`);
	const lifecycleDebug = debug(`${BASE_NAMESPACE}:${namespace}:lifecycle`);

	const logger = (message: string, ...args: unknown[]) => {
		baseDebug(message, ...args);
	};

	logger.info = (message: string, ...args: unknown[]) => {
		baseDebug(message, ...args);
	};

	logger.error = (message: string, ...args: unknown[]) => {
		errorDebug(message, ...args);
	};

	logger.warn = (message: string, ...args: unknown[]) => {
		warnDebug(message, ...args);
	};

	logger.lifecycle = (message: string, ...args: unknown[]) => {
		lifecycleDebug(message, ...args);
	};

	logger.extend = (subNamespace: string) => {
		return createLogger(`${namespace}:${subNamespace}`);
	};

	return logger as Logger;
}

/**
 * Utility for one-off logging without creating a logger instance
 *
 * @param namespace - The namespace for this log message
 * @param message - The message to log
 * @param args - Additional arguments to log
 *
 * @example
 * ```typescript
 * import { log } from '@repo/logger';
 *
 * log('Utils', 'Processing data', data);
 * ```
 */
export function log(
	namespace: string,
	message: string,
	...args: unknown[]
): void {
	const logger = createLogger(namespace);
	logger(message, ...args);
}
