{"name": "@repo/storybook-utils", "version": "0.0.0", "type": "module", "private": true, "scripts": {"lint": "eslint --max-warnings=0 .", "check-types": "tsc --noEmit", "clean": "rimraf .turbo dist tsconfig.tsbuildinfo", "build": "tsc --build", "dev": "tsc --watch"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "catalog:node", "eslint": "catalog:dev", "rimraf": "catalog:dev", "typescript": "catalog:dev"}, "exports": {"./*": "./dist/*.js"}}