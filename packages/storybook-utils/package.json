{"name": "@repo/storybook-utils", "version": "0.0.0", "type": "module", "private": true, "scripts": {"lint": "eslint --max-warnings=0 .", "check-types": "tsc --noEmit", "clean": "rimraf .turbo dist tsconfig.tsbuildinfo", "build": "tsc --build", "dev": "tsc --watch"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.13.8", "eslint": "^9.20.0", "rimraf": "^6.0.1", "typescript": "~5.8.3"}, "exports": {"./*": "./dist/*.js"}}