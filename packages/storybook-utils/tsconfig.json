{
	"extends": "@repo/typescript-config/base.json",
	"compilerOptions": {
		// Override the module and moduleResolution settings
		"module": "ESNext",
		"moduleResolution": "Bundler",
		"target": "ES2022",
		"lib": ["DOM", "ES2022", "DOM.Iterable"],
		// Keep your other settings
		"outDir": "dist",
		"rootDir": "src",
		"composite": true,
		"incremental": true,
		"declaration": true,
		"declarationMap": true,
		"sourceMap": true
	},
	"include": ["src"],
	"exclude": ["node_modules", "dist"]
}
