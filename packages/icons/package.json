{"name": "@repo/icons", "version": "0.0.1", "type": "module", "sideEffects": false, "private": true, "scripts": {"lint": "eslint --max-warnings=0 .", "check-types": "tsc --noEmit", "clean": "rimraf .turbo dist src/components src/assets tsconfig.tsbuildinfo", "build": "node scripts/generate-icons.mjs", "dev": "node --watch-path=./src/svg scripts/generate-icons.mjs"}, "exports": {"./WxIcon/iconCodeLookup": "./src/WxIcon/iconCodeLookup.ts", "./WxIcon": "./src/WxIcon/WxIcon.tsx", "./AlertIcon": "./src/AlertIcon/AlertIcon.tsx", "./BaseIcon": "./src/BaseIcon.tsx", "./*": "./src/components/*/index.ts"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@storybook/react": "catalog:storybook", "@storybook/types": "catalog:storybook", "@svgr/core": "^8.1.0", "@types/node": "catalog:node", "@types/react": "catalog:web", "@types/react-dom": "catalog:web", "eslint": "catalog:dev", "glob": "catalog:dev", "prettier": "catalog:dev", "rimraf": "catalog:dev", "svgo": "^4.0.0", "typescript": "catalog:dev"}, "peerDependencies": {"class-variance-authority": "catalog:web", "next": "catalog:web", "react": "catalog:web", "react-dom": "catalog:web"}, "dependencies": {"@repo/dal": "workspace:*", "@repo/storybook-utils": "workspace:*", "clsx": "catalog:web", "tailwind-merge": "catalog:web"}}