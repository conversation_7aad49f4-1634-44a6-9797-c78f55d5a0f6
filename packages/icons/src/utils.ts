/**
 * Converts a string to PascalCase
 * e.g., "partly-cloudy-day" -> "PartlyCloudyDay"
 */
export function toPascalCase(str: string): string {
	return str
		.split(/[-_\s]+/)
		.map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
		.join('');
}

/**
 * Converts a string to kebab-case
 * e.g., "PartlyCloudyDay" -> "partly-cloudy-day"
 */
export function toKebabCase(str: string): string {
	return str
		.replace(/([a-z])([A-Z])/g, '$1-$2')
		.replace(/\s+/g, '-')
		.toLowerCase();
}
