import { Meta } from "@storybook/blocks";

<Meta title="Icon Package/Documentation" />

# Icon System

The wx-next icon system is designed to provide a consistent set of icons across the application. It follows a similar approach to [Lucide Icons](https://github.com/lucide-icons/lucide), providing a clean, customizable set of icons that can be easily imported and used in React components.

## Icon Structure

Icons are organized by category in the `packages/icons/src/components` directory:

Each icon is implemented as a React component that wraps the SVG content, providing a consistent API for all icons.

## Best Practices

- Consider accessibility when using icons (add aria-label or title when needed). Consider
  overriding default icon title as needed, especially if a translation is needed.
- Prefer using static imports of icon components over dynamically imports.

## Using Icons

### Basic Usage

Icons can be imported directly from their respective categories:

```tsx
import { User, Person } from "@repo/icons/Avatar";
import { Cloudy } from "@repo/icons/Weather";

function MyComponent() {
	return (
		<div>
			<User />
			<Person />
			<Cloudy />
		</div>
	);
}
```

### Icon Properties

All icons accept the following props:

- All standard SVG attributes (`width`, `height`, `viewBox`, etc.)
- All standard React props (`className`, `style`, etc.)
- `color`: Sets the stroke color of the icon
- `size`: Sets both width and height of the icon

### Special Icon Components

#### WxIcon

The `WxIcon` component is a special component that dynamically imports weather condition icons based on a skycode:

```tsx
import { WxIcon } from "@repo/icons/WxIcon";

function WeatherDisplay({ skycode }) {
	return (
		<div>
			<WxIcon skycode={skycode} className="h-8 w-8" />
		</div>
	);
}
```

#### AlertIcon

The `AlertIcon` component is used for displaying weather alert icons based on alert data or a specified alertLevel:

```tsx
import { AlertIcon } from "@repo/icons/AlertIcon";

function AlertDisplay({ alertType }) {
	return (
		<>
			<AlertIcon
				className="h-8 w-8"
				productIdentifier="TOR"
				phenomena={Phenomena.TORNADO}
				significance={Significance.WARNING}
			/>
			<AlertIcon
				className="h-8 w-8"
				alertLevel={1} //Severe
			/>
		</>
	);
}
```

## Adding New Icons

To add a new icon to the system:

- Create an SVG file in the appropriate category directory under `packages/icons/src/assets`
- Run the icon generation script: `node packages/icons/scripts/generate-icons.mjs`
- The script will create a React component for the icon in the corresponding category directory

## Implementation Details

### BaseIcon Component

All icons are built using the `BaseIcon` component, which provides a consistent wrapper around SVG content:

```tsx
import React from "react";
import { BaseIcon, type BaseIconProps } from "../../BaseIcon";
import CloudySVG from "../../assets/skycode/cloudy.svg";

export const Cloudy = React.forwardRef<
	SVGSVGElement,
	Omit<BaseIconProps, "icon">
>((props, ref) => <BaseIcon ref={ref} icon={CloudySVG} {...props} />);

Cloudy.displayName = "Cloudy";
```

### Icon Generation

Icons are generated using a script that:

1. Scans the `assets` directory for SVG files
2. Creates React components that wrap the SVG content
3. Organizes the components by category
4. Creates index files for easy importing

## Comparison with Lucide Icons

Our icon system is inspired by Lucide Icons but customized for our specific needs:

- **Categorization**: Icons are organized by domain-specific categories
- **Special Components**: WxIcon and AlertIcon provide domain-specific functionality
- **SVG Handling**: SVGs are imported directly rather than inlined
- **Component Structure**: Uses a BaseIcon wrapper for consistent behavior

## Future Improvements

- Incorporate SVGO optimization in the build script
- Integrate build script with Turbopack.
- Refactor existing lucide and SVG icon imports
