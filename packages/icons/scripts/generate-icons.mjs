import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { glob } from 'glob';
import prettier from 'prettier';
import { optimize } from 'svgo';
import { transform } from '@svgr/core';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const SVG_DIR = path.join(__dirname, '../src/svg');
const ASSETS_DIR = path.join(__dirname, '../src/assets');
const COMPONENTS_DIR = path.join(__dirname, '../src/components');

// SVGO Configuration
// Base configuration with common settings
const baseConfig = {
	plugins: [
		{
			name: 'preset-default',
			params: {
				overrides: {
					// Keep viewBox attribute
					removeDesc: false, // Keep original descriptions
					// Remove IDs by default
					cleanupIds: true,
					// Remove XML instructions
					removeXMLProcInst: true,
					// Remove comments
					removeComments: true,
					// Remove metadata
					removeMetadata: true,
					// Remove editor data
					removeEditorsNSData: true,
					// Remove hidden elements
					removeHiddenElems: true,
					// Remove empty attributes
					removeEmptyAttrs: true,
					// Remove empty containers
					removeEmptyContainers: true,
					// Minify styles
					minifyStyles: true,
					// Collapse useless groups
					collapseGroups: true,
					// Round numeric values
					convertPathData: {
						floatPrecision: 3,
					},
				},
			},
		},
		// Remove width/height attributes to make icons scalable
		'removeDimensions',
	],
};

// Category-specific configurations
const svgoConfigs = {
	// Default configuration for most icons
	default: {
		...baseConfig,
		plugins: [
			...baseConfig.plugins,
			// Add convertColors plugin to default config
			{
				name: 'convertColors',
				params: {
					currentColor: true,
				},
			},
		],
	},

	// Category-specific overrides
	skycode: {
		...baseConfig,
		// No additional plugins - we specifically don't want color conversion
	},
};

/**
 * Optimizes an SVG file using SVGO with category-specific configuration
 * @param {string} svgContent - The SVG content to optimize
 * @param {string} category - The icon category
 * @returns {Promise<{data: string, info: object}>} - The optimized SVG content and optimization info
 */
async function optimizeSvg(svgContent, category) {
	const config = svgoConfigs[category] || svgoConfigs.default;

	try {
		const result = optimize(svgContent, {
			path: undefined, // Path is optional
			...config,
		});
		return result;
	} catch (error) {
		console.error(`Error optimizing SVG in category ${category}:`, error);
		return { data: svgContent, info: { error: true } };
	}
}

// Ensure directories exist
if (!fs.existsSync(COMPONENTS_DIR)) {
	fs.mkdirSync(COMPONENTS_DIR, { recursive: true });
}

// Clean assets directory before generating
if (fs.existsSync(ASSETS_DIR)) {
	fs.rmSync(ASSETS_DIR, { recursive: true, force: true });
}
fs.mkdirSync(ASSETS_DIR, { recursive: true });

async function generateIcons() {
	// Generate icon components
	console.log('Generating icon components...');
	const svgFiles = await glob('**/*.svg', { cwd: SVG_DIR });

	// Group by category
	const categorizedFiles = svgFiles.reduce((acc, file) => {
		const [category, filename] = file.split('/');
		if (!acc[category]) acc[category] = [];
		if (filename) acc[category].push(filename);
		return acc;
	}, {});

	// Track optimization statistics
	const stats = {
		totalFiles: svgFiles.length,
		optimizedFiles: 0,
		totalSizeBefore: 0,
		totalSizeAfter: 0,
		skippedFiles: 0,
	};

	// Process each category
	for (const [category, files] of Object.entries(categorizedFiles)) {
		// Create category directory
		const categoryDir = path.join(COMPONENTS_DIR, category);
		if (!fs.existsSync(categoryDir)) {
			fs.mkdirSync(categoryDir, { recursive: true });
		}

		// Generate individual icon files
		const iconNames = [];

		for (const filename of files) {
			const iconName = path.basename(filename, '.svg');
			const pascalCaseName = iconName
				.split('-')
				.map((part) => {
					// Convert leading numbers to text values
					if (/^\d/.test(part)) {
						const numberWords = {
							0: 'Zero',
							1: 'One',
							2: 'Two',
							3: 'Three',
							4: 'Four',
							5: 'Five',
							6: 'Six',
							7: 'Seven',
							8: 'Eight',
							9: 'Nine',
						};
						const firstChar = part.charAt(0);
						const restOfPart = part.slice(1);
						return (
							numberWords[firstChar] +
							restOfPart.charAt(0).toUpperCase() +
							restOfPart.slice(1)
						);
					}
					return part.charAt(0).toUpperCase() + part.slice(1);
				})
				.join('');

			iconNames.push(pascalCaseName);

			// Read the original SVG
			const svgPath = path.join(SVG_DIR, category, filename);
			const svgContent = fs.readFileSync(svgPath, 'utf8');

			// Track original size
			stats.totalSizeBefore += svgContent.length;

			// Optimize SVG with category-specific configuration
			const { data: optimizedSvg } = await optimizeSvg(svgContent, category);

			// Track optimized size
			stats.totalSizeAfter += optimizedSvg.length;
			stats.optimizedFiles++;

			// Convert optimized SVG string to React component code using SVGR
			const svgrComponentCode = await transform(
				optimizedSvg,
				{
					typescript: true,
					jsxRuntime: 'automatic',
					icon: false, // Use 'false' to get a standard SVG component, not one sized to 1em
					ref: true, // Ensures SVGR uses React.forwardRef
					titleProp: true,
					descProp: true,
					plugins: ['@svgr/plugin-jsx'],
					exportType: 'named',
					// We don't need to control the export name from SVGR itself,
					// as we will grab its internal 'ForwardRef' const.
				},
				// The 'state' (3rd arg) can name the internal unwrapped component if needed,
				// but SVGR seems to default to 'SvgComponent' for that.
				// Let's ensure the internal unwrapped component is uniquely named to avoid clashes if multiple SVGR blocks were in one file.
				{ componentName: `${pascalCaseName}SvgInternal` },
			);

			// --- START STRING MANIPULATION TO REMOVE XMLNS FROM DIV ---
			let processedSvgrComponentCode = svgrComponentCode; // Use 'let' to allow modification
			if (typeof processedSvgrComponentCode === 'string') {
				// Remove xmlns="http://www.w3.org/1999/xhtml" from div elements
				// This regex looks for '<div', any characters not '>', then the xmlns attribute, then any characters not '>', then '>'
				// It captures the parts before and after the xmlns attribute to reconstruct the tag without it.
				processedSvgrComponentCode = processedSvgrComponentCode.replace(
					/(<div[^>]*?)\s+xmlns="http:\/\/www\.w3\.org\/1999\/xhtml"([^>]*?>)/g,
					'$1$2',
				);
			} else {
				console.warn(
					`WARNING for ${pascalCaseName} (${filename}): svgrComponentCode was not a string (type: ${typeof processedSvgrComponentCode}). Skipping xmlns removal. Value:`,
					processedSvgrComponentCode,
				);
			}
			// --- END STRING MANIPULATION ---

			// Ensure assets category directory exists
			const assetDir = path.join(ASSETS_DIR, category);
			if (!fs.existsSync(assetDir)) {
				fs.mkdirSync(assetDir, { recursive: true });
			}

			// Write optimized SVG to assets directory
			fs.writeFileSync(path.join(assetDir, filename), optimizedSvg);

			// Generate icon component content
			const iconContent = `
import React from 'react';
import { BaseIcon, type BaseIconProps } from '../../BaseIcon';

// --- START SVGR Generated Code ---
${processedSvgrComponentCode}
// --- END SVGR Generated Code ---

        /**
         * ${pascalCaseName} icon
         * This icon was automatically generated, optimized with SVGO, and converted to a React component with SVGR.
         * Optimization profile: ${svgoConfigs[category] ? category : 'default'}
         */
        export const ${pascalCaseName} = React.forwardRef<SVGSVGElement, Omit<BaseIconProps, 'icon'>>((props, ref) => (
          <BaseIcon ref={ref} icon={ForwardRef} {...props} />
        ));

        ${pascalCaseName}.displayName = '${pascalCaseName}';
`;

			// Format and write the file
			const formattedIconContent = await prettier.format(iconContent, {
				parser: 'typescript',
				singleQuote: true,
			});

			fs.writeFileSync(
				path.join(categoryDir, `${pascalCaseName}.tsx`),
				formattedIconContent,
			);
		}

		// Generate category index file
		const categoryIndexContent = `
      ${iconNames.map((name) => `export { ${name} } from './${name}';`).join('\n')}
    `;

		const formattedCategoryIndex = await prettier.format(categoryIndexContent, {
			parser: 'typescript',
			singleQuote: true,
		});

		fs.writeFileSync(
			path.join(categoryDir, 'index.ts'),
			formattedCategoryIndex,
		);
	}

	const categories = Object.keys(categorizedFiles);

	// Log optimization statistics
	const sizeSavedBytes = stats.totalSizeBefore - stats.totalSizeAfter;
	const sizeSavedPercentage = (
		(sizeSavedBytes / stats.totalSizeBefore) *
		100
	).toFixed(2);

	console.log(`Generated icon components for ${categories.length} categories`);
	console.log(`Source SVGs read from: ${SVG_DIR}`);
	console.log(`Optimized SVGs written to: ${ASSETS_DIR}`);
	console.log(`Optimization statistics:`);
	console.log(`- Total files processed: ${stats.totalFiles}`);
	console.log(`- Files optimized: ${stats.optimizedFiles}`);
	console.log(`- Files skipped: ${stats.skippedFiles}`);
	console.log(
		`- Total size before: ${(stats.totalSizeBefore / 1024).toFixed(2)} KB`,
	);
	console.log(
		`- Total size after: ${(stats.totalSizeAfter / 1024).toFixed(2)} KB`,
	);
	console.log(
		`- Size saved: ${(sizeSavedBytes / 1024).toFixed(2)} KB (${sizeSavedPercentage}%)`,
	);
}

generateIcons().catch(console.error);
