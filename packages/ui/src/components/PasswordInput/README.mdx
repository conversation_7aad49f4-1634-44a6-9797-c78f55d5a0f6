# PasswordInput Component

A secure, accessible password input component with visibility toggle functionality. Built on top of the NewInput component.

## Features

- **Password Visibility Toggle**: Show/hide password with a button click
- **Floating Label**: Label animates to indicate focus and filled states (inherited from NewInput)
- **Error Handling**: Displays error messages and visual indicators
- **Accessibility**: Proper ARIA attributes for screen readers
- **Form Integration**: Compatible with react-hook-form
- **Customization**: Supports all standard HTML input attributes except 'type'

## Usage

### Basic Usage

```tsx
import { PasswordInput } from "@repo/ui/components/PasswordInput";

export default function LoginForm() {
	return <PasswordInput label="Password" name="password" required />;
}
```

### With Error State

```tsx
<PasswordInput
	label="Password"
	name="password"
	error="Password must be at least 8 characters"
/>
```

### With Custom Label

```tsx
<PasswordInput label="Current Password" name="current-password" />
```

### With Form Integration (using react-hook-form)

```tsx
import { useForm } from "react-hook-form";
import { PasswordInput } from "@repo/ui/components/PasswordInput";

export default function LoginForm() {
	const {
		register,
		handleSubmit,
		formState: { errors },
	} = useForm();

	const onSubmit = (data) => {
		console.log(data);
	};

	return (
		<form onSubmit={handleSubmit(onSubmit)}>
			<PasswordInput
				label="Password"
				error={errors.password?.message}
				{...register("password", {
					required: "Password is required",
					minLength: {
						value: 8,
						message: "Password must be at least 8 characters",
					},
				})}
			/>
			<button type="submit">Login</button>
		</form>
	);
}
```

## Props

| Prop       | Type                          | Default      | Description                              |
| ---------- | ----------------------------- | ------------ | ---------------------------------------- |
| `label`    | `string`                      | `'Password'` | The label text for the input             |
| `error`    | `string`                      | `undefined`  | Error message to display below the input |
| `...props` | `Omit<NewInputProps, 'type'>` | -            | All props from NewInput except 'type'    |

## States

The component handles several visual states:

- **Default**: Standard appearance with password hidden
- **Visible**: Password text is visible when toggle button is clicked
- **Focused**: Black border with floating label
- **Filled**: Black border with floating label
- **Error**: Red border with error message and icon
- **Disabled**: Grayed out appearance

## Accessibility

- Uses proper label association with `htmlFor` and `id`
- Includes `aria-invalid` when in error state
- Toggle button has appropriate icon for current state
- Supports keyboard navigation

## Implementation Details

### Password Visibility Toggle

The component uses a state variable `showPassword` to track whether the password should be displayed as plain text or masked. When the toggle button is clicked, it:

1. Toggles the `showPassword` state
2. Changes the input type between 'password' and 'text'
3. Updates the icon to reflect the current state

### Preventing Clear Button

The PasswordInput component intentionally prevents the clear button from appearing (which would normally be shown by the NewInput component when a value is present) by not passing the resetField prop to the underlying NewInput.

### Inheritance from NewInput

The PasswordInput component inherits most of its functionality from the NewInput component, including:

- Floating label behavior
- Error state handling
- Focus management
- Styling and appearance

## Browser Compatibility

- Works in all modern browsers
- Responsive across device sizes
- Supports dark mode with appropriate color adjustments

{/* 
Interactive examples using MDX capabilities
*/}

import { PasswordInput } from "./PasswordInput";
import { useState } from "react";

## Live Examples

export const PasswordExample = () => {
	const [value, setValue] = useState("");

    return (
    	<PasswordInput
    		label="Password"
    		name="password-example"
    		value={value}
    		onChange={(e) => setValue(e.target.value)}
    	/>
    );

};

export const PasswordWithErrorExample = () => {
	const [value, setValue] = useState("");

    return (
    	<PasswordInput
    		label="Password"
    		name="password-error-example"
    		value={value}
    		onChange={(e) => setValue(e.target.value)}
    		error="Password must be at least 8 characters"
    	/>
    );

};

export const DisabledPasswordExample = () => (
	<PasswordInput
		label="Disabled Password"
		name="password-disabled"
		disabled
		defaultValue="password123"
	/>
);

<div className="space-y-6">
	<div>
		<h3 className="mb-2 text-lg font-medium">Default Password Input</h3>
		<PasswordExample />
	</div>

    <div>
    	<h3 className="mb-2 text-lg font-medium">With Error</h3>
    	<PasswordWithErrorExample />
    </div>

    <div>
    	<h3 className="mb-2 text-lg font-medium">Disabled</h3>
    	<DisabledPasswordExample />
    </div>

    <div>
    	<h3 className="mb-2 text-lg font-medium">Custom Label</h3>
    	<PasswordInput label="Current Password" name="current-password-example" />
    </div>

</div>
