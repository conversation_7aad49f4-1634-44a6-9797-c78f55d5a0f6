'use client';

import React, { useState } from 'react';
import { Input, InputProps } from '../Input/Input';
import { Button } from '../Button/Button';
import { ViewOff, View } from '@repo/icons/components/Toggle';

export interface PasswordInputProps extends Omit<InputProps, 'type'> {
	// No need to include 'type' as it will be managed internally
}

export const PasswordInput = React.forwardRef<
	HTMLInputElement,
	PasswordInputProps
>(({ className, label = 'Password', error, ...props }, ref) => {
	const [showPassword, setShowPassword] = useState(false);

	const togglePasswordVisibility = () => {
		setShowPassword(!showPassword);
	};

	return (
		<div className="relative">
			<Input
				label={label}
				type={showPassword ? 'text' : 'password'}
				error={error}
				ref={ref}
				{...props}
				onClear={undefined}
			/>
			<Button
				type="button"
				onClick={togglePasswordVisibility}
				variant="ghost"
				size="text"
				data-testid="password-toggle-button"
				className="absolute right-0 top-2 flex items-center hover:bg-transparent"
			>
				{showPassword ? (
					<ViewOff className="h-4 w-4 text-gray-500" />
				) : (
					<View className="h-4 w-4 text-gray-500" />
				)}
			</Button>
		</div>
	);
});

PasswordInput.displayName = 'PasswordInput';
