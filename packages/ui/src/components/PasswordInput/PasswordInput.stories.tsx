import type { Meta, StoryObj } from '@storybook/react';
import { PasswordInput } from './PasswordInput';
import { expect, userEvent, within } from '@storybook/test';

const meta: Meta<typeof PasswordInput> = {
	title: 'UI/Password Input',
	component: PasswordInput,
	tags: ['autodocs'],
	argTypes: {
		label: {
			control: 'text',
			description: 'The label text for the password input field',
			defaultValue: 'Password',
		},
		error: {
			control: 'text',
			description: 'Error message to display below the input',
		},
		disabled: {
			control: 'boolean',
			description: 'Whether the input is disabled',
		},
		placeholder: {
			control: 'text',
			description: 'Placeholder text for the input',
		},
	},
	parameters: {
		docs: {
			description: {
				component:
					'A password input component with toggle visibility functionality. Built on top of the NewInput component.',
			},
		},
	},
};

export default meta;
type Story = StoryObj<typeof PasswordInput>;

/**
 * This story showcases all states of the PasswordInput component in a single view,
 * making it easy to compare different states side-by-side.
 */
export const PasswordInputStates: Story = {
	name: 'Password Input States',
	render: () => (
		<div className="space-y-8">
			<h2 className="text-xl font-bold">Password Input States</h2>
			<div className="grid grid-cols-2 gap-6">
				<div>
					<h3 className="mb-2 font-medium">Default</h3>
					<PasswordInput label="Default Password" name="default" />
				</div>
				<div>
					<h3 className="mb-2 font-medium">Focused</h3>
					<PasswordInput label="Focused Password" name="focused" autoFocus />
				</div>
				<div>
					<h3 className="mb-2 font-medium">With Value</h3>
					<PasswordInput
						label="Filled Password"
						name="filled"
						value="SecurePassword123"
					/>
				</div>
				<div>
					<h3 className="mb-2 font-medium">Disabled</h3>
					<PasswordInput label="Disabled Password" name="disabled" disabled />
				</div>
				<div>
					<h3 className="mb-2 font-medium">With Error</h3>
					<PasswordInput
						label="Error Password"
						name="error"
						error="Password must be at least 8 characters"
					/>
				</div>
				<div>
					<h3 className="mb-2 font-medium">With Value and Error</h3>
					<PasswordInput
						label="Error Password with Value"
						name="error-value"
						value="123"
						error="Password must be at least 8 characters"
					/>
				</div>
			</div>
		</div>
	),
};

/**
 * This story tests the password visibility toggle functionality.
 * It verifies that clicking the show/hide password button changes the input type
 * between "password" and "text".
 */
export const PasswordVisibilityToggle: Story = {
	name: 'Password Visibility Toggle',
	render: () => (
		<div className="w-80">
			<PasswordInput
				label="Test Password"
				name="test-password"
				value="SecurePassword123"
				data-testid="password-input"
			/>
		</div>
	),
	play: async ({ canvasElement, step }) => {
		const canvas = within(canvasElement);

		// Get the password input and toggle button
		const passwordInput = canvas.getByTestId('password-input');
		const toggleButton = canvas.getByTestId('password-toggle-button');

		if (!toggleButton) {
			throw new Error('Toggle button not found');
		}

		// Initial state should be type="password" (hidden)
		await step('Verify initial password is hidden', () => {
			expect(passwordInput.getAttribute('type')).toBe('password');
		});

		// Click the toggle button to show the password
		await step('Click toggle button to show password', async () => {
			await userEvent.click(toggleButton);

			// Password should now be visible (type="text")
			expect(passwordInput.getAttribute('type')).toBe('text');
		});

		// Click the toggle button again to hide the password
		await step('Click toggle button to hide password', async () => {
			await userEvent.click(toggleButton);

			// Password should be hidden again (type="password")
			expect(passwordInput.getAttribute('type')).toBe('password');
		});
	},
};
