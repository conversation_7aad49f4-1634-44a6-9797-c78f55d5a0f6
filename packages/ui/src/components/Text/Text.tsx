import React, { ReactNode } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@repo/ui/lib/utils';

export interface TextProps extends VariantProps<typeof textVariants> {
	elementType?: 'p' | 'span' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'div';
	className?: string;
	children: ReactNode;
}

const Text = React.forwardRef<HTMLElement, TextProps>(
	(
		{ className, variant, elementType, lineClamp, color, children, ...props },
		ref,
	) => {
		const Element =
			elementType || (getDefaultTag({ variant }) as React.ElementType);

		return (
			<Element
				className={cn(textVariants({ variant, color, lineClamp, className }))}
				ref={ref}
				{...props}
			>
				{children}
			</Element>
		);
	},
);

Text.displayName = 'Text';

export { Text, textVariants };

export default Text;

const textVariants = cva('', {
	variants: {
		variant: {
			'Display.XL': ['text-6xl', 'leading-relaxed', 'tracking-tighter'],
			'Display.L': [
				'text-5xl',
				'leading-normal',
				'tracking-tighter',
				'font-medium',
			],
			'Display.M': [
				'text-4xl',
				'leading-tight',
				'tracking-tight',
				'font-medium',
			],
			'Display.S': [
				'text-xl',
				'leading-relaxed',
				'tracking-tight',
				'font-medium',
			],
			'Title.XL': [
				'text-4xl',
				'leading-snug',
				'tracking-tight',
				'font-semibold',
			],
			'Title.L': ['text-3xl', 'leading-tight', 'tracking-tight', 'font-medium'],
			'Title.M': [
				'text-2xl',
				'leading-tight',
				'tracking-tight',
				'font-semibold',
			],
			'Title.S': [
				'text-xl',
				'leading-tight',
				'tracking-tight',
				'font-semibold',
			],
			'Body.L': ['text-lg', 'leading-looser', 'tracking-normal', 'font-medium'],
			'Body.L.Bold': [
				'text-lg',
				'leading-looser',
				'tracking-normal',
				'font-bold',
			],
			'Body.M': [
				'text-base',
				'leading-looser',
				'tracking-tight',
				'font-medium',
			],
			'Body.M.Bold': [
				'text-base',
				'leading-looser',
				'tracking-tight',
				'font-bold',
			],
			'Body.S': ['text-sm', 'leading-normal', 'tracking-tight', 'font-medium'],
			'Body.S.Bold': [
				'text-sm',
				'leading-normal',
				'tracking-normal',
				'font-bold',
			],
			'Caption.M': [
				'text-xs',
				'leading-normal',
				'tracking-normal',
				'font-medium',
			],
			'Caption.S': [
				'text-xxs',
				'leading-loose',
				'tracking-tight',
				'font-medium',
			],
		},
		color: {
			primary: 'text-gray-900',
			secondary: 'text-gray-500',
			default: 'text-gray-900',
			disabled: 'text-gray-400',
			placeholder: 'text-gray-600',
			brandDark: 'text-brand-dark',
			brandLight: 'text-brand-light',
			inverse: 'text-white',
			error: 'text-[var(--color-alert-severe)]',
		},
		lineClamp: {
			1: 'line-clamp-1',
			2: 'line-clamp-2',
			3: 'line-clamp-3',
			4: 'line-clamp-4',
		},
	},
	defaultVariants: {
		variant: 'Body.M',
		color: 'default',
	},
});

function getDefaultTag({
	variant,
}: {
	variant?: VariantProps<typeof textVariants>['variant'];
}) {
	switch (variant) {
		case 'Display.XL':
		case 'Display.L':
		case 'Title.XL':
			return 'h1';
		case 'Display.M':
		case 'Display.S':
		case 'Title.L':
		case 'Title.M':
			return 'h2';
		case 'Title.S':
			return 'h3';
		case 'Body.L':
		case 'Body.M':
		case 'Body.S':
		case 'Caption.M':
		case 'Caption.S':
		default:
			return 'p';
	}
}
