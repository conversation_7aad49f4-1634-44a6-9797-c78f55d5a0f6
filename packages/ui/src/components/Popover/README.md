# Popover Component

The Popover component provides a way to display floating content when a trigger element is clicked or hovered. It's built on top of Radix UI's Popover primitive with added support for hover triggers.

## Features

- Click trigger (default behavior)
- Hover trigger with configurable delays
- Customizable positioning
- Accessible by default (thanks to <PERSON><PERSON><PERSON>)
- Animated transitions

## Usage

### Basic Usage with Click Trigger (<PERSON><PERSON>ult)

```tsx
import {
	Popover,
	PopoverTrigger,
	PopoverContent,
} from "@repo/ui/components/Popover/Popover";

<Popover>
	<PopoverTrigger>Click me</PopoverTrigger>
	<PopoverContent>
		This content appears when the trigger is clicked.
	</PopoverContent>
</Popover>;
```

### Hover Trigger

```tsx
import {
	Popover,
	PopoverTrigger,
	PopoverContent,
} from "@repo/ui/components/Popover/Popover";

<Popover trigger="hover">
	<PopoverTrigger>Hover me</PopoverTrigger>
	<PopoverContent>
		This content appears when the trigger is hovered.
	</PopoverContent>
</Popover>;
```

### Configuring Hover Delays

You can configure the delay before the popover opens on hover and the delay before it closes when the cursor leaves:

```tsx
<Popover
	trigger="hover"
	openDelay={200} // 200ms delay before opening
	closeDelay={500} // 500ms delay before closing
>
	<PopoverTrigger>Hover me</PopoverTrigger>
	<PopoverContent>
		This content appears after a 200ms delay and disappears after a 500ms delay.
	</PopoverContent>
</Popover>
```

### With Custom Trigger Element

You can use the `asChild` prop to use a custom element as the trigger:

```tsx
<Popover trigger="hover">
	<PopoverTrigger asChild>
		<Button>Hover over this button</Button>
	</PopoverTrigger>
	<PopoverContent>Popover content</PopoverContent>
</Popover>
```

## API Reference

### Popover

The root component that manages the state of the popover.

| Prop           | Type                      | Default   | Description                                       |
| -------------- | ------------------------- | --------- | ------------------------------------------------- |
| `trigger`      | `'click' \| 'hover'`      | `'click'` | The event that triggers the popover               |
| `openDelay`    | `number`                  | `0`       | Delay in milliseconds before opening on hover     |
| `closeDelay`   | `number`                  | `300`     | Delay in milliseconds before closing on hover out |
| `open`         | `boolean`                 | -         | Controlled open state                             |
| `onOpenChange` | `(open: boolean) => void` | -         | Callback when open state changes                  |
| `defaultOpen`  | `boolean`                 | `false`   | Default open state (uncontrolled)                 |

### PopoverTrigger

The element that triggers the popover.

| Prop      | Type      | Default | Description                                |
| --------- | --------- | ------- | ------------------------------------------ |
| `asChild` | `boolean` | `false` | Whether to render the trigger as its child |

### PopoverContent

The content of the popover.

| Prop         | Type                           | Default    | Description                                      |
| ------------ | ------------------------------ | ---------- | ------------------------------------------------ |
| `align`      | `'start' \| 'center' \| 'end'` | `'center'` | Alignment of the popover relative to the trigger |
| `sideOffset` | `number`                       | `4`        | Distance in pixels from the trigger              |
| `className`  | `string`                       | -          | Additional CSS classes                           |

### PopoverAnchor

An optional anchor element to position the popover against, instead of the trigger.

## Accessibility

The popover component follows WAI-ARIA guidelines:

- Click-triggered popovers are fully keyboard accessible
- Hover-triggered popovers should be used for non-essential information, as they may be difficult to access for keyboard and screen reader users
- For critical information or actions, prefer click-triggered popovers

## Examples

See `popover.stories.tsx` for more examples of how to use the Popover component.
