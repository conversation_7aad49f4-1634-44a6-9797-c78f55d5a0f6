import React from 'react';
import { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Popover, PopoverContent, PopoverTrigger } from './Popover';
import { Button } from '../Button/Button';
import { expect, within, userEvent } from '@storybook/test';

const meta: Meta<typeof Popover> = {
	title: 'UI/Popover',
	component: Popover,
	tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof Popover>;

/**
 * Consolidated story showing multiple Popover variants in one view.
 * This follows the Story Consolidation Pattern to reduce the number of
 * Chromatic screenshots while providing a comprehensive view of the component's capabilities.
 */
export const PopoverVariants: Story = {
	name: 'Popover Variants',
	render: () => (
		<div className="space-y-8">
			{/* Click Trigger Section */}
			<div>
				<h2 className="mb-4 text-xl font-bold">Click Trigger</h2>
				<p className="text-muted-foreground mb-4 text-sm">
					Default click trigger
				</p>
				<div className="flex flex-col">
					<Popover>
						<PopoverTrigger asChild>
							<Button className="w-30">Click Me</Button>
						</PopoverTrigger>
						<PopoverContent className="w-80">
							<div className="grid gap-4">
								<div className="space-y-2">
									<h4 className="font-medium leading-none">
										Click-triggered Popover
									</h4>
									<p className="text-muted-foreground text-sm">
										This popover is triggered by a click event.
									</p>
								</div>
							</div>
						</PopoverContent>
					</Popover>
				</div>
			</div>

			{/* Hover Trigger Section */}
			<div>
				<h2 className="mb-4 text-xl font-bold">Hover Triggers</h2>
				<div className="flex flex-col gap-8">
					{/* Standard Hover */}
					<div className="flex flex-col">
						<p className="text-muted-foreground mb-4 text-sm">
							Standard delay (200ms/300ms)
						</p>
						<Popover trigger="hover" openDelay={200} closeDelay={300}>
							<PopoverTrigger asChild>
								<Button className="w-30">Hover Me</Button>
							</PopoverTrigger>
							<PopoverContent className="w-80">
								<div className="grid gap-4">
									<div className="space-y-2">
										<h4 className="font-medium leading-none">
											Hover-triggered Popover
										</h4>
										<p className="text-muted-foreground text-sm">
											This popover is triggered by a hover event with 200ms open
											delay and 300ms close delay.
										</p>
									</div>
								</div>
							</PopoverContent>
						</Popover>
					</div>

					{/* No Delay Hover */}
					<div className="flex flex-col">
						<p className="text-muted-foreground mb-4 text-sm">
							No delay (0ms/0ms)
						</p>
						<Popover trigger="hover" openDelay={0} closeDelay={0}>
							<PopoverTrigger asChild>
								<Button className="w-30">Hover Me</Button>
							</PopoverTrigger>
							<PopoverContent className="w-80">
								<div className="grid gap-4">
									<div className="space-y-2">
										<h4 className="font-medium leading-none">
											Instant Hover Popover
										</h4>
										<p className="text-muted-foreground text-sm">
											This popover is triggered by a hover event with no delay.
										</p>
									</div>
								</div>
							</PopoverContent>
						</Popover>
					</div>
				</div>
			</div>
		</div>
	),
};

/**
 * This story includes interaction tests for the click-triggered Popover.
 * It demonstrates how to test that clicking the trigger button opens the popover content.
 */
export const ClickInteraction: Story = {
	name: 'Click Interaction Test',
	render: () => (
		<div className="p-8">
			<Popover>
				<PopoverTrigger asChild>
					<Button data-testid="popover-trigger">Click to Open Popover</Button>
				</PopoverTrigger>
				<PopoverContent className="w-80" data-testid="popover-content">
					<div className="grid gap-4">
						<div className="space-y-2">
							<h4 className="font-medium leading-none">
								Click-triggered Popover
							</h4>
							<p className="text-muted-foreground text-sm">
								This popover is triggered by a click event.
							</p>
						</div>
					</div>
				</PopoverContent>
			</Popover>
		</div>
	),
	play: async ({ canvasElement, step }) => {
		const canvas = within(canvasElement);

		// Initially, the popover content should not be visible in the document body
		await expect(
			document.querySelector('[data-testid="popover-content"]'),
		).toBeNull();

		// Click the trigger button
		await step('Click the trigger button', async () => {
			const triggerButton = canvas.getByTestId('popover-trigger');
			await userEvent.click(triggerButton);
		});

		// After clicking, the popover content should be visible
		// We need to look in document.body since the popover is rendered in a portal
		await step('Verify popover content is visible', async () => {
			// Small delay to allow for animations
			await new Promise((resolve) => setTimeout(resolve, 100));

			// Find the popover content in the document body
			const popoverContent = document.querySelector(
				'[data-testid="popover-content"]',
			);
			await expect(popoverContent).not.toBeNull();
			await expect(popoverContent).toBeVisible();

			// Verify the content has the expected data-state attribute
			await expect(popoverContent?.getAttribute('data-state')).toBe('open');

			// Verify the content text is correct
			if (popoverContent) {
				const contentText = popoverContent.textContent;
				await expect(contentText).toContain('Click-triggered Popover');
			}
		});
	},
};

/**
 * This story includes interaction tests for the hover-triggered Popover.
 * It demonstrates how to test that hovering over the trigger opens the popover content
 * and moving the mouse away closes it, respecting the configured delays.
 */
export const HoverInteraction: Story = {
	name: 'Hover Interaction Test',
	render: () => (
		<div className="p-8">
			<Popover trigger="hover" openDelay={100} closeDelay={200}>
				<PopoverTrigger asChild>
					<Button data-testid="hover-trigger">Hover to Open Popover</Button>
				</PopoverTrigger>
				<PopoverContent className="w-80" data-testid="hover-content">
					<div className="grid gap-4">
						<div className="space-y-2">
							<h4 className="font-medium leading-none">
								Hover-triggered Popover
							</h4>
							<p className="text-muted-foreground text-sm">
								This popover is triggered by a hover event with 100ms open delay
								and 200ms close delay.
							</p>
						</div>
					</div>
				</PopoverContent>
			</Popover>
		</div>
	),
	play: async ({ canvasElement, step }) => {
		const canvas = within(canvasElement);

		// Initially, the popover content should not be visible
		await expect(
			document.querySelector('[data-testid="hover-content"]'),
		).toBeNull();

		// Hover over the trigger button
		await step('Hover over the trigger button', async () => {
			const triggerButton = canvas.getByTestId('hover-trigger');
			await userEvent.hover(triggerButton);
		});

		// After hovering and waiting for the openDelay, the popover content should be visible
		await step('Verify popover content appears after hover delay', async () => {
			// Wait for the openDelay (100ms) plus a small buffer
			await new Promise((resolve) => setTimeout(resolve, 150));

			// Find the popover content in the document body
			const popoverContent = document.querySelector(
				'[data-testid="hover-content"]',
			);
			await expect(popoverContent).not.toBeNull();
			await expect(popoverContent).toBeVisible();

			// Verify the content has the expected data-state attribute
			await expect(popoverContent?.getAttribute('data-state')).toBe('open');

			// Verify the content text is correct
			if (popoverContent) {
				const contentText = popoverContent.textContent;
				await expect(contentText).toContain('Hover-triggered Popover');
			}
		});

		// Move mouse away from the trigger
		await step('Move mouse away from trigger', async () => {
			const triggerButton = canvas.getByTestId('hover-trigger');
			await userEvent.unhover(triggerButton);
		});

		// After moving away and waiting for the closeDelay, the popover should be hidden
		await step(
			'Verify popover content disappears after close delay',
			async () => {
				// Wait for the closeDelay (500ms) plus a small buffer
				await new Promise((resolve) => setTimeout(resolve, 500));

				// The popover content should either be null (removed from DOM) or have data-state="closed"
				const popoverContent = document.querySelector(
					'[data-testid="hover-content"]',
				);

				// Check if the element is removed or has closed state
				if (popoverContent === null) {
					await expect(popoverContent).toBeNull();
				} else {
					await expect(popoverContent.getAttribute('data-state')).toBe(
						'closed',
					);
					await expect(popoverContent).not.toBeVisible();
				}
			},
		);
	},
};
