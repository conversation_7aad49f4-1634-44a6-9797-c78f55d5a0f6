'use client';

import * as React from 'react';
import * as PopoverPrimitive from '@radix-ui/react-popover';

import { cn } from '@repo/ui/lib/utils';

type PopoverProps = React.ComponentProps<typeof PopoverPrimitive.Root> & {
	trigger?: 'click' | 'hover';
	openDelay?: number;
	closeDelay?: number;
};

// Create a context to pass the trigger type to children
const PopoverContext = React.createContext<{
	trigger: 'click' | 'hover';
	onMouseEnter: () => void;
	onMouseLeave: () => void;
}>({
	trigger: 'click',
	onMouseEnter: () => {},
	onMouseLeave: () => {},
});

function Popover({
	trigger = 'click',
	openDelay = 0,
	closeDelay = 300,
	open,
	onOpenChange,
	...props
}: PopoverProps) {
	const [isOpen, setIsOpen] = React.useState(open || false);
	const openTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);
	const closeTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

	// Sync controlled state with uncontrolled state
	React.useEffect(() => {
		if (open !== undefined) {
			setIsOpen(open);
		}
	}, [open]);

	const handleOpenChange = React.useCallback(
		(newOpen: boolean) => {
			setIsOpen(newOpen);
			onOpenChange?.(newOpen);
		},
		[onOpenChange],
	);

	// Clean up timeouts on unmount
	React.useEffect(() => {
		return () => {
			if (openTimeoutRef.current) clearTimeout(openTimeoutRef.current);
			if (closeTimeoutRef.current) clearTimeout(closeTimeoutRef.current);
		};
	}, []);

	// For hover trigger, we need to handle the open state ourselves
	const handleMouseEnter = React.useCallback(() => {
		if (trigger !== 'hover') return;

		if (closeTimeoutRef.current) {
			clearTimeout(closeTimeoutRef.current);
			closeTimeoutRef.current = null;
		}

		if (!isOpen) {
			openTimeoutRef.current = setTimeout(() => {
				handleOpenChange(true);
			}, openDelay);
		}
	}, [trigger, isOpen, openDelay, handleOpenChange]);

	const handleMouseLeave = React.useCallback(() => {
		if (trigger !== 'hover') return;

		if (openTimeoutRef.current) {
			clearTimeout(openTimeoutRef.current);
			openTimeoutRef.current = null;
		}

		closeTimeoutRef.current = setTimeout(() => {
			handleOpenChange(false);
		}, closeDelay);
	}, [trigger, closeDelay, handleOpenChange]);

	const contextValue = React.useMemo(
		() => ({
			trigger,
			onMouseEnter: handleMouseEnter,
			onMouseLeave: handleMouseLeave,
		}),
		[trigger, handleMouseEnter, handleMouseLeave],
	);

	return (
		<PopoverContext.Provider value={contextValue}>
			<PopoverPrimitive.Root
				data-slot="popover"
				open={isOpen}
				onOpenChange={handleOpenChange}
				{...props}
			/>
		</PopoverContext.Provider>
	);
}

function PopoverTrigger({
	...props
}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {
	const { trigger, onMouseEnter, onMouseLeave } =
		React.useContext(PopoverContext);

	const triggerProps =
		trigger === 'hover' ? { onMouseEnter, onMouseLeave } : {};

	return (
		<PopoverPrimitive.Trigger
			data-slot="popover-trigger"
			{...triggerProps}
			{...props}
		/>
	);
}

function PopoverArrow({
	...props
}: React.ComponentProps<typeof PopoverPrimitive.Arrow>) {
	return <PopoverPrimitive.Arrow data-slot="popover-arrow" {...props} />;
}

function PopoverContent({
	className,
	align = 'center',
	sideOffset = 4,
	children,
	showArrow = true,
	...props
}: React.ComponentProps<typeof PopoverPrimitive.Content> & {
	showArrow?: boolean;
}) {
	const { trigger, onMouseEnter, onMouseLeave } =
		React.useContext(PopoverContext);

	const contentProps =
		trigger === 'hover' ? { onMouseEnter, onMouseLeave } : {};

	return (
		<PopoverPrimitive.Portal>
			<PopoverPrimitive.Content
				data-slot="popover-content"
				align={align}
				sideOffset={sideOffset}
				className={cn(
					'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-(--radix-popover-content-transform-origin) outline-hidden z-50 w-72 rounded-md border-none p-4 shadow-[0_2px_12px_0_#0003]',
					className,
				)}
				{...contentProps}
				{...props}
			>
				{showArrow && (
					<PopoverPrimitive.Arrow
						className="border-none fill-white"
						height={12}
						width={20}
					/>
				)}
				{children}
			</PopoverPrimitive.Content>
		</PopoverPrimitive.Portal>
	);
}

function PopoverAnchor({
	...props
}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {
	return <PopoverPrimitive.Anchor data-slot="popover-anchor" {...props} />;
}

export { Popover, PopoverTrigger, PopoverContent, PopoverAnchor, PopoverArrow };
