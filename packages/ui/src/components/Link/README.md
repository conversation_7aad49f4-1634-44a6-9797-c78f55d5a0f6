# Link Component and Navigation Utilities

This directory contains the Link component and navigation utilities for the wx-next application.

## Components and Hooks

### `Link` Component

A custom Link component that can render either a Next.js Link or a regular anchor tag.

```tsx
import Link from '@/components/Link';

// MPA mode (default) - renders as <a> tag
<Link href="/about">About</Link>

// SPA mode - uses Next.js Link
<Link href="/about" mpa={false}>About</Link>
```

#### Props

| Prop      | Type                                    | Default  | Description                                                    |
| --------- | --------------------------------------- | -------- | -------------------------------------------------------------- |
| `href`    | `string`                                | Required | The URL to navigate to                                         |
| `mpa`     | `boolean`                               | `true`   | When true, renders as `<a>` tag. When false, uses Next.js Link |
| `...rest` | `React.ComponentProps<typeof NextLink>` | -        | All other props are passed to the underlying component         |

### `usePageNavigation` Hook

A custom hook for programmatic page navigation that supports both MPA and SPA navigation modes.

```tsx
// Import directly from the hooks folder
import { usePageNavigation } from "@/repo/ui/hooks/usePageNavigation";

const MyComponent = () => {
	const { navigate, refresh } = usePageNavigation();

	return (
		<div>
			<button onClick={() => navigate("/about")}>Go to About (MPA)</button>

			<button onClick={() => navigate("/about", { mpa: false })}>
				Go to About (SPA)
			</button>

			<button onClick={() => refresh()}>Refresh Page (MPA)</button>
		</div>
	);
};
```

#### Return Value

| Property   | Type         | Description                            |
| ---------- | ------------ | -------------------------------------- |
| `navigate` | `function`   | Navigate to a new page                 |
| `refresh`  | `function`   | Refresh the current page               |
| `router`   | `NextRouter` | The underlying Next.js router instance |

#### `navigate` Function

```tsx
navigate(href: string, options?: { mpa?: boolean; replace?: boolean }): void
```

| Parameter         | Type      | Default  | Description                                                               |
| ----------------- | --------- | -------- | ------------------------------------------------------------------------- |
| `href`            | `string`  | Required | The URL to navigate to                                                    |
| `options.mpa`     | `boolean` | `true`   | When true, uses `window.location`. When false, uses Next.js router        |
| `options.replace` | `boolean` | `false`  | When true, replaces the current history entry instead of adding a new one |

#### `refresh` Function

```tsx
refresh(options?: { mpa?: boolean }): void
```

| Parameter     | Type      | Default | Description                                                                     |
| ------------- | --------- | ------- | ------------------------------------------------------------------------------- |
| `options.mpa` | `boolean` | `true`  | When true, uses `window.location.reload()`. When false, uses `router.refresh()` |

## Usage Examples

### Basic Link Usage

```tsx
import Link from '@/components/Link';

// MPA navigation (default)
<Link href="/weather/today">Today's Weather</Link>

// SPA navigation
<Link href="/weather/today" mpa={false}>Today's Weather</Link>
```

### Programmatic Navigation

```tsx
import { usePageNavigation } from "@/repo/ui/hooks/usePageNavigation";

const WeatherNavigation = () => {
	const { navigate } = usePageNavigation();

	const goToTodaysForecast = () => {
		navigate("/weather/today");
	};

	const goToHourlyForecast = () => {
		navigate("/weather/hourly", { mpa: false });
	};

	return (
		<div>
			<button onClick={goToTodaysForecast}>Today's Forecast (MPA)</button>
			<button onClick={goToHourlyForecast}>Hourly Forecast (SPA)</button>
		</div>
	);
};
```

### Page Refresh

```tsx
import { usePageNavigation } from "@/repo/ui/hooks/usePageNavigation";

const RefreshButton = () => {
	const { refresh } = usePageNavigation();

	return <button onClick={() => refresh()}>Refresh Page (MPA)</button>;
};
```

### Form Submission with Navigation

```tsx
import { usePageNavigation } from "@/repo/ui/hooks/usePageNavigation";

const SearchForm = () => {
	const { navigate } = usePageNavigation();
	const [query, setQuery] = useState("");

	const handleSubmit = (e) => {
		e.preventDefault();
		navigate(`/search?q=${encodeURIComponent(query)}`);
	};

	return (
		<form onSubmit={handleSubmit}>
			<input
				type="text"
				value={query}
				onChange={(e) => setQuery(e.target.value)}
			/>
			<button type="submit">Search</button>
		</form>
	);
};
```

### Advanced Usage with Router Access

```tsx
import { usePageNavigation } from "@/repo/ui/hooks/usePageNavigation";

const AdvancedNavigation = () => {
	const { router } = usePageNavigation();

	// Access the underlying Next.js router for advanced use cases
	const handlePrefetch = () => {
		router.prefetch("/dashboard");
	};

	return <button onClick={handlePrefetch}>Prefetch Dashboard</button>;
};
```

## MPA vs SPA Navigation

This component and hook support two navigation modes:

1. **MPA (Multi-Page Application)** - Default mode

   - Uses `window.location` for navigation
   - Causes a full page reload
   - Resets React state
   - Better for SEO and initial page loads

2. **SPA (Single-Page Application)**
   - Uses Next.js router for client-side navigation
   - Preserves React state
   - Smoother user experience for subsequent navigations
   - Better for interactive applications

Choose the appropriate mode based on your specific use case.
