import React from 'react';
import { render, screen, fireEvent, within } from '@testing-library/react';
import { TabList } from './TabList';
import { describe, it, expect, vi } from 'vitest';
import '@testing-library/jest-dom';

describe('TabList Component', () => {
	// Sample tabs data for testing
	const sampleTabs = [
		{ id: 'tab1', label: 'Tab 1' },
		{ id: 'tab2', label: 'Tab 2' },
		{ id: 'tab3', label: 'Tab 3' },
	];

	it('renders correctly with default props', () => {
		const handleTabChange = vi.fn();

		render(
			<TabList
				tabs={sampleTabs}
				defaultActiveTab="tab1"
				onTabChange={handleTabChange}
			/>,
		);

		// Check if all tabs are rendered
		expect(screen.getByText('Tab 1')).toBeInTheDocument();
		expect(screen.getByText('Tab 2')).toBeInTheDocument();
		expect(screen.getByText('Tab 3')).toBeInTheDocument();

		// Check if the first tab is active
		const activeTab = screen
			.getByText('Tab 1')
			.closest('[data-state="active"]');
		expect(activeTab).toBeInTheDocument();

		// Filter icon should not be present by default
		const filterIcon = document.querySelector('svg');
		expect(filterIcon).not.toBeInTheDocument();
	});

	it('renders with filter icon when showFilterIcon is true', () => {
		const handleTabChange = vi.fn();

		render(
			<TabList
				tabs={sampleTabs}
				defaultActiveTab="tab1"
				onTabChange={handleTabChange}
				showFilterIcon={true}
			/>,
		);

		// Filter icon should be present
		const filterIcon = document.querySelector('svg');
		expect(filterIcon).toBeInTheDocument();
	});

	it('applies correct size classes for large size', () => {
		const handleTabChange = vi.fn();

		render(
			<TabList
				tabs={sampleTabs}
				defaultActiveTab="tab1"
				onTabChange={handleTabChange}
				size="large"
			/>,
		);

		// Check if the TabsList has the large height class
		const tabsList = document.querySelector('[data-slot="tabs-list"]');
		expect(tabsList).toHaveClass('h-14');

		// Check if the tabs have the large text class
		const tab = screen.getByText('Tab 1').closest('[data-slot="tabs-trigger"]');
		expect(tab).toHaveClass('text-base');
		expect(tab).toHaveClass('py-2.5');
	});

	it('applies correct size classes for small size', () => {
		const handleTabChange = vi.fn();

		render(
			<TabList
				tabs={sampleTabs}
				defaultActiveTab="tab1"
				onTabChange={handleTabChange}
				size="small"
			/>,
		);

		// Check if the TabsList has the small height class
		const tabsList = document.querySelector('[data-slot="tabs-list"]');
		expect(tabsList).toHaveClass('h-10');

		// Check if the tabs have the small text class
		const tab = screen.getByText('Tab 1').closest('[data-slot="tabs-trigger"]');
		expect(tab).toHaveClass('text-sm');
		expect(tab).toHaveClass('py-1.5');
	});

	it('calls onTabChange when a tab is clicked', () => {
		const handleTabChange = vi.fn();

		render(
			<TabList
				tabs={sampleTabs}
				defaultActiveTab="tab1"
				onTabChange={handleTabChange}
			/>,
		);

		// Click on the second tab
		fireEvent.click(screen.getByText('Tab 2'));

		// Check if onTabChange was called with the correct tab id
		expect(handleTabChange).toHaveBeenCalledWith('tab2');
	});

	it('renders the correct active tab', () => {
		const handleTabChange = vi.fn();

		const { container } = render(
			<TabList
				tabs={sampleTabs}
				defaultActiveTab="tab2"
				onTabChange={handleTabChange}
			/>,
		);

		// Check if the second tab is active
		const tabsList = container.querySelector('[role="tablist"]');
		expect(tabsList).not.toBeNull();

		if (tabsList) {
			const activeTab = within(tabsList).getByRole('tab', { selected: true });
			expect(activeTab).toHaveTextContent('Tab 2');
		}
	});

	it('applies custom className when provided', () => {
		const handleTabChange = vi.fn();
		const customClass = 'custom-class';

		const { container } = render(
			<TabList
				tabs={sampleTabs}
				defaultActiveTab="tab1"
				onTabChange={handleTabChange}
				className={customClass}
			/>,
		);

		// Check if the custom class is applied to the root element
		const tabsRoot = container.querySelector('[data-slot="tabs"]');
		expect(tabsRoot).not.toBeNull();
		expect(tabsRoot?.classList.contains(customClass)).toBe(true);
	});

	// Accessibility tests
	describe('Accessibility', () => {
		it('has proper ARIA roles', () => {
			const handleTabChange = vi.fn();

			const { container } = render(
				<TabList
					tabs={sampleTabs}
					defaultActiveTab="tab1"
					onTabChange={handleTabChange}
				/>,
			);

			// Check for tablist role
			const tablist = container.querySelector('[role="tablist"]');
			expect(tablist).not.toBeNull();

			// Check for tab roles
			const tabs = container.querySelectorAll('[role="tab"]');
			expect(tabs.length).toBe(3);

			// Check for proper aria-selected attribute
			const activeTab = container.querySelector('[aria-selected="true"]');
			expect(activeTab).not.toBeNull();
			expect(activeTab?.textContent).toBe('Tab 1');

			const inactiveTabs = container.querySelectorAll(
				'[aria-selected="false"]',
			);
			expect(inactiveTabs.length).toBe(2);
		});
	});
});
