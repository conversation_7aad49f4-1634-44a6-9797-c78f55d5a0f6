import React, { useState } from 'react';
import { Meta, StoryObj } from '@storybook/react';
import { TabList, TabItem } from './TabList';

const meta: Meta<typeof TabList> = {
	title: 'Components/TabList',
	component: TabList,
	tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof TabList>;

// Sample tabs data
const sampleTabs: TabItem[] = [
	{ id: 'tab1', label: 'Short' },
	{ id: 'tab2', label: 'Medium Label' },
	{ id: 'tab3', label: 'Longer Label Text' },
	{ id: 'tab4', label: 'Very Long Label Text Here' },
];

// Many tabs to demonstrate horizontal scrolling
const manyTabs: TabItem[] = [
	{ id: 'tab1', label: 'Tab 1' },
	{ id: 'tab2', label: 'Tab 2' },
	{ id: 'tab3', label: 'Tab 3' },
	{ id: 'tab4', label: 'Tab 4' },
	{ id: 'tab5', label: 'Tab 5' },
	{ id: 'tab6', label: 'Tab 6' },
	{ id: 'tab7', label: 'Tab 7' },
	{ id: 'tab8', label: 'Tab 8' },
	{ id: 'tab9', label: 'Tab 9' },
	{ id: 'tab10', label: 'Tab 10' },
];

/**
 * Consolidated story showing all TabList variants
 * Following the project's Storybook best practices for consolidated stories
 */
export const TabListVariants: Story = {
	name: 'TabList Variants',
	render: () => (
		<div className="w-[900px] space-y-12">
			{/* Size Variants */}
			<div className="space-y-8">
				<h2 className="text-xl font-bold">Size Variants</h2>

				<div className="space-y-6">
					<div>
						<h3 className="mb-2 text-lg">Large</h3>
						<TabList tabs={sampleTabs} size="large" />
					</div>

					<div>
						<h3 className="mb-2 text-lg">Small</h3>
						<TabList tabs={sampleTabs} size="small" />
					</div>
				</div>
			</div>

			{/* Filter Icon Variants */}
			<div className="space-y-8">
				<h2 className="text-xl font-bold">Filter Icon Variants</h2>

				<div className="space-y-6">
					<div>
						<h3 className="mb-2 text-lg">Large with Filter Icon</h3>
						<TabList tabs={sampleTabs} size="large" showFilterIcon={true} />
					</div>

					<div>
						<h3 className="mb-2 text-lg">Small with Filter Icon</h3>
						<TabList tabs={sampleTabs} size="small" showFilterIcon={true} />
					</div>
				</div>
			</div>

			{/* No Filter Icon Variants */}
			<div className="space-y-8">
				<h2 className="text-xl font-bold">Without Filter Icon</h2>

				<div className="space-y-6">
					<div>
						<h3 className="mb-2 text-lg">Large without Filter Icon</h3>
						<TabList tabs={sampleTabs} size="large" showFilterIcon={false} />
					</div>

					<div>
						<h3 className="mb-2 text-lg">Small without Filter Icon</h3>
						<TabList tabs={sampleTabs} size="small" showFilterIcon={false} />
					</div>
				</div>
			</div>
		</div>
	),
};

/**
 * Example with many tabs to demonstrate horizontal scrolling
 */
export const HorizontalScrolling: Story = {
	name: 'Horizontal Scrolling',
	render: () => (
		<div className="space-y-12">
			<div className="space-y-8">
				<h2 className="text-xl font-bold">Horizontal Scrolling</h2>

				<div className="space-y-6">
					<div>
						<h3 className="mb-2 text-lg">Many Tabs</h3>
						<div className="w-[520px] border border-dashed border-gray-300 p-4">
							<TabList tabs={manyTabs} size="large" />
						</div>
					</div>

					<div>
						<h3 className="mb-2 text-lg">Many Tabs (Small Size)</h3>
						<div className="w-[520px] border border-dashed border-gray-300 p-4">
							<TabList tabs={manyTabs} size="small" />
						</div>
					</div>

					<div>
						<h3 className="mb-2 text-lg">Many Tabs with Filter Icon</h3>
						<div className="w-[520px] border border-dashed border-gray-300 p-4">
							<TabList tabs={manyTabs} size="large" showFilterIcon={true} />
						</div>
					</div>
				</div>
			</div>
		</div>
	),
};

/**
 * Example with custom content for each tab
 */
export const TabListWithContent: Story = {
	name: 'Tabs With Content',
	render: () => {
		// Define tabs with custom content
		const customContentTabs = [
			{
				id: 'tab1',
				label: 'Text Content',
				content: (
					<div className="rounded-lg bg-gray-50 p-4">
						<h3 className="mb-2 text-lg font-medium">Custom Text Content</h3>
						<p>This is a custom text content for the first tab.</p>
						<p className="mt-2">You can add any React components here.</p>
					</div>
				),
			},
			{
				id: 'tab2',
				label: 'Component',
				content: (
					<div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
						<div className="mb-3 flex items-center gap-2">
							<div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-500 text-white">
								<span>i</span>
							</div>
							<h3 className="text-lg font-medium">Component Example</h3>
						</div>
						<p>
							This tab demonstrates using a more complex component as content.
						</p>
						<div className="mt-4 flex gap-2">
							<button className="rounded-md bg-blue-500 px-4 py-2 text-white">
								Primary Action
							</button>
							<button className="rounded-md border border-gray-300 px-4 py-2">
								Secondary Action
							</button>
						</div>
					</div>
				),
			},
			{
				id: 'tab3',
				label: 'List Example',
				content: (
					<div className="p-4">
						<h3 className="mb-3 text-lg font-medium">List Example</h3>
						<ul className="list-disc space-y-2 pl-5">
							<li>First item in the list</li>
							<li>
								Second item with <strong>formatted</strong> text
							</li>
							<li>
								Third item with a{' '}
								<a href="#" className="text-blue-500 underline">
									link
								</a>
							</li>
							<li>Fourth item in the list</li>
						</ul>
					</div>
				),
			},
		];

		return (
			<div className="space-y-12">
				<div className="space-y-8">
					<h2 className="text-xl font-bold">Custom Tab Content</h2>

					<div className="space-y-6">
						<div>
							<h3 className="mb-2 text-lg">Tabs with Custom Content</h3>
							<TabList tabs={customContentTabs} size="large" />
						</div>
					</div>
				</div>
			</div>
		);
	},
};

/**
 * Example with external content controlled by tab selection
 * This demonstrates how to use the onTabChange callback to render
 * different content based on the active tab without using the tabs.content property
 */
export const ExternalContent: Story = {
	name: 'External Content',
	render: function ExternalContentExample() {
		// Define tabs without content property
		const tabsWithoutContent = [
			{ id: 'breaking', label: 'Breaking News' },
			{ id: 'local', label: 'Local News' },
			{ id: 'tech', label: 'Technology' },
		];

		// Use state to track the active tab
		const [activeTabId, setActiveTabId] = useState('breaking');

		// Define the type for NewsArticle props
		interface NewsArticleProps {
			title: string;
			summary: string;
			time: string;
			category: string;
			image?: string;
		}

		// Define the type for tab content configuration
		interface TabContentConfig {
			title: string;
			articles: NewsArticleProps[];
		}

		// Define all news content organized by category
		const newsContentByTab: Record<string, TabContentConfig> = {
			breaking: {
				title: 'Breaking News',
				articles: [
					{
						title: 'Major Storm System Approaching',
						summary:
							'A powerful storm system is expected to bring heavy rain and strong winds to the region starting tomorrow.',
						time: '10 min ago',
						category: 'Weather Alert',
						image: '🌪️',
					},
					{
						title: 'City Council Approves New Infrastructure Plan',
						summary:
							'The city council has unanimously approved a $2 billion infrastructure plan to improve roads and public transportation.',
						time: '45 min ago',
						category: 'Politics',
						image: '🏙️',
					},
					{
						title: 'Stock Market Reaches Record High',
						summary:
							'The stock market closed at a record high today, with technology and energy sectors leading the gains.',
						time: '2 hours ago',
						category: 'Finance',
						image: '📈',
					},
				],
			},
			local: {
				title: 'Local News',
				articles: [
					{
						title: 'New Community Center Opening Next Month',
						summary:
							'The long-awaited community center will open its doors next month with free activities for residents during the first week.',
						time: '3 hours ago',
						category: 'Community',
						image: '🏫',
					},
					{
						title: 'Local Restaurant Wins National Award',
						summary:
							"Downtown's favorite bistro has been recognized as one of the top 50 restaurants in the country by Food & Dining magazine.",
						time: 'Yesterday',
						category: 'Food',
						image: '🍽️',
					},
					{
						title: 'Weekend Festival Expected to Draw Large Crowds',
						summary:
							'The annual arts and music festival returns this weekend with over 100 vendors and performances across five stages.',
						time: 'Yesterday',
						category: 'Events',
						image: '🎭',
					},
				],
			},
			tech: {
				title: 'Technology News',
				articles: [
					{
						title: 'New Smartphone Announced with Revolutionary Camera',
						summary:
							'The latest flagship smartphone features a groundbreaking camera system that promises to transform mobile photography.',
						time: '5 hours ago',
						category: 'Devices',
						image: '📱',
					},
					{
						title: 'AI Research Breakthrough in Natural Language Processing',
						summary:
							'Scientists have developed a new algorithm that significantly improves machine understanding of context in conversations.',
						time: 'Yesterday',
						category: 'AI',
						image: '🤖',
					},
					{
						title: 'Major Tech Companies Announce Sustainability Initiatives',
						summary:
							'Leading technology firms have committed to ambitious carbon neutrality goals and renewable energy investments.',
						time: '2 days ago',
						category: 'Environment',
						image: '🌱',
					},
				],
			},
		};

		// Content to render based on active tab
		const renderContent = () => {
			// Find the selected tab configuration
			const selectedTabContent = newsContentByTab[activeTabId];

			// If no tab is selected or the tab doesn't exist, show a default message
			if (!selectedTabContent) {
				return <div>Select a tab to see content</div>;
			}

			// Render the selected tab content using the configuration
			return (
				<div className="rounded-lg p-4">
					<h3 className="mb-2 text-lg font-medium">
						{selectedTabContent.title}
					</h3>
					<div className="space-y-3">
						{selectedTabContent.articles.map((article, index) => (
							<>
								<div
									key={index}
									className="rounded-md border border-gray-200 bg-white p-3"
								>
									<div className="flex items-start gap-3">
										{article.image && (
											<div className="h-16 w-16 flex-shrink-0 overflow-hidden rounded-md bg-gray-100">
												<div className="flex h-full w-full items-center justify-center text-2xl">
													{article.image}
												</div>
											</div>
										)}
										<div className="flex-1">
											<div className="flex items-center justify-between">
												<span className="rounded-full bg-gray-100 px-2 py-0.5 text-xs text-gray-600">
													{article.category}
												</span>
												<span className="text-xs text-gray-500">
													{article.time}
												</span>
											</div>
											<h4 className="mt-1 font-medium">{article.title}</h4>
											<p className="text-sm text-gray-600">{article.summary}</p>
										</div>
									</div>
								</div>
							</>
						))}
					</div>
				</div>
			);
		};

		return (
			<div className="space-y-12">
				<div className="space-y-8">
					<h2 className="text-xl font-bold">External Content</h2>
					<p className="text-gray-600">
						This example demonstrates using the TabList component with external
						content rendering. Each tab shows similar content structure (news
						articles) but with different items based on the selected category.
					</p>

					<div className="max-w-[768px] space-y-4">
						<TabList
							tabs={tabsWithoutContent}
							defaultActiveTab="breaking"
							onTabChange={setActiveTabId}
						/>

						<div className="mt-4">{renderContent()}</div>
					</div>
				</div>
			</div>
		);
	},
};
