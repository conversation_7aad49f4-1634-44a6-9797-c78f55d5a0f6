'use client';

import React, { useState } from 'react';
import {
	Tabs,
	Ta<PERSON>Content,
	Ta<PERSON>List,
	TabsTrigger,
} from '@repo/ui/components/tabs';
import { cn } from '@repo/ui/lib/utils';
import { SettingsAdjust } from '@repo/icons/components/Control/SettingsAdjust';

export interface TabItem {
	id: string;
	label: string;
	content?: React.ReactNode;
}

export interface TabListProps {
	/**
	 * Array of tab items to display
	 */
	tabs: TabItem[];

	/**
	 * ID of the initially active tab (optional)
	 * If not provided, the first tab will be active by default
	 */
	defaultActiveTab?: string;

	/**
	 * Callback function when a tab is selected (optional)
	 */
	onTabChange?: (tabId: string) => void;

	/**
	 * Size variant of the tabs
	 * @default 'small'
	 */
	size?: 'small' | 'large';

	/**
	 * Whether to show the filter icon
	 * @default false
	 */
	showFilterIcon?: boolean;

	/**
	 * Additional CSS classes
	 */
	className?: string;
}

/**
 * TabList component for horizontal filter rows
 *
 * Follows WAI-ARIA best practices for tabs:
 * - Uses proper ARIA roles (tablist, tab)
 * - Supports keyboard navigation
 * - Manages focus appropriately
 */
export const TabList: React.FC<TabListProps> = ({
	tabs,
	defaultActiveTab,
	onTabChange,
	size = 'small',
	showFilterIcon,
	className,
}) => {
	// Internal state for active tab
	const [activeTab, setActiveTab] = useState<string>(
		defaultActiveTab || tabs?.[0]?.id || '',
	);

	// Handle tab change
	const handleTabChange = (value: string) => {
		setActiveTab(value);

		if (onTabChange) {
			onTabChange(value);
		}
	};

	const heightClass = {
		'h-10': size === 'small',
		'h-12': size === 'large',
	};

	return (
		<Tabs
			value={activeTab}
			onValueChange={handleTabChange}
			className={cn('w-full', className)}
		>
			<div
				className={cn(
					'flex items-center gap-2 p-1',
					size === 'small' ? 'h-10' : 'h-14',
				)}
			>
				{showFilterIcon && (
					<div
						className={cn(
							'flex aspect-square shrink-0 items-center justify-center rounded-lg bg-gray-100',
							heightClass,
						)}
					>
						<SettingsAdjust
							className={cn('text-gray-600', {
								'h-4 w-4': size === 'small',
								'h-5 w-5': size === 'large',
							})}
						/>
					</div>
				)}

				<div className="scrollbar-hide overflow-x-auto">
					<TabsList
						className={cn('flex w-fit gap-2 bg-transparent p-0', heightClass)}
					>
						{tabs.map((tab) => (
							<TabsTrigger
								key={tab.id}
								value={tab.id}
								className={cn(
									'flex-shrink-0 cursor-pointer whitespace-nowrap rounded-lg py-3',
									'data-[state=active]:bg-brand-400 data-[state=active]:text-white',
									'data-[state=inactive]:bg-gray-100 data-[state=inactive]:text-gray-600',
									{
										'px-3 text-sm': size === 'small',
										'px-4 text-base': size === 'large',
									},
								)}
							>
								{tab.label}
							</TabsTrigger>
						))}
					</TabsList>
				</div>
			</div>

			<div>
				{tabs.map(
					(tab) =>
						tab.content && (
							<TabsContent key={tab.id} value={tab.id}>
								{tab.content}
							</TabsContent>
						),
				)}
			</div>
		</Tabs>
	);
};

export default TabList;
