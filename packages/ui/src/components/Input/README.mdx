# Input Component

A modern, accessible text input component with floating label, error state handling, and clear button functionality.

## Features

- **Floating Label**: Label animates to indicate focus and filled states
- **Error Handling**: Displays error messages and visual indicators
- **Clear Button**: Optional button to clear input content
- **Accessibility**: Proper ARIA attributes for screen readers
- **Form Integration**: Compatible with react-hook-form
- **Customization**: Supports all standard HTML input attributes

## Usage

### Basic Usage

```tsx
import { Input } from "@repo/ui/components/Input";

export default function MyForm() {
	return <Input label="Email Address" name="email" type="email" required />;
}
```

### With Error State

```tsx
<Input
	label="Email Address"
	name="email"
	error="Please enter a valid email address"
/>
```

### With Clear Button

```tsx
import { useState } from "react";
import { Input } from "@repo/ui/components/Input";

export default function MyForm() {
	const [email, setEmail] = useState("");

	const handleClear = () => {
		setEmail("");
	};

	return (
		<form>
			<Input
				label="Email Address"
				name="email"
				value={email}
				onChange={(e) => setEmail(e.target.value)}
				onClear={handleClear}
			/>
		</form>
	);
}
```

## Props

| Prop       | Type                                    | Default     | Description                                       |
| ---------- | --------------------------------------- | ----------- | ------------------------------------------------- |
| `label`    | `string`                                | (required)  | The label text for the input                      |
| `error`    | `string`                                | `undefined` | Error message to display below the input          |
| `onClear`  | `() => void`                            | `undefined` | Function to call when the clear button is clicked |
| `...props` | `InputHTMLAttributes<HTMLInputElement>` | -           | All standard HTML input attributes                |

## States

The component handles several visual states:

- **Default**: Standard appearance with gray border
- **Focused**: Black border with floating label
- **Filled**: Black border with floating label
- **Error**: Red border with error message and icon
- **Disabled**: Grayed out appearance

## Accessibility

- Uses proper label association with `htmlFor` and `id`
- Includes `aria-invalid` when in error state
- Clear button has `aria-label` for screen readers
- Supports keyboard navigation

## Implementation Details

### Floating Label

The floating label is implemented using CSS transforms and the peer selector in Tailwind. When the input is focused or has a value, the label animates to a position above the input.

### Error Handling

Error states are visually indicated with:

- Red border color on the input
- Red label text when the input is focused or filled
- Error icon appearing inline next to the label when focused or filled
- Error message displayed below the input

### Clear Button

The clear button appears when:

1. The input has a value
2. An `onClear` function is provided

When clicked, it:

1. Clears the input value
2. Resets focus state
3. Calls the provided `onClear` function

## Browser Compatibility

- Works in all modern browsers
- Responsive across device sizes
- Supports dark mode with appropriate color adjustments

{/* 
You can add interactive examples here using MDX capabilities.
For example:
*/}

import { Input } from "./Input";

## Live Examples

<div className="space-y-6">
	<div>
		<h3 className="mb-2 text-lg font-medium">Default Input</h3>
		<Input label="Default Input" name="default" />
	</div>

    <div>
    	<h3 className="mb-2 text-lg font-medium">With Error</h3>
    	<Input
    		label="Email Address"
    		name="email-error"
    		type="email"
    		error="Please enter a valid email address"
    	/>
    </div>

    <div>
    	<h3 className="mb-2 text-lg font-medium">With Clear Button</h3>
    	<Input
    		label="Clearable Input"
    		name="clearable"
    		defaultValue="Type something here"
    		onClear={() => {}}
    	/>
    </div>

    <div>
    	<h3 className="mb-2 text-lg font-medium">Disabled</h3>
    	<Input
    		label="Disabled Input"
    		name="disabled"
    		disabled
    		defaultValue="Cannot edit this"
    	/>
    </div>

</div>
