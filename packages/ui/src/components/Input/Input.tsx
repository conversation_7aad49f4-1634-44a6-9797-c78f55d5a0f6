import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { Text, textVariants } from '../Text/Text';
import { Warning } from '@repo/icons/Status';
import { Close } from '@repo/icons/Navigation';
import { cn } from '@repo/ui/lib/utils';

export interface InputProps
	extends React.InputHTMLAttributes<HTMLInputElement> {
	label: string;
	error?: string;
	onClear?: () => void;
	'data-testid'?: string;
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
	(
		{
			className,
			label,
			error,
			onClear,
			autoComplete,
			name,
			disabled,
			value,
			onFocus,
			onBlur,
			onChange,
			type,
			...props
		},
		ref,
	) => {
		const [isFocused, setIsFocused] = React.useState(false);
		const [isFilled, setIsFilled] = React.useState(!!value);

		const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
			setIsFocused(true);
			onFocus?.(e);
		};

		const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
			setIsFocused(false);
			onBlur?.(e);
		};

		const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
			setIsFilled(!!e.target.value);
			onChange?.(e);
		};

		const handleOnClear = () => {
			setIsFocused(false);
			setIsFilled(false);
			onClear?.();
		};

		let inputStatusVariant: VariantProps<typeof inputVariantsConfig>['status'];
		let labelStatusVariant: VariantProps<typeof labelVariantsConfig>['status'];
		let determinedLabelColor: VariantProps<typeof labelTextStyles>['color'];

		const isActive = isFocused || isFilled;
		const showClearButton = isFilled && !!onClear;
		const labelPositionVariant = isActive ? 'active' : 'default';

		switch (true) {
			case disabled:
				inputStatusVariant = 'disabled';
				labelStatusVariant = 'disabled';
				determinedLabelColor = 'disabled';
				break;
			case !!error:
				inputStatusVariant = 'error';
				labelStatusVariant = isActive ? 'error' : 'default';
				determinedLabelColor = isActive ? 'error' : 'placeholder'; // If error but not active, label text is not error color
				break;
			case isFilled: // and not disabled, and no error
				inputStatusVariant = 'filled';
				labelStatusVariant = 'default';
				determinedLabelColor = 'primary';
				break;
			default: // not disabled, no error, not filled
				inputStatusVariant = 'default';
				labelStatusVariant = 'default';
				determinedLabelColor = isActive ? 'primary' : 'placeholder'; // If focused but empty: primary, else disabled
		}

		return (
			<div className="relative h-[67px]">
				<input
					id={name}
					name={name}
					disabled={disabled}
					value={value}
					aria-invalid={!!error}
					ref={ref}
					type={type ?? 'text'}
					onFocus={handleFocus}
					onBlur={handleBlur}
					onChange={handleChange}
					autoComplete={autoComplete}
					data-testid={props['data-testid']}
					className={cn(
						inputVariantsConfig({
							status: inputStatusVariant,
							hasClearButton: showClearButton,
						}),
						className,
					)}
					placeholder=" "
				/>
				<label
					htmlFor={name}
					className={cn(
						labelVariantsConfig({
							status: labelStatusVariant,
							position: labelPositionVariant,
						}),
					)}
				>
					<span className="flex items-center">
						<Text
							variant={isActive ? 'Body.S' : 'Body.L'}
							color={
								labelTextStyles({
									color: determinedLabelColor,
								}) as VariantProps<typeof labelTextStyles>['color']
							}
						>
							{label}
						</Text>
						{error && isActive && (
							<Warning
								className="ml-1 h-3 w-3 text-[var(--color-alert-severe)] dark:text-[var(--color-alert-severe)]"
								aria-hidden="true"
							/>
						)}
					</span>
				</label>
				{isFilled && onClear && (
					<button
						type="button"
						onClick={handleOnClear}
						className="absolute right-0 top-0 cursor-pointer p-1"
						aria-label="Clear input"
					>
						<Close className="h-4 w-4" />
					</button>
				)}
				{error && (
					<Text variant="Caption.S" color="error" className="mt-1">
						{error}
					</Text>
				)}
			</div>
		);
	},
);

Input.displayName = 'Input';

const labelTextStyles = cva('', {
	variants: {
		color: {
			disabled: 'disabled',
			placeholder: 'placeholder',
			error: 'error',
			primary: 'primary',
		},
	},
});

const inputVariantsConfig = cva(
	[
		textVariants({ variant: 'Body.L' }),
		'peer block w-full appearance-none border-b bg-transparent px-0 pt-2 placeholder-gray-300 focus:outline-none focus:ring-0 dark:text-black dark:placeholder-gray-300',
	],
	{
		variants: {
			status: {
				default:
					'border-[#25242220] focus:border-black dark:border-[#25242220] dark:focus:border-black',
				error:
					'border-[var(--color-alert-severe)] focus:border-[var(--color-alert-severe)] dark:border-[var(--color-alert-severe)] dark:focus:border-[var(--color-alert-severe)]',
				filled:
					'border-black focus:border-black dark:border-black dark:focus:border-black',
				disabled: 'cursor-not-allowed border-[#F2F2F2] dark:border-[#F2F2F2]',
			},
			hasClearButton: {
				true: 'pr-8',
				false: '',
			},
		},
		defaultVariants: {
			status: 'default',
			hasClearButton: false,
		},
	},
);

const labelVariantsConfig = cva(
	'absolute top-0 z-10 origin-[0] transform duration-300 peer-focus:start-0 rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:text-gray-400',
	{
		variants: {
			status: {
				default:
					'text-[#545351] peer-focus:text-blue-600 dark:text-[#545351] dark:peer-focus:text-gray-500',
				error:
					'text-[var(--color-alert-severe)] peer-focus:text-[var(--color-alert-severe)] dark:text-[var(--color-alert-severe)] dark:peer-focus:text-[var(--color-alert-severe)]',
				disabled: 'text-[#8D8D8D] dark:text-[#8D8D8D]',
			},
			position: {
				default: 'translate-y-0 scale-100',
				active: '-translate-y-[0.5rem] scale-75',
			},
		},
		defaultVariants: {
			status: 'default',
			position: 'default',
		},
	},
);
