import type { Meta, StoryObj } from '@storybook/react';
import { Input } from './Input';
import { useState } from 'react';
// TODO: These aren't needed but need to be fixed.
// icon prop is not a valid property for Input
// import { Home } from '@repo/icons/Navigation';
// import { Search } from '@repo/icons/Operations';

const meta: Meta<typeof Input> = {
	title: 'UI/Input',
	component: Input,
	tags: ['autodocs'],
	argTypes: {
		label: {
			control: 'text',
			description: 'The label text for the input field',
		},
		error: {
			control: 'text',
			description: 'Error message to display below the input',
		},
		disabled: {
			control: 'boolean',
			description: 'Whether the input is disabled',
		},
		placeholder: {
			control: 'text',
			description: 'Placeholder text for the input',
		},
	},
	parameters: {
		docs: {
			description: {
				component:
					'A text input component with floating label and error state handling.',
			},
		},
	},
};

export default meta;
type Story = StoryObj<typeof Input>;

/**
 * This story showcases all states of the Input component in a single view,
 * making it easy to compare different states side-by-side.
 */
export const InputStates: Story = {
	name: 'Input States',
	render: () => {
		const [filledValue, setFilledValue] = useState('Input value');
		const [errorValue, setErrorValue] = useState('Invalid value');

		const handleClearFilled = () => {
			setFilledValue('');
		};

		const handleClearErrorValue = () => {
			setErrorValue('');
		};

		return (
			<div className="space-y-8">
				<h2 className="text-xl font-bold">Input States</h2>
				<div className="grid grid-cols-2 gap-6">
					<div>
						<h3 className="mb-2 font-medium">Default</h3>
						<Input label="Default Input" name="default" />
					</div>
					<div>
						<h3 className="mb-2 font-medium">Focused</h3>
						<Input label="Focused Input" name="focused" autoFocus />
					</div>
					<div>
						<h3 className="mb-2 font-medium">With Value</h3>
						<Input
							label="Filled Input"
							name="filled"
							value={filledValue}
							onChange={(e) => setFilledValue(e.target.value)}
							onClear={handleClearFilled}
						/>
					</div>
					<div>
						<h3 className="mb-2 font-medium">Disabled</h3>
						<Input label="Disabled Input" name="disabled" disabled />
					</div>
					<div>
						<h3 className="mb-2 font-medium">With Error</h3>
						<Input
							label="Error Input"
							name="error"
							error="This field is required"
						/>
					</div>
					<div>
						<h3 className="mb-2 font-medium">With Value and Error</h3>
						<Input
							label="Error Input with Value"
							name="error-value"
							value={errorValue}
							onChange={(e) => setErrorValue(e.target.value)}
							onClear={handleClearErrorValue}
							error="Please enter a valid value"
						/>
					</div>
				</div>

				<h2 className="mt-8 text-xl font-bold">Input with Icons</h2>
				<div className="mt-4 grid grid-cols-2 gap-6">
					<div>
						<h3 className="mb-2 font-medium">With Home Icon</h3>
						<Input
							label="Address"
							name="address"
							// icon={<Home className="h-4 w-4 text-gray-500" />}
						/>
					</div>
					<div>
						<h3 className="mb-2 font-medium">With Search Icon</h3>
						<Input
							label="Search"
							name="search"
							// icon={<Search className="h-4 w-4 text-gray-500" />}
						/>
					</div>
					<div>
						<h3 className="mb-2 font-medium">With Icon and Value</h3>
						<Input
							label="Home Address"
							name="address-filled"
							value="123 Main St"
							// icon={<Home className="h-4 w-4 text-gray-500" />}
							onClear={() => {}}
						/>
					</div>
					<div>
						<h3 className="mb-2 font-medium">With Icon and Error</h3>
						<Input
							label="Home Address"
							name="address-error"
							value="invalid address"
							// icon={<Home className="h-4 w-4 text-gray-500" />}
							error="Please enter a valid address"
							onClear={() => {}}
						/>
					</div>
				</div>
			</div>
		);
	},
};
