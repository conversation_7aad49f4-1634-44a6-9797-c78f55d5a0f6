'use client';

import { useRouter } from 'next/navigation';

/**
 * Custom hook for page navigation that supports both MPA and SPA navigation modes.
 *
 * @returns Navigation methods for programmatic navigation
 * @example
 * // In a component:
 * const { navigate, refresh } = usePageNavigation();
 *
 * // MPA navigation (full page load)
 * navigate('/about', { mpa: true });
 *
 * // SPA navigation (client-side)
 * navigate('/about', { mpa: false });
 *
 * // Refresh current page
 * refresh();
 */
export const usePageNavigation = () => {
	const router = useRouter();

	/**
	 * Navigate to a new page
	 * @param href - The URL to navigate to
	 * @param options - Navigation options
	 */
	const navigate = (
		href: string,
		options: {
			mpa?: boolean;
			replace?: boolean;
		} = {},
	) => {
		const { mpa = true, replace = false } = options;

		if (mpa) {
			// MPA navigation using window.location
			if (replace) {
				window.location.replace(href);
			} else {
				window.location.href = href;
			}
		} else {
			// SPA navigation using Next.js router
			if (replace) {
				router.replace(href);
			} else {
				router.push(href);
			}
		}
	};

	/**
	 * Refresh the current page
	 * @param options - Refresh options
	 */
	const refresh = (options: { mpa?: boolean } = {}) => {
		const { mpa = true } = options;

		if (mpa) {
			// MPA refresh using window.location
			window.location.reload();
		} else {
			// SPA refresh using Next.js router
			router.refresh();
		}
	};

	return {
		navigate,
		refresh,
		// Expose the underlying router for advanced use cases
		router,
	};
};

export default usePageNavigation;
