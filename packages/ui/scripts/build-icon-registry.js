#!/usr/bin/env node

/**
 * Icon Registry Builder
 *
 * This script scans the SVG files in the Icon assets directory and generates
 * a TypeScript registry file with paths to all available icons.
 *
 * The registry is organized by category (subdirectory) and icon name (filename).
 * Icons are chunked by category with evenly distributed chunks.
 */

import fs from 'fs';
import path from 'path';
import * as glob from 'glob';
import { fileURLToPath } from 'url';

// Create __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const iconDir = path.resolve(__dirname, '../src/components/Icon/assets');
const outputFile = path.resolve(
	__dirname,
	'../src/components/IconV2/registry.ts',
);

// Chunking configuration
const MAX_CHUNK_SIZE = 20; // Maximum icons per chunk
const EXCLUDED_FROM_CHUNKING = []; //['skycode']; // Categories excluded from chunking

// Storage for our registry
const registry = {};
const categoryIconCounts = {}; // Track icon count per category

console.log('Building icon registry...');
console.log(`Scanning directory: ${iconDir}`);

// Process the icon directory
const svgFiles = glob.sync(`${iconDir}/**/*.svg`);

console.log(`Found ${svgFiles.length} SVG files`);

svgFiles.forEach((file) => {
	const relativePath = path.relative(iconDir, file);
	const parts = relativePath.split(path.sep);

	// First part is the category (subdirectory)
	const category = parts.length > 1 ? parts[0] : 'misc';

	// Last part is the file name without extension
	const fileName = path.basename(parts[parts.length - 1], '.svg');

	// Create import path relative to the registry file location
	// We need to go up one directory level and into the Icon directory
	const importPath = `../Icon/assets/${relativePath}`;

	// Initialize category in registry if needed
	if (!registry[category]) {
		registry[category] = {};
		categoryIconCounts[category] = 0;
	}

	// Add to registry
	registry[category][fileName] = importPath;
	categoryIconCounts[category]++;
});

// Calculate optimal chunk distribution for each category
const categoryChunkInfo = {};
Object.entries(categoryIconCounts).forEach(([category, count]) => {
	const isExcluded = EXCLUDED_FROM_CHUNKING.includes(category);

	if (isExcluded || count <= MAX_CHUNK_SIZE) {
		// No chunking needed or excluded from chunking
		categoryChunkInfo[category] = {
			numChunks: 1,
			chunkSize: count,
			distribution: [count],
		};
	} else {
		// Calculate optimal number of chunks
		const numChunks = Math.ceil(count / MAX_CHUNK_SIZE);
		// Calculate optimal chunk size (evenly distributed)
		const optimalChunkSize = Math.ceil(count / numChunks);

		// Create distribution array
		const distribution = [];
		let remainingIcons = count;

		for (let i = 0; i < numChunks; i++) {
			const chunkSize = Math.min(optimalChunkSize, remainingIcons);
			distribution.push(chunkSize);
			remainingIcons -= chunkSize;
		}

		categoryChunkInfo[category] = {
			numChunks,
			chunkSize: optimalChunkSize,
			distribution,
		};
	}
});

// Generate TypeScript file
const tsContent = `
// Auto-generated file. Do not edit manually.
// Generated on ${new Date().toISOString()}

// This file is a registry of icon paths for use with the IconV2 component
// It maps category and icon names to dynamic import functions with webpack chunking

// Icon components registry with dynamic imports
export const iconComponents = {
${Object.entries(registry)
	.map(([category, icons]) => {
		// const chunkInfo = categoryChunkInfo[category];
		// const needsChunking = chunkInfo.numChunks > 1;

		return `  '${category}': {
${Object.entries(icons)
	.map(([name, path]) => {
		// Calculate which chunk this icon belongs to
		// let chunkIndex = 0;
		// if (needsChunking) {
		// 	let iconPosition = 0;
		// 	for (let i = 0; i < chunkInfo.distribution.length; i++) {
		// 		if (
		// 			index >= iconPosition &&
		// 			index < iconPosition + chunkInfo.distribution[i]
		// 		) {
		// 			chunkIndex = i;
		// 			break;
		// 		}
		// 		iconPosition += chunkInfo.distribution[i];
		// 	}
		// }

		// const chunkSuffix = needsChunking ? `-${chunkIndex}` : '';

		return `    '${name}': () => import('${path}'),`;
		// return `    '${name}': () => import(/* webpackChunkName: "icon-${category}${chunkSuffix}" */ '${path}'),`;
	})
	.join('\n')}
  },`;
	})
	.join('\n')}
};

export type IconCategory = keyof typeof iconComponents;
export type IconName<C extends IconCategory> = keyof typeof iconComponents[C];
`;

// Write file
fs.writeFileSync(outputFile, tsContent);
console.log(`Generated icon registry at ${outputFile}`);
console.log(
	`Registry contains ${Object.keys(registry).length} categories and ${svgFiles.length} icons`,
);

// Log chunking information
console.log('\nChunking information:');
Object.entries(categoryChunkInfo).forEach(([category, info]) => {
	const { numChunks, distribution } = info;
	console.log(
		`- ${category}: ${categoryIconCounts[category]} icons in ${numChunks} chunk${numChunks > 1 ? 's' : ''}`,
	);
	if (numChunks > 1) {
		console.log(`  Distribution: ${distribution.join(', ')} icons per chunk`);
	}
});
