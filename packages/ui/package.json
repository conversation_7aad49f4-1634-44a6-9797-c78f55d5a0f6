{"name": "@repo/ui", "version": "0.0.0", "type": "module", "scripts": {"lint": "eslint --max-warnings=0 ."}, "peerDependencies": {"class-variance-authority": "catalog:web", "next": "catalog:web"}, "dependencies": {"@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@repo/icons": "workspace:*", "clsx": "catalog:web", "cmdk": "^1.1.1", "lucide-react": "catalog:web", "next-themes": "catalog:web", "react": "catalog:web", "react-dom": "catalog:web", "swr": "catalog:web", "tailwind-merge": "catalog:web", "tailwindcss-animate": "catalog:web"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@storybook/react": "catalog:storybook", "@storybook/test": "catalog:storybook", "@storybook/types": "catalog:storybook", "@tailwindcss/postcss": "catalog:web", "@tailwindcss/typography": "catalog:web", "@testing-library/dom": "catalog:dev", "@testing-library/react": "catalog:dev", "@types/node": "catalog:node", "@types/react": "catalog:web", "@types/react-dom": "catalog:web", "eslint": "catalog:dev", "glob": "catalog:dev", "postcss": "catalog:web", "tailwindcss": "catalog:web", "typescript": "catalog:dev", "vitest": "catalog:dev"}, "exports": {"./globals.css": "./src/styles/globals.css", "./payload.css": "./src/styles/payload.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts"}}