{"name": "@repo/ui", "version": "0.0.0", "type": "module", "private": true, "scripts": {"lint": "eslint --max-warnings=0 ."}, "peerDependencies": {"class-variance-authority": "^0.7.1", "next": "^15.2.0"}, "dependencies": {"@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@repo/icons": "workspace:*", "clsx": "^2.1.1", "cmdk": "^1.1.1", "lucide-react": "^0.525.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "swr": "^2.3.3", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@storybook/react": "^8.6.11", "@storybook/test": "^8.6.14", "@storybook/types": "^8.6.11", "@tailwindcss/postcss": "^4.0.9", "@tailwindcss/typography": "^0.5.16", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@turbo/gen": "^2.4.4", "@types/node": "^22.13.8", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "eslint": "^9.20.0", "glob": "^11.0.2", "postcss": "^8.5.3", "tailwindcss": "^4.0.14", "typescript": "~5.8.3", "vitest": "^3.2.0"}, "exports": {"./globals.css": "./src/styles/globals.css", "./payload.css": "./src/styles/payload.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts"}}