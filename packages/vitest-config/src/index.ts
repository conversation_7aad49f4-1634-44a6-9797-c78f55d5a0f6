import tsconfigPaths from 'vite-tsconfig-paths';
import react from '@vitejs/plugin-react';

export const sharedConfig = {
	plugins: [tsconfigPaths(), react()],
	test: {
		globals: true,
		environment: 'jsdom',
		// Exclude Playwright test files
		exclude: [
			'**/node_modules/**',
			'**/dist/**',
			'**/.{idea,git,cache,output,temp}/**',
			'**/*.spec.ts', // Exclude Playwright spec files
			'**/tests/**/*.ts', // Exclude files in tests directories
		],
		include: ['**/*.test.{ts,tsx,js,jsx}'],
		// Other shared configuration
	},
};
