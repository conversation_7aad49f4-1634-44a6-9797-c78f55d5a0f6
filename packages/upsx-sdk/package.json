{"name": "@twc/upsx-sdk", "version": "1.0.0", "description": "TypeScript SDK for the UPSX API", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc --build", "check-types": "tsc --noEmit", "test": "vitest run", "lint": "eslint --max-warnings=0 src --ext .ts", "prepublishOnly": "pnpm test && pnpm run lint", "preversion": "pnpm test && pnpm run lint"}, "keywords": ["upsx", "profile", "api", "sdk", "typescript"], "author": "", "license": "MIT", "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@repo/vitest-config": "workspace:*", "@types/node": "catalog:node", "eslint": "catalog:dev", "typescript": "catalog:dev", "vitest": "catalog:dev"}, "files": ["dist/**/*"]}