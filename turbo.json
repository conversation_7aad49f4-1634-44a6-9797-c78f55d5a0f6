{"$schema": "https://turbo.build/schema.json", "ui": "tui", "globalEnv": ["NPM_TOKEN", "CHROMATIC_PROJECT_TOKEN"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**", "storybook-static/**", "src/assets/**", "src/components/**"], "env": ["MONGODB_URI", "MONGODB_CERT", "MONGODB_REJECT_UNAUTHORIZED", "MONGODB_USE_TLS", "LOG_LEVEL", "CRON_SECRET", "PAYLOAD_SECRET", "PREVIEW_SECRET", "S3_ACCESS_KEY_ID", "S3_BUCKET", "S3_REGION", "S3_SECRET_ACCESS_KEY", "SMTP_HOST", "SMTP_PASS", "SMTP_USER", "SUN_API_KEY", "NEXT_PUBLIC_MPARTICLE_API_KEY", "NEXT_PUBLIC_SUN_API_KEY", "NEXT_PUBLIC_SPEED_INSIGHTS_SAMPLE_RATE", "NEXT_PUBLIC_HELIOS_SCRIPT_URL", "CRON_SECRET", "VERCEL_TARGET_ENV", "KALLIOPE_AUTH_TOKEN", "WX_CONTENT_MEDIA_API_KEY", "UPSX_HMAC", "UPSX_PEM", "VERCEL_ENV", "NEXT_RUNTIME", "NEXT_PUBLIC_DEBUG_COMPONENTS", "VERCEL_PREVIEW_URL", "NEXT_PUBLIC_WX_HOST", "NEXT_PUBLIC_AMPLITUDE_EXPERIMENT_API_KEY", "NEW_RELIC_RUM_ACCOUNT_ID", "NEW_RELIC_RUM_APP_ID", "NEW_RELIC_RUM_LICENSE_KEY", "PRODUCTION_SOURCEMAPS", "VERCEL_PROJECT_PRODUCTION_URL", "EDGE_CONFIG", "EDGE_CONFIG_ID", "VERCEL_TOKEN", "DEBUG"]}, "check-types": {"dependsOn": ["^build", "^check-types"]}, "//#check-format": {}, "clean": {"cache": false, "dependsOn": ["^clean"]}, "dev": {"cache": false, "persistent": true, "dependsOn": ["^build"]}, "lint": {"dependsOn": ["^build", "^lint"]}, "publish": {"cache": false}, "test": {"dependsOn": ["^build", "^test"]}, "test:watch": {"cache": false, "persistent": true, "dependsOn": ["^build"]}}}