{"$schema": "https://turbo.build/schema.json", "ui": "tui", "globalEnv": ["NPM_TOKEN", "CHROMATIC_PROJECT_TOKEN"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*", "!**/*.stories.tsx", "!**/*.mdx"], "outputs": [".next/**", "!.next/cache/**", "dist/**", "src/assets/**", "src/components/**"], "env": ["BYPASS_SSO", "CRON_SECRET", "KALLIOPE_AUTH_TOKEN", "LOG_LEVEL", "MONGODB_CERT", "MONGODB_REJECT_UNAUTHORIZED", "MONGODB_URI", "MONGODB_USE_TLS", "NEW_RELIC_RUM_ACCOUNT_ID", "NEW_RELIC_RUM_APP_ID", "NEW_RELIC_RUM_LICENSE_KEY", "NEXT_PUBLIC_AMPLITUDE_EXPERIMENT_API_KEY", "NEXT_PUBLIC_DEBUG_COMPONENTS", "NEXT_PUBLIC_HELIOS_SCRIPT_URL", "NEXT_PUBLIC_MPARTICLE_API_KEY", "NEXT_PUBLIC_SPEED_INSIGHTS_SAMPLE_RATE", "NEXT_PUBLIC_SUN_API_KEY", "NEXT_PUBLIC_WX_HOST", "NEXT_RUNTIME", "PAYLOAD_OAUTH_CLIENT_ID", "PAYLOAD_OAUTH_IDENTITY_METADATA", "PAYLOAD_SECRET", "PREVIEW_SECRET", "PRODUCTION_SOURCEMAPS", "S3_ACCESS_KEY_ID", "S3_BUCKET", "S3_REGION", "S3_SECRET_ACCESS_KEY", "SMTP_HOST", "SMTP_PASS", "SMTP_USER", "SUN_API_KEY", "UPSX_HMAC", "UPSX_PEM", "VERCEL_AUTOMATION_BYPASS_SECRET", "VERCEL_ENV", "VERCEL_PREVIEW_URL", "VERCEL_PROJECT_PRODUCTION_URL", "VERCEL_TARGET_ENV", "WX_CONTENT_MEDIA_API_KEY", "NEXT_PUBLIC_WX_HOST", "NEXT_PUBLIC_AMPLITUDE_EXPERIMENT_API_KEY", "NEW_RELIC_RUM_ACCOUNT_ID", "NEW_RELIC_RUM_APP_ID", "NEW_RELIC_RUM_LICENSE_KEY", "PRODUCTION_SOURCEMAPS", "VERCEL_PROJECT_PRODUCTION_URL", "EDGE_CONFIG", "EDGE_CONFIG_ID", "VERCEL_TOKEN", "DEBUG"]}, "build:storybook": {}, "check-types": {"dependsOn": ["^build", "^check-types"]}, "//#check-format": {}, "clean": {"cache": false, "dependsOn": ["^clean"]}, "dev": {"cache": false, "persistent": true, "dependsOn": ["^build"]}, "lint": {"dependsOn": ["^build", "^lint"]}, "publish": {"cache": false}, "test": {"dependsOn": ["^build", "^test"]}, "test:watch": {"cache": false, "persistent": true, "dependsOn": ["^build"]}}}